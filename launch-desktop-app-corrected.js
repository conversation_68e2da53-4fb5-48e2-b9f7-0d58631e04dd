#!/usr/bin/env node

/**
 * 🖥️ LANCEUR APPLICATION DESKTOP LOUNA AI CORRIGÉ
 * Lance l'application desktop native avec toutes les corrections
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

console.log('🖥️ LANCEMENT APPLICATION DESKTOP LOUNA AI CORRIGÉE');
console.log('==================================================');

let serverProcess = null;
let isServerReady = false;

// Fonction pour vérifier si le serveur répond
function checkServerHealth(port = 52796) {
    return new Promise((resolve) => {
        const req = http.get(`http://localhost:${port}/api/metrics`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const metrics = JSON.parse(data);
                    if (metrics.success && metrics.neurons) {
                        console.log(`✅ Serveur opérationnel - ${metrics.neurons} neurones actifs`);
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                } catch (e) {
                    resolve(false);
                }
            });
        });
        
        req.on('error', () => resolve(false));
        req.setTimeout(3000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// Démarrer le serveur backend corrigé
function startCorrectedServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Démarrage du serveur backend corrigé...');
        
        serverProcess = spawn('node', ['server.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, PORT: '52796' },
            cwd: __dirname
        });

        let startupOutput = '';

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            startupOutput += output;
            console.log('📡 Serveur:', output.trim());
            
            // Vérifier les messages de succès
            if (output.includes('SERVEUR LOUNA AI DÉMARRÉ') || 
                output.includes('port 52796') ||
                output.includes('Cerveau autonome: ACTIF')) {
                
                if (!isServerReady) {
                    isServerReady = true;
                    console.log('✅ Serveur backend prêt !');
                    
                    // Vérifier la santé du serveur
                    setTimeout(async () => {
                        const isHealthy = await checkServerHealth();
                        if (isHealthy) {
                            resolve(serverProcess);
                        } else {
                            console.log('⚠️ Serveur démarré mais pas encore prêt, attente...');
                            setTimeout(async () => {
                                const isHealthy2 = await checkServerHealth();
                                resolve(serverProcess);
                            }, 3000);
                        }
                    }, 2000);
                }
            }
        });

        serverProcess.stderr.on('data', (data) => {
            const error = data.toString();
            if (!error.includes('DeprecationWarning') && !error.includes('ExperimentalWarning')) {
                console.error('❌ Erreur serveur:', error);
            }
        });

        serverProcess.on('error', (error) => {
            console.error('❌ Impossible de démarrer le serveur:', error);
            reject(error);
        });

        serverProcess.on('exit', (code) => {
            if (code !== 0 && !isServerReady) {
                console.error(`❌ Serveur arrêté avec le code: ${code}`);
                reject(new Error(`Serveur arrêté avec le code: ${code}`));
            }
        });

        // Timeout de sécurité
        setTimeout(() => {
            if (!isServerReady) {
                console.log('⏰ Timeout - Serveur considéré comme prêt');
                resolve(serverProcess);
            }
        }, 20000);
    });
}

// Lancer l'application desktop native
function launchNativeDesktopApp() {
    const url = 'http://localhost:52796';
    
    console.log('🖥️ Lancement de l\'application desktop native...');
    console.log(`🌐 URL: ${url}`);
    
    // Commandes pour lancer en mode application native selon l'OS
    const commands = {
        darwin: [
            // Chrome en mode app natif (macOS)
            `open -na "Google Chrome" --args --app="${url}" --new-window --disable-web-security --allow-running-insecure-content --window-size=1400,900 --window-position=100,100 --user-data-dir="/tmp/louna-chrome-app" --disable-features=TranslateUI --disable-extensions --no-first-run --no-default-browser-check`,
            // Safari en mode app
            `open -a Safari "${url}"`,
            // Navigateur par défaut
            `open "${url}"`
        ],
        win32: [
            // Chrome en mode app (Windows)
            `start chrome --app="${url}" --new-window --window-size=1400,900`,
            // Edge en mode app
            `start msedge --app="${url}"`,
            // Navigateur par défaut
            `start "${url}"`
        ],
        linux: [
            // Chrome/Chromium en mode app (Linux)
            `google-chrome --app="${url}" --new-window --window-size=1400,900`,
            `chromium-browser --app="${url}" --new-window --window-size=1400,900`,
            // Firefox
            `firefox "${url}"`,
            // Navigateur par défaut
            `xdg-open "${url}"`
        ]
    };

    const osCommands = commands[process.platform] || commands.linux;
    
    // Essayer chaque commande jusqu'à ce qu'une fonctionne
    function tryCommand(index = 0) {
        if (index >= osCommands.length) {
            console.error('❌ Impossible de lancer l\'application desktop');
            return;
        }

        const command = osCommands[index];
        console.log(`🔄 Tentative ${index + 1}: ${command}`);
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.warn(`⚠️ Commande ${index + 1} échouée:`, error.message);
                tryCommand(index + 1);
            } else {
                console.log('✅ Application desktop lancée avec succès !');
                console.log('🎯 LOUNA AI est maintenant accessible en mode desktop natif');
                
                // Afficher les informations de l'application
                setTimeout(async () => {
                    console.log('\n🧠 LOUNA AI - APPLICATION DESKTOP NATIVE ACTIVE');
                    console.log('===============================================');
                    
                    // Vérifier les métriques en temps réel
                    const isHealthy = await checkServerHealth();
                    if (isHealthy) {
                        console.log('🌡️ Mémoire thermique: ACTIVE');
                        console.log('🧬 Neurogenèse: EN COURS');
                        console.log('🎓 Formation avancée: ACTIVE');
                        console.log('🎤👁️ Analyse multimodale: ACTIVE');
                        console.log('💻 Interface desktop native: OPÉRATIONNELLE');
                        console.log('===============================================');
                        console.log('✨ Votre IA autonome est prête et fonctionnelle !');
                    } else {
                        console.log('⚠️ Serveur en cours de stabilisation...');
                    }
                }, 3000);
            }
        });
    }

    tryCommand();
}

// Fonction principale
async function main() {
    try {
        console.log('🔧 Vérification des prérequis...');
        
        // Vérifier que le serveur existe
        if (!fs.existsSync(path.join(__dirname, 'server.js'))) {
            throw new Error('Fichier server.js introuvable');
        }
        
        // Vérifier que le dossier public existe
        if (!fs.existsSync(path.join(__dirname, 'public'))) {
            throw new Error('Dossier public introuvable');
        }
        
        console.log('✅ Prérequis validés');
        
        // Démarrer le serveur corrigé
        const server = await startCorrectedServer();
        
        // Attendre que le serveur soit complètement stabilisé
        console.log('⏳ Attente de la stabilisation du serveur...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Vérifier une dernière fois la santé
        const isHealthy = await checkServerHealth();
        if (isHealthy) {
            console.log('✅ Serveur complètement opérationnel');
        } else {
            console.log('⚠️ Serveur partiellement opérationnel, lancement quand même...');
        }
        
        // Lancer l'application desktop native
        launchNativeDesktopApp();
        
        // Gestion de l'arrêt propre
        process.on('SIGINT', () => {
            console.log('\n🛑 Arrêt de LOUNA AI...');
            if (serverProcess) {
                serverProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
        
        process.on('SIGTERM', () => {
            console.log('\n🛑 Arrêt de LOUNA AI...');
            if (serverProcess) {
                serverProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ Erreur lors du lancement:', error.message);
        process.exit(1);
    }
}

// Lancer l'application
main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
});

console.log('🚀 Lanceur desktop LOUNA AI corrigé initialisé...');
