/**
 * 🌐 GESTIONNAIRE D'ÉTAT GLOBAL - LOUNA AI
 * Gestion centralisée de l'état de l'application
 */

class GlobalStateManager {
    constructor() {
        this.state = {
            // État de l'application
            isRunning: false,
            currentMode: 'normal',
            
            // État du serveur
            serverPort: 3005,
            serverStatus: 'stopped',
            
            // État de l'agent
            agentStatus: 'idle',
            currentAgent: null,
            
            // État de la mémoire thermique
            thermalMemoryStatus: 'inactive',
            memoryTemperature: 37,
            memoryEfficiency: 99.9,
            
            // État des accélérateurs
            acceleratorsStatus: 'inactive',
            acceleratorCount: 0,
            
            // État de l'interface
            interfaceMode: 'desktop',
            currentView: 'main',
            
            // Métriques système
            systemMetrics: {
                cpu: 0,
                memory: 0,
                temperature: 0
            },
            
            // Configuration
            config: {
                autoStart: true,
                enableAccelerators: true,
                enableThermalMemory: true
            }
        };
        
        this.listeners = new Map();
        this.history = [];
        this.maxHistorySize = 100;
    }

    /**
     * Obtenir l'état actuel
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Obtenir une valeur spécifique de l'état
     */
    get(key) {
        return this.getNestedValue(this.state, key);
    }

    /**
     * Mettre à jour l'état
     */
    setState(updates) {
        const previousState = { ...this.state };
        
        // Appliquer les mises à jour
        this.state = this.deepMerge(this.state, updates);
        
        // Ajouter à l'historique
        this.addToHistory(previousState, this.state);
        
        // Notifier les listeners
        this.notifyListeners(updates, previousState);
        
        return this.state;
    }

    /**
     * Mettre à jour une valeur spécifique
     */
    set(key, value) {
        const updates = this.createNestedUpdate(key, value);
        return this.setState(updates);
    }

    /**
     * S'abonner aux changements d'état
     */
    subscribe(listener, filter = null) {
        const id = Date.now() + Math.random();
        this.listeners.set(id, { listener, filter });
        
        // Retourner une fonction de désabonnement
        return () => this.listeners.delete(id);
    }

    /**
     * Réinitialiser l'état
     */
    reset() {
        const defaultState = new GlobalStateManager().state;
        this.setState(defaultState);
    }

    /**
     * Obtenir l'historique des changements
     */
    getHistory() {
        return [...this.history];
    }

    /**
     * Méthodes utilitaires privées
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    createNestedUpdate(path, value) {
        const keys = path.split('.');
        const result = {};
        let current = result;
        
        for (let i = 0; i < keys.length - 1; i++) {
            current[keys[i]] = {};
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        return result;
    }

    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    addToHistory(previousState, newState) {
        this.history.push({
            timestamp: new Date().toISOString(),
            previous: previousState,
            current: newState
        });
        
        // Limiter la taille de l'historique
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    notifyListeners(updates, previousState) {
        this.listeners.forEach(({ listener, filter }) => {
            try {
                if (!filter || this.matchesFilter(updates, filter)) {
                    listener(this.state, updates, previousState);
                }
            } catch (error) {
                console.error('Erreur dans le listener d\'état:', error);
            }
        });
    }

    matchesFilter(updates, filter) {
        if (typeof filter === 'string') {
            return updates.hasOwnProperty(filter);
        }
        if (Array.isArray(filter)) {
            return filter.some(key => updates.hasOwnProperty(key));
        }
        if (typeof filter === 'function') {
            return filter(updates);
        }
        return true;
    }

    /**
     * Méthodes de convenance pour les états courants
     */
    isServerRunning() {
        return this.get('serverStatus') === 'running';
    }

    isAgentActive() {
        return this.get('agentStatus') === 'active';
    }

    isThermalMemoryActive() {
        return this.get('thermalMemoryStatus') === 'active';
    }

    areAcceleratorsActive() {
        return this.get('acceleratorsStatus') === 'active';
    }

    /**
     * Actions prédéfinies
     */
    startServer() {
        this.setState({
            isRunning: true,
            serverStatus: 'running'
        });
    }

    stopServer() {
        this.setState({
            isRunning: false,
            serverStatus: 'stopped',
            agentStatus: 'idle'
        });
    }

    activateAgent(agentName) {
        this.setState({
            agentStatus: 'active',
            currentAgent: agentName
        });
    }

    deactivateAgent() {
        this.setState({
            agentStatus: 'idle',
            currentAgent: null
        });
    }

    updateSystemMetrics(metrics) {
        this.setState({
            systemMetrics: { ...this.get('systemMetrics'), ...metrics }
        });
    }
}

// Instance globale
const globalStateManager = new GlobalStateManager();

module.exports = globalStateManager;
