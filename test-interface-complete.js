/**
 * 🧪 TEST COMPLET DE L'INTERFACE CORRIGÉE
 * Vérification que tout fonctionne : chat, réflexions, boutons
 */

const axios = require('axios');

class InterfaceCompleteTest {
    constructor() {
        this.baseURL = 'http://localhost:52796';
        this.testResults = [];
    }

    async runCompleteTest() {
        console.log('🧪 TEST COMPLET DE L\'INTERFACE CORRIGÉE');
        console.log('=' .repeat(60));

        try {
            // Test 1: API Chat
            await this.testChatAPI();

            // Test 2: API Réflexions
            await this.testReflectionAPI();

            // Test 3: API Métriques
            await this.testMetricsAPI();

            // Test 4: Interface accessible
            await this.testInterfaceAccess();

            // Test 5: Réflexions en temps réel
            await this.testLiveReflections();

            // Résultats
            this.displayResults();

        } catch (error) {
            console.error('❌ Erreur test complet:', error);
        }
    }

    async testChatAPI() {
        console.log('\n💬 Test API Chat...');
        
        try {
            const response = await axios.post(`${this.baseURL}/api/chat`, {
                message: 'Test interface corrigée'
            });

            if (response.data.success && response.data.response) {
                this.addResult(true, 'API Chat', `Réponse reçue: ${response.data.response.substring(0, 50)}...`);
                
                // Vérifier les métriques dans la réponse
                if (response.data.metrics && response.data.metrics.brainStats) {
                    const neurons = response.data.metrics.brainStats.activeNeurons;
                    this.addResult(true, 'Métriques Chat', `${neurons} neurones actifs`);
                }
            } else {
                this.addResult(false, 'API Chat', 'Réponse invalide');
            }
        } catch (error) {
            this.addResult(false, 'API Chat', error.message);
        }
    }

    async testReflectionAPI() {
        console.log('\n🧠 Test API Réflexions...');
        
        try {
            // Test démarrage
            const startResponse = await axios.get(`${this.baseURL}/api/reflection/start`);
            if (startResponse.data.success) {
                this.addResult(true, 'Démarrage Réflexions', 'Réflexions démarrées');
            } else {
                this.addResult(false, 'Démarrage Réflexions', 'Échec démarrage');
            }

            // Attendre un peu pour les réflexions
            await new Promise(resolve => setTimeout(resolve, 4000));

            // Test récupération pensées
            const thoughtsResponse = await axios.get(`${this.baseURL}/api/reflection/thoughts?count=3`);
            if (thoughtsResponse.data.success && thoughtsResponse.data.thoughts) {
                const thoughts = thoughtsResponse.data.thoughts;
                this.addResult(true, 'Récupération Pensées', `${thoughts.length} pensées récupérées`);
                
                if (thoughts.length > 0) {
                    const latestThought = thoughts[0];
                    this.addResult(true, 'Contenu Pensée', `"${latestThought.content.substring(0, 50)}..."`);
                }
            } else {
                this.addResult(false, 'Récupération Pensées', 'Aucune pensée trouvée');
            }

            // Test état
            const stateResponse = await axios.get(`${this.baseURL}/api/reflection/state`);
            if (stateResponse.data.success) {
                const state = stateResponse.data.reflection;
                this.addResult(true, 'État Réflexions', `Actif: ${state.isActive}`);
            }

        } catch (error) {
            this.addResult(false, 'API Réflexions', error.message);
        }
    }

    async testMetricsAPI() {
        console.log('\n📊 Test API Métriques...');
        
        try {
            const response = await axios.get(`${this.baseURL}/api/metrics`);
            
            if (response.data.success || response.data.brainStats) {
                const data = response.data;
                
                // Vérifier neurones
                const neurons = data.brainStats?.activeNeurons || data.neurons;
                if (neurons && neurons > 0) {
                    this.addResult(true, 'Neurones', `${neurons} neurones actifs`);
                } else {
                    this.addResult(false, 'Neurones', 'Aucun neurone détecté');
                }

                // Vérifier température
                const temp = data.thermalStats?.temperature || data.temperature;
                if (temp && temp > 0) {
                    this.addResult(true, 'Température', `${temp.toFixed(1)}°C`);
                } else {
                    this.addResult(false, 'Température', 'Température invalide');
                }

                // Vérifier mémoire
                const entries = data.thermalStats?.totalEntries || data.entries;
                if (entries && entries > 0) {
                    this.addResult(true, 'Mémoire', `${entries} entrées`);
                } else {
                    this.addResult(false, 'Mémoire', 'Aucune entrée mémoire');
                }

                // Vérifier QI
                const qi = data.iqStats?.combined || data.qi?.combined;
                if (qi && qi > 0) {
                    this.addResult(true, 'QI', `QI ${qi}`);
                } else {
                    this.addResult(false, 'QI', 'QI non disponible');
                }

            } else {
                this.addResult(false, 'API Métriques', 'Réponse invalide');
            }
        } catch (error) {
            this.addResult(false, 'API Métriques', error.message);
        }
    }

    async testInterfaceAccess() {
        console.log('\n🌐 Test Accès Interface...');
        
        try {
            // Test page principale
            const mainResponse = await axios.get(`${this.baseURL}/`);
            if (mainResponse.status === 200 && mainResponse.data.includes('LOUNA AI')) {
                this.addResult(true, 'Interface Principale', 'Page accessible');
            } else {
                this.addResult(false, 'Interface Principale', 'Page non accessible');
            }

            // Test page validation
            const validationResponse = await axios.get(`${this.baseURL}/validation`);
            if (validationResponse.status === 200) {
                this.addResult(true, 'Page Validation', 'Page accessible');
            } else {
                this.addResult(false, 'Page Validation', 'Page non accessible');
            }

            // Test page test chat
            const testChatResponse = await axios.get(`${this.baseURL}/test-chat`);
            if (testChatResponse.status === 200) {
                this.addResult(true, 'Page Test Chat', 'Page accessible');
            } else {
                this.addResult(false, 'Page Test Chat', 'Page non accessible');
            }

        } catch (error) {
            this.addResult(false, 'Accès Interface', error.message);
        }
    }

    async testLiveReflections() {
        console.log('\n⚡ Test Réflexions en Temps Réel...');
        
        try {
            // Prendre un snapshot initial
            const initialResponse = await axios.get(`${this.baseURL}/api/reflection/thoughts?count=1`);
            const initialThought = initialResponse.data.thoughts?.[0];

            // Attendre 6 secondes
            await new Promise(resolve => setTimeout(resolve, 6000));

            // Prendre un nouveau snapshot
            const newResponse = await axios.get(`${this.baseURL}/api/reflection/thoughts?count=1`);
            const newThought = newResponse.data.thoughts?.[0];

            if (initialThought && newThought && initialThought.id !== newThought.id) {
                this.addResult(true, 'Réflexions Temps Réel', 'Nouvelles pensées générées');
                this.addResult(true, 'Évolution Pensées', `"${newThought.content.substring(0, 40)}..."`);
            } else if (newThought) {
                this.addResult(true, 'Réflexions Actives', 'Pensées présentes');
            } else {
                this.addResult(false, 'Réflexions Temps Réel', 'Aucune nouvelle pensée');
            }

        } catch (error) {
            this.addResult(false, 'Réflexions Temps Réel', error.message);
        }
    }

    addResult(passed, testName, details) {
        const result = { passed, testName, details };
        this.testResults.push(result);
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${details}`);
    }

    displayResults() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 RÉSULTATS DU TEST COMPLET');
        console.log('=' .repeat(60));
        
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const successRate = (passed / total) * 100;
        
        console.log(`✅ Tests réussis: ${passed}`);
        console.log(`❌ Tests échoués: ${total - passed}`);
        console.log(`📊 Taux de réussite: ${successRate.toFixed(1)}%`);
        
        if (successRate >= 90) {
            console.log('\n🎉 EXCELLENT ! Interface complètement fonctionnelle !');
            console.log('🚀 Chat, réflexions, boutons - tout fonctionne parfaitement !');
        } else if (successRate >= 75) {
            console.log('\n👍 BON ! Interface majoritairement fonctionnelle.');
            console.log('🔧 Quelques ajustements mineurs nécessaires.');
        } else {
            console.log('\n⚠️ Interface partiellement fonctionnelle.');
            console.log('🔧 Corrections supplémentaires nécessaires.');
        }

        // Afficher les échecs
        const failures = this.testResults.filter(r => !r.passed);
        if (failures.length > 0) {
            console.log('\n❌ Tests échoués:');
            failures.forEach(f => {
                console.log(`   - ${f.testName}: ${f.details}`);
            });
        }
    }
}

// Exécuter le test
async function runTest() {
    const tester = new InterfaceCompleteTest();
    await tester.runCompleteTest();
}

if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = InterfaceCompleteTest;
