<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Interface Chat LOUNA AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: #3498db;
            margin-left: auto;
            text-align: right;
        }

        .agent-message {
            background: #2ecc71;
            margin-right: auto;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        #messageInput {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }

        #sendButton {
            padding: 12px 24px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s;
        }

        #sendButton:hover {
            background: #c0392b;
        }

        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 8px 16px;
            background: #9b59b6;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .test-btn:hover {
            background: #8e44ad;
        }

        .loading {
            color: #f39c12;
            font-style: italic;
        }

        .error {
            color: #e74c3c;
            font-weight: bold;
        }

        .success {
            color: #2ecc71;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Interface Chat LOUNA AI</h1>
            <p>Test de l'interface de chat avec API réelle</p>
        </div>

        <div class="status" id="status">
            🔄 Initialisation du test...
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testAPI()">🔧 Test API Direct</button>
            <button class="test-btn" onclick="testQuickMessage()">⚡ Test Message Rapide</button>
            <button class="test-btn" onclick="clearChat()">🗑️ Vider Chat</button>
            <button class="test-btn" onclick="testMetrics()">📊 Test Métriques</button>
        </div>

        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message agent-message">
                    🤖 Interface de test prête ! Tapez un message pour tester la communication avec LOUNA AI.
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Tapez votre message de test ici..." />
                <button id="sendButton">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let messageCount = 0;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Test interface chargée');
            updateStatus('✅ Interface de test prête', 'success');
            
            // Événements
            document.getElementById('sendButton').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });

        // Envoyer message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                updateStatus('⚠️ Veuillez taper un message', 'error');
                return;
            }

            // Ajouter message utilisateur
            addMessage('user', message);
            input.value = '';
            messageCount++;

            // Indicateur de chargement
            updateStatus('🔄 Envoi vers LOUNA AI...', 'loading');
            const loadingId = addMessage('agent', '💭 LOUNA réfléchit...', true);

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                
                // Supprimer indicateur de chargement
                removeMessage(loadingId);

                if (data.success && data.response) {
                    addMessage('agent', data.response);
                    updateStatus(`✅ Message ${messageCount} envoyé avec succès`, 'success');
                } else {
                    addMessage('agent', '❌ Erreur: ' + (data.error || 'Réponse invalide'));
                    updateStatus('❌ Erreur dans la réponse', 'error');
                }
            } catch (error) {
                removeMessage(loadingId);
                addMessage('agent', '❌ Erreur de connexion: ' + error.message);
                updateStatus('❌ Erreur de connexion', 'error');
                console.error('Erreur:', error);
            }
        }

        // Ajouter message
        function addMessage(sender, text, isTemporary = false) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            messageDiv.id = messageId;
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            
            if (isTemporary) {
                messageDiv.style.opacity = '0.7';
                messageDiv.style.fontStyle = 'italic';
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
            
            return messageId;
        }

        // Supprimer message
        function removeMessage(messageId) {
            const messageEl = document.getElementById(messageId);
            if (messageEl && messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }

        // Mettre à jour statut
        function updateStatus(text, type = '') {
            const status = document.getElementById('status');
            status.textContent = text;
            status.className = 'status ' + type;
        }

        // Test API direct
        async function testAPI() {
            updateStatus('🔧 Test API direct...', 'loading');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Test API direct' })
                });

                const data = await response.json();
                
                if (data.success) {
                    updateStatus('✅ API fonctionne correctement', 'success');
                    addMessage('agent', '🔧 Test API: ' + data.response);
                } else {
                    updateStatus('❌ API retourne une erreur', 'error');
                }
            } catch (error) {
                updateStatus('❌ Erreur API: ' + error.message, 'error');
            }
        }

        // Test message rapide
        function testQuickMessage() {
            const testMessages = [
                'Bonjour LOUNA',
                'Comment ça va ?',
                'Combien de neurones as-tu ?',
                'Quelle est ta température ?'
            ];
            
            const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
            document.getElementById('messageInput').value = randomMessage;
            sendMessage();
        }

        // Vider chat
        function clearChat() {
            const messages = document.getElementById('messages');
            messages.innerHTML = '<div class="message agent-message">🗑️ Chat vidé. Prêt pour de nouveaux tests !</div>';
            messageCount = 0;
            updateStatus('🗑️ Chat vidé', 'success');
        }

        // Test métriques
        async function testMetrics() {
            updateStatus('📊 Test métriques...', 'loading');
            
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success || data.brainStats) {
                    const neurons = data.brainStats?.activeNeurons || data.neurons || 'N/A';
                    const temp = data.thermalStats?.temperature || data.temperature || 'N/A';
                    const qi = data.iqStats?.combined || data.qi?.combined || 'N/A';
                    
                    addMessage('agent', `📊 Métriques: ${neurons} neurones, ${temp}°C, QI ${qi}`);
                    updateStatus('✅ Métriques récupérées', 'success');
                } else {
                    updateStatus('❌ Erreur métriques', 'error');
                }
            } catch (error) {
                updateStatus('❌ Erreur métriques: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
