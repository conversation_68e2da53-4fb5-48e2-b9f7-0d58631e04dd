/**
 * 🧠 MODULE DE COMPRÉHENSION AVANCÉE POUR LOUNA AI
 * Analyse sémantique, compréhension contextuelle, raisonnement logique
 */

class AdvancedComprehension {
    constructor() {
        this.knowledgeBase = new Map();
        this.contextHistory = [];
        this.reasoningPatterns = new Map();
        this.semanticNetwork = new Map();
        this.learningMemory = new Map();
        
        console.log('🧠 Initialisation du module de compréhension avancée...');
        this.initializeKnowledgeBase();
        this.startContinuousLearning();
    }

    /**
     * 📚 Initialise la base de connaissances
     */
    initializeKnowledgeBase() {
        // Concepts de programmation
        this.knowledgeBase.set('programming', {
            languages: ['JavaScript', 'Python', 'Java', 'C++', 'Go', 'Rust'],
            paradigms: ['OOP', 'Functional', 'Procedural', 'Reactive'],
            concepts: ['Variables', 'Functions', 'Classes', 'Modules', 'APIs'],
            patterns: ['Singleton', 'Observer', 'Factory', 'Strategy', 'MVC']
        });

        // Concepts d'IA
        this.knowledgeBase.set('ai', {
            types: ['Machine Learning', 'Deep Learning', 'NLP', 'Computer Vision'],
            algorithms: ['Neural Networks', 'Decision Trees', 'SVM', 'Clustering'],
            applications: ['Chatbots', 'Image Recognition', 'Prediction', 'Automation']
        });

        // Concepts généraux
        this.knowledgeBase.set('general', {
            sciences: ['Physique', 'Chimie', 'Biologie', 'Mathématiques'],
            technologies: ['Internet', 'Cloud', 'Blockchain', 'IoT'],
            domains: ['Santé', 'Finance', 'Éducation', 'Divertissement']
        });

        console.log('📚 Base de connaissances initialisée');
    }

    /**
     * 🔍 Analyse sémantique avancée
     */
    analyzeSemantics(text) {
        const analysis = {
            timestamp: Date.now(),
            text: text,
            tokens: this.tokenize(text),
            entities: this.extractEntities(text),
            concepts: this.identifyConcepts(text),
            sentiment: this.analyzeSentiment(text),
            intent: this.detectIntent(text),
            complexity: this.assessComplexity(text),
            topics: this.extractTopics(text),
            relationships: this.findRelationships(text)
        };

        // Stocker dans la mémoire thermique
        if (global.thermalMemory) {
            global.thermalMemory.add(
                'semantic_analysis',
                `Analyse: "${text.substring(0, 100)}..." - Intent: ${analysis.intent}, Sentiment: ${analysis.sentiment.dominant}`,
                0.8,
                'comprehension'
            );
        }

        console.log('🔍 Analyse sémantique:', analysis);
        return analysis;
    }

    /**
     * 🔤 Tokenisation avancée
     */
    tokenize(text) {
        // Tokenisation avec reconnaissance d'entités
        const tokens = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(token => token.length > 0);

        return tokens.map(token => ({
            word: token,
            type: this.classifyToken(token),
            importance: this.calculateTokenImportance(token),
            frequency: this.getTokenFrequency(token)
        }));
    }

    /**
     * 🏷️ Classification de tokens
     */
    classifyToken(token) {
        const programmingKeywords = ['function', 'class', 'variable', 'array', 'object', 'method'];
        const aiKeywords = ['neural', 'network', 'learning', 'algorithm', 'model', 'training'];
        const actionWords = ['create', 'build', 'develop', 'implement', 'design', 'optimize'];

        if (programmingKeywords.includes(token)) return 'programming';
        if (aiKeywords.includes(token)) return 'ai';
        if (actionWords.includes(token)) return 'action';
        if (token.length > 6) return 'complex';
        return 'general';
    }

    /**
     * 🎯 Extraction d'entités
     */
    extractEntities(text) {
        const entities = {
            persons: this.extractPersons(text),
            technologies: this.extractTechnologies(text),
            concepts: this.extractConceptEntities(text),
            numbers: this.extractNumbers(text),
            dates: this.extractDates(text)
        };

        return entities;
    }

    extractPersons(text) {
        const personPatterns = ['Jean-Luc', 'LOUNA', 'utilisateur', 'développeur'];
        return personPatterns.filter(person => 
            text.toLowerCase().includes(person.toLowerCase())
        );
    }

    extractTechnologies(text) {
        const techPatterns = ['JavaScript', 'Python', 'React', 'Node.js', 'AI', 'ML'];
        return techPatterns.filter(tech => 
            text.toLowerCase().includes(tech.toLowerCase())
        );
    }

    extractConceptEntities(text) {
        const concepts = [];
        for (const [category, data] of this.knowledgeBase) {
            for (const key in data) {
                data[key].forEach(item => {
                    if (text.toLowerCase().includes(item.toLowerCase())) {
                        concepts.push({ category, type: key, concept: item });
                    }
                });
            }
        }
        return concepts;
    }

    extractNumbers(text) {
        const numberPattern = /\b\d+(?:\.\d+)?\b/g;
        return text.match(numberPattern) || [];
    }

    extractDates(text) {
        const datePatterns = [
            /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,
            /\b\d{4}-\d{2}-\d{2}\b/g,
            /\b(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\b/gi
        ];
        
        const dates = [];
        datePatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) dates.push(...matches);
        });
        
        return dates;
    }

    /**
     * 💭 Identification de concepts
     */
    identifyConcepts(text) {
        const concepts = new Map();
        
        // Analyser chaque catégorie de la base de connaissances
        for (const [category, data] of this.knowledgeBase) {
            let categoryScore = 0;
            const foundConcepts = [];
            
            for (const key in data) {
                data[key].forEach(concept => {
                    if (text.toLowerCase().includes(concept.toLowerCase())) {
                        foundConcepts.push(concept);
                        categoryScore += 1;
                    }
                });
            }
            
            if (categoryScore > 0) {
                concepts.set(category, {
                    score: categoryScore,
                    concepts: foundConcepts,
                    relevance: categoryScore / data.length || 0
                });
            }
        }
        
        return Object.fromEntries(concepts);
    }

    /**
     * 😊 Analyse de sentiment
     */
    analyzeSentiment(text) {
        const positiveWords = ['bon', 'excellent', 'parfait', 'génial', 'super', 'formidable', 'merci'];
        const negativeWords = ['mauvais', 'terrible', 'problème', 'erreur', 'bug', 'cassé'];
        const neutralWords = ['peut-être', 'probablement', 'possible', 'normal'];

        let positiveScore = 0;
        let negativeScore = 0;
        let neutralScore = 0;

        const words = text.toLowerCase().split(/\s+/);
        
        words.forEach(word => {
            if (positiveWords.some(pw => word.includes(pw))) positiveScore++;
            if (negativeWords.some(nw => word.includes(nw))) negativeScore++;
            if (neutralWords.some(neu => word.includes(neu))) neutralScore++;
        });

        const total = positiveScore + negativeScore + neutralScore || 1;
        
        return {
            positive: (positiveScore / total * 100).toFixed(1),
            negative: (negativeScore / total * 100).toFixed(1),
            neutral: (neutralScore / total * 100).toFixed(1),
            dominant: positiveScore > negativeScore ? 
                (positiveScore > neutralScore ? 'positive' : 'neutral') :
                (negativeScore > neutralScore ? 'negative' : 'neutral'),
            confidence: Math.max(positiveScore, negativeScore, neutralScore) / total * 100
        };
    }

    /**
     * 🎯 Détection d'intention
     */
    detectIntent(text) {
        const intentPatterns = {
            question: ['comment', 'pourquoi', 'quand', 'où', 'qui', 'quoi', '?'],
            request: ['peux-tu', 'pourrais-tu', 'aide-moi', 'fais', 'crée'],
            information: ['explique', 'dis-moi', 'montre', 'décris'],
            command: ['lance', 'démarre', 'arrête', 'ferme', 'ouvre'],
            greeting: ['bonjour', 'salut', 'hello', 'bonsoir'],
            gratitude: ['merci', 'thanks', 'remercie']
        };

        const textLower = text.toLowerCase();
        const detectedIntents = [];

        for (const [intent, patterns] of Object.entries(intentPatterns)) {
            const matches = patterns.filter(pattern => textLower.includes(pattern));
            if (matches.length > 0) {
                detectedIntents.push({
                    intent,
                    confidence: (matches.length / patterns.length * 100).toFixed(1),
                    triggers: matches
                });
            }
        }

        return detectedIntents.length > 0 ? 
            detectedIntents.reduce((max, current) => 
                parseFloat(current.confidence) > parseFloat(max.confidence) ? current : max
            ).intent : 'unknown';
    }

    /**
     * 📊 Évaluation de complexité
     */
    assessComplexity(text) {
        const factors = {
            length: text.length,
            words: text.split(/\s+/).length,
            sentences: text.split(/[.!?]+/).length,
            avgWordLength: text.split(/\s+/).reduce((sum, word) => sum + word.length, 0) / text.split(/\s+/).length,
            technicalTerms: this.countTechnicalTerms(text),
            punctuation: (text.match(/[.,;:!?]/g) || []).length
        };

        const complexityScore = (
            Math.min(factors.length / 1000, 1) * 20 +
            Math.min(factors.avgWordLength / 10, 1) * 30 +
            Math.min(factors.technicalTerms / 10, 1) * 40 +
            Math.min(factors.punctuation / factors.sentences, 1) * 10
        );

        return {
            score: complexityScore.toFixed(1),
            level: complexityScore < 30 ? 'simple' : 
                   complexityScore < 60 ? 'moyen' : 'complexe',
            factors: factors
        };
    }

    countTechnicalTerms(text) {
        const technicalTerms = [
            'algorithm', 'function', 'variable', 'class', 'method', 'api',
            'database', 'server', 'client', 'framework', 'library', 'module'
        ];
        
        return technicalTerms.filter(term => 
            text.toLowerCase().includes(term)
        ).length;
    }

    /**
     * 🏷️ Extraction de sujets
     */
    extractTopics(text) {
        const topics = new Map();
        
        // Analyser les mots-clés par fréquence
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        words.forEach(word => {
            topics.set(word, (topics.get(word) || 0) + 1);
        });

        // Retourner les top 5 sujets
        return Array.from(topics.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([topic, frequency]) => ({ topic, frequency }));
    }

    /**
     * 🔗 Recherche de relations
     */
    findRelationships(text) {
        const relationships = [];
        const entities = this.extractEntities(text);
        
        // Relations entre technologies
        if (entities.technologies.length > 1) {
            relationships.push({
                type: 'technology_stack',
                entities: entities.technologies,
                relationship: 'used_together'
            });
        }

        // Relations entre concepts
        const concepts = this.identifyConcepts(text);
        if (Object.keys(concepts).length > 1) {
            relationships.push({
                type: 'concept_relation',
                entities: Object.keys(concepts),
                relationship: 'related_domains'
            });
        }

        return relationships;
    }

    /**
     * 🧠 Raisonnement contextuel
     */
    reasonWithContext(text, context = []) {
        const currentAnalysis = this.analyzeSemantics(text);
        
        // Ajouter au contexte
        this.contextHistory.push({
            text,
            analysis: currentAnalysis,
            timestamp: Date.now()
        });

        // Garder seulement les 10 derniers contextes
        if (this.contextHistory.length > 10) {
            this.contextHistory.shift();
        }

        // Analyser les patterns dans le contexte
        const contextPatterns = this.analyzeContextPatterns();
        
        return {
            currentAnalysis,
            contextPatterns,
            reasoning: this.generateReasoning(currentAnalysis, contextPatterns)
        };
    }

    analyzeContextPatterns() {
        if (this.contextHistory.length < 2) return null;

        const patterns = {
            topicContinuity: this.analyzeTopicContinuity(),
            sentimentEvolution: this.analyzeSentimentEvolution(),
            complexityTrend: this.analyzeComplexityTrend()
        };

        return patterns;
    }

    analyzeTopicContinuity() {
        const recentTopics = this.contextHistory.slice(-3)
            .map(ctx => ctx.analysis.topics)
            .flat();
        
        const topicFreq = new Map();
        recentTopics.forEach(topicObj => {
            const topic = topicObj.topic;
            topicFreq.set(topic, (topicFreq.get(topic) || 0) + 1);
        });

        return Array.from(topicFreq.entries())
            .filter(([topic, freq]) => freq > 1)
            .map(([topic, freq]) => ({ topic, frequency: freq }));
    }

    analyzeSentimentEvolution() {
        const sentiments = this.contextHistory.slice(-5)
            .map(ctx => ctx.analysis.sentiment.dominant);
        
        return {
            current: sentiments[sentiments.length - 1],
            trend: this.calculateSentimentTrend(sentiments),
            stability: this.calculateSentimentStability(sentiments)
        };
    }

    calculateSentimentTrend(sentiments) {
        if (sentiments.length < 2) return 'stable';
        
        const last = sentiments[sentiments.length - 1];
        const previous = sentiments[sentiments.length - 2];
        
        if (last === 'positive' && previous !== 'positive') return 'improving';
        if (last === 'negative' && previous !== 'negative') return 'declining';
        return 'stable';
    }

    calculateSentimentStability(sentiments) {
        const unique = new Set(sentiments);
        return unique.size === 1 ? 'very_stable' : 
               unique.size === 2 ? 'stable' : 'variable';
    }

    analyzeComplexityTrend() {
        const complexities = this.contextHistory.slice(-3)
            .map(ctx => parseFloat(ctx.analysis.complexity.score));
        
        if (complexities.length < 2) return 'insufficient_data';
        
        const trend = complexities[complexities.length - 1] - complexities[0];
        return trend > 10 ? 'increasing' : 
               trend < -10 ? 'decreasing' : 'stable';
    }

    generateReasoning(analysis, patterns) {
        const reasoning = [];
        
        // Raisonnement basé sur l'intention
        if (analysis.intent === 'question') {
            reasoning.push('L\'utilisateur cherche des informations ou de l\'aide');
        }
        
        // Raisonnement basé sur le sentiment
        if (analysis.sentiment.dominant === 'negative') {
            reasoning.push('L\'utilisateur semble rencontrer des difficultés');
        }
        
        // Raisonnement basé sur la complexité
        if (analysis.complexity.level === 'complexe') {
            reasoning.push('Le sujet abordé nécessite une expertise technique');
        }
        
        // Raisonnement contextuel
        if (patterns && patterns.topicContinuity.length > 0) {
            reasoning.push(`Continuité détectée sur: ${patterns.topicContinuity.map(t => t.topic).join(', ')}`);
        }
        
        return reasoning;
    }

    /**
     * 🎓 Apprentissage continu
     */
    startContinuousLearning() {
        setInterval(() => {
            this.updateKnowledgeBase();
        }, 60000); // Toutes les minutes

        console.log('🎓 Apprentissage continu démarré');
    }

    updateKnowledgeBase() {
        // Analyser les patterns récents pour enrichir la base de connaissances
        if (this.contextHistory.length > 5) {
            const recentConcepts = this.contextHistory.slice(-5)
                .map(ctx => ctx.analysis.concepts)
                .reduce((acc, concepts) => {
                    Object.keys(concepts).forEach(key => {
                        acc.add(key);
                    });
                    return acc;
                }, new Set());

            console.log('📚 Base de connaissances mise à jour avec:', Array.from(recentConcepts));
        }
    }

    calculateTokenImportance(token) {
        // Importance basée sur la longueur et la rareté
        return Math.min(100, token.length * 10 + (this.isRareToken(token) ? 50 : 0));
    }

    isRareToken(token) {
        const commonWords = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'];
        return !commonWords.includes(token);
    }

    getTokenFrequency(token) {
        // Simulation de fréquence
        return Math.floor(Math.random() * 100);
    }

    /**
     * 📊 Obtient les statistiques de compréhension
     */
    getComprehensionStats() {
        return {
            knowledgeBaseSize: this.knowledgeBase.size,
            contextHistoryLength: this.contextHistory.length,
            reasoningPatternsCount: this.reasoningPatterns.size,
            semanticNetworkSize: this.semanticNetwork.size,
            learningMemorySize: this.learningMemory.size,
            recentTopics: this.contextHistory.slice(-3).map(ctx => ctx.analysis.topics).flat()
        };
    }
}

module.exports = AdvancedComprehension;
