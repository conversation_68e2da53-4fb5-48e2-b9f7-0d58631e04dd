const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

/**
 * 🌡️ CAPTEUR DE TEMPÉRATURE CPU RÉELLE
 * Lit la vraie température du processeur pour la mémoire thermique
 */
class CPUTemperatureSensor {
    constructor() {
        this.currentTemperature = 37.0; // Température de base
        this.temperatureHistory = [];
        this.isMonitoring = false;
        this.platform = process.platform;
        this.lastReading = Date.now();
        
        // 🎯 CURSEUR DE RÉGULATION AUTOMATIQUE
        this.temperatureCursor = {
            position: 37.0,
            target: 37.0,
            speed: 0.1,
            range: { min: 30.0, max: 85.0 },
            autoRegulation: true,
            sensitivity: 0.5
        };
        
        this.startMonitoring();
    }

    /**
     * 🚀 Démarre la surveillance de température
     */
    async startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        console.log('🌡️ Démarrage surveillance température CPU réelle...');
        
        // Lecture initiale
        await this.readCPUTemperature();
        
        // Surveillance continue toutes les 2 secondes
        setInterval(async () => {
            await this.readCPUTemperature();
            this.updateTemperatureCursor();
        }, 2000);
        
        // Régulation automatique toutes les 500ms
        setInterval(() => {
            this.autoRegulateTemperature();
        }, 500);
    }

    /**
     * 📊 Lit la température CPU selon la plateforme
     */
    async readCPUTemperature() {
        try {
            let temperature = 37.0;
            
            switch (this.platform) {
                case 'darwin': // macOS
                    temperature = await this.readMacOSTemperature();
                    break;
                case 'linux':
                    temperature = await this.readLinuxTemperature();
                    break;
                case 'win32':
                    temperature = await this.readWindowsTemperature();
                    break;
                default:
                    temperature = this.simulateTemperature();
            }
            
            // Valider la température
            if (temperature && temperature > 0 && temperature < 150) {
                this.currentTemperature = temperature;
                this.addToHistory(temperature);
                this.lastReading = Date.now();
                
                // Mettre à jour la mémoire thermique globale
                if (global.thermalMemory && global.thermalMemory.updateTemperature) {
                    global.thermalMemory.updateTemperature(temperature);
                }
            }
            
        } catch (error) {
            console.warn('⚠️ Erreur lecture température CPU, utilisation simulation:', error.message);
            this.currentTemperature = this.simulateTemperature();
        }
    }

    /**
     * 🍎 Lit la température sur macOS
     */
    async readMacOSTemperature() {
        try {
            // Essayer powermetrics (plus précis)
            const { stdout } = await execAsync('sudo powermetrics -n 1 -s cpu_power | grep "CPU die temperature"');
            const match = stdout.match(/(\d+\.\d+)/);
            if (match) {
                return parseFloat(match[1]);
            }
        } catch (error) {
            // Fallback: utiliser istats ou simulation
            try {
                const { stdout } = await execAsync('istats cpu temp');
                const match = stdout.match(/(\d+\.\d+)°C/);
                if (match) {
                    return parseFloat(match[1]);
                }
            } catch (fallbackError) {
                // Simulation basée sur l'activité système
                return this.simulateTemperature();
            }
        }
        return this.simulateTemperature();
    }

    /**
     * 🐧 Lit la température sur Linux
     */
    async readLinuxTemperature() {
        try {
            // Lire depuis /sys/class/thermal/
            const { stdout } = await execAsync('cat /sys/class/thermal/thermal_zone*/temp 2>/dev/null | head -1');
            const temp = parseInt(stdout.trim());
            if (temp > 1000) {
                return temp / 1000; // Convertir de millidegrés
            }
            return temp;
        } catch (error) {
            // Fallback: lm-sensors
            try {
                const { stdout } = await execAsync('sensors | grep "Core 0" | head -1');
                const match = stdout.match(/\+(\d+\.\d+)°C/);
                if (match) {
                    return parseFloat(match[1]);
                }
            } catch (fallbackError) {
                return this.simulateTemperature();
            }
        }
        return this.simulateTemperature();
    }

    /**
     * 🪟 Lit la température sur Windows
     */
    async readWindowsTemperature() {
        try {
            // Utiliser wmic
            const { stdout } = await execAsync('wmic /namespace:\\\\root\\wmi PATH MSAcpi_ThermalZoneTemperature get CurrentTemperature');
            const lines = stdout.split('\n');
            for (const line of lines) {
                const temp = parseInt(line.trim());
                if (temp > 0) {
                    return (temp / 10) - 273.15; // Convertir de Kelvin * 10
                }
            }
        } catch (error) {
            return this.simulateTemperature();
        }
        return this.simulateTemperature();
    }

    /**
     * 🎲 Simule une température réaliste basée sur l'activité
     */
    simulateTemperature() {
        const baseTemp = 45.0; // Température de base CPU
        const variation = Math.sin(Date.now() / 10000) * 5.0; // Variation naturelle
        const load = Math.random() * 15.0; // Charge simulée
        const ambient = 25.0; // Température ambiante
        
        return ambient + baseTemp + variation + load;
    }

    /**
     * 📈 Ajoute une température à l'historique
     */
    addToHistory(temperature) {
        this.temperatureHistory.push({
            temperature,
            timestamp: Date.now()
        });
        
        // Garder seulement les 100 dernières mesures
        if (this.temperatureHistory.length > 100) {
            this.temperatureHistory.shift();
        }
    }

    /**
     * 🎯 Met à jour le curseur de température
     */
    updateTemperatureCursor() {
        if (!this.temperatureCursor.autoRegulation) return;
        
        // Le curseur suit la température réelle avec un délai
        const targetTemp = this.currentTemperature;
        const currentPos = this.temperatureCursor.position;
        const diff = targetTemp - currentPos;
        
        // Mouvement progressif du curseur
        const movement = diff * this.temperatureCursor.speed;
        this.temperatureCursor.position += movement;
        
        // Limiter dans la plage autorisée
        this.temperatureCursor.position = Math.max(
            this.temperatureCursor.range.min,
            Math.min(this.temperatureCursor.range.max, this.temperatureCursor.position)
        );
        
        this.temperatureCursor.target = targetTemp;
    }

    /**
     * ⚙️ Régulation automatique de température
     */
    autoRegulateTemperature() {
        if (!this.temperatureCursor.autoRegulation) return;
        
        const currentTemp = this.currentTemperature;
        const optimalRange = { min: 35.0, max: 75.0 };
        
        // Ajustement automatique si hors plage optimale
        if (currentTemp > optimalRange.max) {
            // Température trop élevée - simulation de refroidissement
            this.temperatureCursor.speed = 0.2; // Plus rapide
            console.log(`🌡️ Régulation: Température élevée (${currentTemp.toFixed(1)}°C) - Refroidissement`);
        } else if (currentTemp < optimalRange.min) {
            // Température trop basse - simulation de réchauffement
            this.temperatureCursor.speed = 0.1; // Plus lent
            console.log(`🌡️ Régulation: Température basse (${currentTemp.toFixed(1)}°C) - Réchauffement`);
        } else {
            // Température optimale
            this.temperatureCursor.speed = 0.05; // Très lent
        }
    }

    /**
     * 📊 Obtient les statistiques de température
     */
    getTemperatureStats() {
        const recent = this.temperatureHistory.slice(-10);
        const average = recent.length > 0 ? 
            recent.reduce((sum, item) => sum + item.temperature, 0) / recent.length : 
            this.currentTemperature;
        
        const min = recent.length > 0 ? Math.min(...recent.map(item => item.temperature)) : this.currentTemperature;
        const max = recent.length > 0 ? Math.max(...recent.map(item => item.temperature)) : this.currentTemperature;
        
        return {
            current: this.currentTemperature,
            average: average,
            min: min,
            max: max,
            cursor: {
                position: this.temperatureCursor.position,
                target: this.temperatureCursor.target,
                autoRegulation: this.temperatureCursor.autoRegulation,
                speed: this.temperatureCursor.speed
            },
            history: this.temperatureHistory.slice(-20), // 20 dernières mesures
            lastReading: this.lastReading,
            platform: this.platform,
            isReal: this.lastReading > (Date.now() - 10000) // Lecture récente
        };
    }

    /**
     * 🎛️ Configure le curseur de température
     */
    configureCursor(config) {
        if (config.autoRegulation !== undefined) {
            this.temperatureCursor.autoRegulation = config.autoRegulation;
        }
        if (config.speed !== undefined) {
            this.temperatureCursor.speed = Math.max(0.01, Math.min(1.0, config.speed));
        }
        if (config.sensitivity !== undefined) {
            this.temperatureCursor.sensitivity = Math.max(0.1, Math.min(2.0, config.sensitivity));
        }
        
        console.log('🎛️ Curseur de température configuré:', this.temperatureCursor);
    }

    /**
     * 🔄 Réinitialise le capteur
     */
    reset() {
        this.temperatureHistory = [];
        this.temperatureCursor.position = 37.0;
        this.temperatureCursor.target = 37.0;
        console.log('🔄 Capteur de température réinitialisé');
    }
}

module.exports = CPUTemperatureSensor;
