/**
 * 🖥️ SYSTÈME D'ACTIONS BUREAU
 * Contrôle complet du bureau et des applications
 */

const EventEmitter = require('events');
const { exec, spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class DesktopActionsSystem extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      platform: os.platform(),
      actions: {
        fileOperations: true,
        applicationControl: true,
        systemCommands: true,
        windowManagement: true,
        desktopInteraction: true
      },
      stats: {
        totalActions: 0,
        successfulActions: 0,
        fileOperations: 0,
        appLaunches: 0,
        systemCommands: 0
      },
      security: {
        allowedPaths: [
          os.homedir(),
          path.join(os.homedir(), 'Desktop'),
          path.join(os.homedir(), 'Documents'),
          path.join(os.homedir(), 'Downloads'),
          '/tmp',
          process.cwd()
        ],
        blockedCommands: ['rm -rf /', 'del /f /s /q C:\\', 'format', 'fdisk']
      }
    };
    
    this.logger = console;
    this.runningProcesses = new Map();
    
    this.init();
  }

  async init() {
    console.log('🖥️ Initialisation du système d\'actions bureau...');
    
    // Détecter les applications disponibles
    await this.detectAvailableApplications();
    
    console.log('✅ Système d\'actions bureau initialisé');
  }

  // 🔍 DÉTECTER LES APPLICATIONS DISPONIBLES
  async detectAvailableApplications() {
    try {
      this.applications = new Map();
      
      switch (this.config.platform) {
        case 'darwin': // macOS
          await this.detectMacApplications();
          break;
        case 'win32': // Windows
          await this.detectWindowsApplications();
          break;
        case 'linux': // Linux
          await this.detectLinuxApplications();
          break;
      }
      
      console.log(`🔍 ${this.applications.size} applications détectées`);
      
    } catch (error) {
      console.error('❌ Erreur détection applications:', error.message);
    }
  }

  // 🍎 DÉTECTER APPLICATIONS MACOS
  async detectMacApplications() {
    try {
      // 📁 TOUS LES DOSSIERS D'APPLICATIONS POSSIBLES SUR MACOS
      const applicationDirs = [
        '/Applications',                                    // Applications système principales
        path.join(os.homedir(), 'Applications'),           // Applications utilisateur
        '/System/Applications',                             // Applications système (Big Sur+)
        '/System/Applications/Utilities',                   // Utilitaires système
        '/Applications/Utilities',                          // Utilitaires
        '/System/Library/CoreServices',                     // Services système
        '/System/Library/CoreServices/Applications',        // Applications de base
        '/usr/local/bin',                                   // Binaires Homebrew
        '/opt/homebrew/bin',                               // Homebrew Apple Silicon
        '/usr/bin',                                        // Binaires système
        '/bin',                                            // Binaires de base
        path.join(os.homedir(), '.local/bin'),             // Binaires utilisateur
        path.join(os.homedir(), 'bin'),                    // Binaires personnels
        '/Applications/Xcode.app/Contents/Developer/Applications', // Outils Xcode
        '/Library/Application Support',                     // Support applications
        path.join(os.homedir(), 'Library/Application Support'), // Support utilisateur
        '/System/Library/Frameworks',                       // Frameworks système
        '/Library/Frameworks'                              // Frameworks tiers
      ];

      let totalFound = 0;

      for (const dir of applicationDirs) {
        try {
          const items = await fs.readdir(dir);

          for (const item of items) {
            // Applications .app
            if (item.endsWith('.app')) {
              const appName = item.replace('.app', '');
              const appPath = path.join(dir, item);

              // Vérifier si c'est vraiment une application
              try {
                const stats = await fs.stat(appPath);
                if (stats.isDirectory()) {
                  this.applications.set(appName.toLowerCase(), {
                    name: appName,
                    path: appPath,
                    command: `open "${appPath}"`,
                    type: 'application',
                    location: dir
                  });
                  totalFound++;
                }
              } catch (e) {
                // Ignorer si inaccessible
              }
            }

            // Exécutables Unix (dans /usr/bin, /bin, etc.)
            else if (dir.includes('bin') && !item.includes('.')) {
              try {
                const itemPath = path.join(dir, item);
                const stats = await fs.stat(itemPath);

                // Vérifier si c'est exécutable
                if (stats.isFile() && (stats.mode & parseInt('111', 8))) {
                  // Exclure les scripts système trop techniques
                  const excludedBinaries = [
                    'sh', 'bash', 'zsh', 'csh', 'tcsh', 'fish',
                    'ls', 'cd', 'pwd', 'mkdir', 'rmdir', 'cp', 'mv', 'rm',
                    'cat', 'less', 'more', 'head', 'tail', 'grep', 'sed', 'awk',
                    'chmod', 'chown', 'sudo', 'su', 'ps', 'kill', 'killall',
                    'tar', 'gzip', 'gunzip', 'zip', 'unzip'
                  ];

                  if (!excludedBinaries.includes(item)) {
                    this.applications.set(`${item}_bin`, {
                      name: item.charAt(0).toUpperCase() + item.slice(1),
                      path: itemPath,
                      command: itemPath,
                      type: 'binary',
                      location: dir
                    });
                    totalFound++;
                  }
                }
              } catch (e) {
                // Ignorer si inaccessible
              }
            }
          }
        } catch (error) {
          // Dossier non accessible, continuer silencieusement
        }
      }

      console.log(`🔍 ${totalFound} applications trouvées dans les dossiers système`);


      // 🎯 APPLICATIONS SYSTÈME ET POPULAIRES COMMUNES
      const systemApps = {
        // Système de base
        'terminal': { command: 'open -a Terminal', type: 'system' },
        'finder': { command: 'open -a Finder', type: 'system' },
        'activity_monitor': { command: 'open -a "Activity Monitor"', type: 'utility' },
        'system_preferences': { command: 'open -a "System Preferences"', type: 'system' },
        'console': { command: 'open -a Console', type: 'utility' },
        'disk_utility': { command: 'open -a "Disk Utility"', type: 'utility' },
        'keychain_access': { command: 'open -a "Keychain Access"', type: 'security' },

        // Navigateurs
        'safari': { command: 'open -a Safari', type: 'browser' },
        'chrome': { command: 'open -a "Google Chrome"', type: 'browser' },
        'firefox': { command: 'open -a Firefox', type: 'browser' },
        'edge': { command: 'open -a "Microsoft Edge"', type: 'browser' },
        'opera': { command: 'open -a Opera', type: 'browser' },
        'brave': { command: 'open -a "Brave Browser"', type: 'browser' },

        // Éditeurs et IDE
        'vscode': { command: 'open -a "Visual Studio Code"', type: 'editor' },
        'textedit': { command: 'open -a TextEdit', type: 'editor' },
        'xcode': { command: 'open -a Xcode', type: 'ide' },
        'sublime_text': { command: 'open -a "Sublime Text"', type: 'editor' },
        'atom': { command: 'open -a Atom', type: 'editor' },

        // Communication
        'mail': { command: 'open -a Mail', type: 'communication' },
        'messages': { command: 'open -a Messages', type: 'communication' },
        'facetime': { command: 'open -a FaceTime', type: 'communication' },
        'zoom': { command: 'open -a zoom.us', type: 'communication' },
        'teams': { command: 'open -a "Microsoft Teams"', type: 'communication' },
        'slack': { command: 'open -a Slack', type: 'communication' },
        'discord': { command: 'open -a Discord', type: 'communication' },

        // Multimédia
        'music': { command: 'open -a Music', type: 'media' },
        'tv': { command: 'open -a TV', type: 'media' },
        'photos': { command: 'open -a Photos', type: 'media' },
        'preview': { command: 'open -a Preview', type: 'media' },
        'quicktime': { command: 'open -a "QuickTime Player"', type: 'media' },
        'vlc': { command: 'open -a VLC', type: 'media' },
        'spotify': { command: 'open -a Spotify', type: 'media' },

        // Productivité
        'calendar': { command: 'open -a Calendar', type: 'productivity' },
        'notes': { command: 'open -a Notes', type: 'productivity' },
        'reminders': { command: 'open -a Reminders', type: 'productivity' },
        'contacts': { command: 'open -a Contacts', type: 'productivity' },
        'calculator': { command: 'open -a Calculator', type: 'utility' },
        'word': { command: 'open -a "Microsoft Word"', type: 'productivity' },
        'excel': { command: 'open -a "Microsoft Excel"', type: 'productivity' },
        'powerpoint': { command: 'open -a "Microsoft PowerPoint"', type: 'productivity' },
        'pages': { command: 'open -a Pages', type: 'productivity' },
        'numbers': { command: 'open -a Numbers', type: 'productivity' },
        'keynote': { command: 'open -a Keynote', type: 'productivity' },

        // Développement
        'docker': { command: 'open -a Docker', type: 'development' },
        'github_desktop': { command: 'open -a "GitHub Desktop"', type: 'development' },
        'postman': { command: 'open -a Postman', type: 'development' },
        'git': { command: 'git', type: 'development' },
        'node': { command: 'node', type: 'development' },
        'npm': { command: 'npm', type: 'package_manager' },
        'python': { command: 'python3', type: 'development' },
        'pip': { command: 'pip3', type: 'package_manager' },
        'homebrew': { command: 'brew', type: 'package_manager' }
      };
      
      for (const [name, config] of Object.entries(systemApps)) {
        this.applications.set(name, {
          name: name.charAt(0).toUpperCase() + name.slice(1),
          ...config
        });
      }
      
    } catch (error) {
      console.error('❌ Erreur détection apps macOS:', error.message);
    }
  }



  // 🪟 DÉTECTER APPLICATIONS WINDOWS
  async detectWindowsApplications() {
    try {
      // Applications système communes Windows
      const systemApps = {
        'notepad': { command: 'notepad', type: 'editor' },
        'calculator': { command: 'calc', type: 'utility' },
        'explorer': { command: 'explorer', type: 'system' },
        'cmd': { command: 'cmd', type: 'terminal' },
        'powershell': { command: 'powershell', type: 'terminal' },
        'chrome': { command: 'chrome', type: 'browser' },
        'firefox': { command: 'firefox', type: 'browser' },
        'edge': { command: 'msedge', type: 'browser' }
      };
      
      for (const [name, config] of Object.entries(systemApps)) {
        this.applications.set(name, {
          name: name.charAt(0).toUpperCase() + name.slice(1),
          ...config
        });
      }
      
    } catch (error) {
      console.error('❌ Erreur détection apps Windows:', error.message);
    }
  }

  // 🐧 DÉTECTER APPLICATIONS LINUX
  async detectLinuxApplications() {
    try {
      // Applications système communes Linux
      const systemApps = {
        'terminal': { command: 'gnome-terminal', type: 'terminal' },
        'nautilus': { command: 'nautilus', type: 'system' },
        'firefox': { command: 'firefox', type: 'browser' },
        'chrome': { command: 'google-chrome', type: 'browser' },
        'gedit': { command: 'gedit', type: 'editor' },
        'code': { command: 'code', type: 'editor' }
      };
      
      for (const [name, config] of Object.entries(systemApps)) {
        this.applications.set(name, {
          name: name.charAt(0).toUpperCase() + name.slice(1),
          ...config
        });
      }
      
    } catch (error) {
      console.error('❌ Erreur détection apps Linux:', error.message);
    }
  }

  // 🚀 LANCER UNE APPLICATION
  async launchApplication(appName, args = []) {
    try {
      this.config.stats.totalActions++;
      this.config.stats.appLaunches++;
      
      const app = this.applications.get(appName.toLowerCase());
      
      if (!app) {
        throw new Error(`Application non trouvée: ${appName}`);
      }
      
      console.log(`🚀 Lancement de ${app.name}...`);
      
      const command = args.length > 0 ? `${app.command} ${args.join(' ')}` : app.command;
      
      const process = spawn(command, { shell: true, detached: true });
      
      const processId = `${appName}_${Date.now()}`;
      this.runningProcesses.set(processId, {
        name: app.name,
        command,
        process,
        startTime: new Date().toISOString()
      });
      
      process.on('error', (error) => {
        console.error(`❌ Erreur lancement ${app.name}:`, error.message);
        this.runningProcesses.delete(processId);
      });
      
      process.on('exit', (code) => {
        console.log(`🔚 ${app.name} fermé (code: ${code})`);
        this.runningProcesses.delete(processId);
      });
      
      this.config.stats.successfulActions++;
      
      this.emit('applicationLaunched', {
        name: app.name,
        command,
        processId,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: true,
        processId,
        message: `${app.name} lancé avec succès`
      };
      
    } catch (error) {
      console.error(`❌ Erreur lancement application: ${error.message}`);
      
      this.emit('actionError', {
        action: 'launchApplication',
        target: appName,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 📁 OPÉRATIONS DE FICHIERS
  async performFileOperation(operation, params) {
    try {
      this.config.stats.totalActions++;
      this.config.stats.fileOperations++;
      
      // Vérifier la sécurité du chemin
      if (params.path && !this.isPathAllowed(params.path)) {
        throw new Error(`Chemin non autorisé: ${params.path}`);
      }
      
      let result;
      
      switch (operation) {
        case 'create':
          result = await this.createFile(params);
          break;
        case 'read':
          result = await this.readFile(params);
          break;
        case 'write':
          result = await this.writeFile(params);
          break;
        case 'delete':
          result = await this.deleteFile(params);
          break;
        case 'list':
          result = await this.listDirectory(params);
          break;
        case 'mkdir':
          result = await this.createDirectory(params);
          break;
        default:
          throw new Error(`Opération non supportée: ${operation}`);
      }
      
      this.config.stats.successfulActions++;
      
      this.emit('fileOperation', {
        operation,
        params,
        result,
        timestamp: new Date().toISOString()
      });
      
      return result;
      
    } catch (error) {
      console.error(`❌ Erreur opération fichier: ${error.message}`);
      
      this.emit('actionError', {
        action: 'fileOperation',
        operation,
        params,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 📝 CRÉER UN FICHIER
  async createFile(params) {
    const { path: filePath, content = '', encoding = 'utf8' } = params;
    
    await fs.writeFile(filePath, content, encoding);
    
    return {
      success: true,
      message: `Fichier créé: ${filePath}`,
      path: filePath,
      size: Buffer.byteLength(content, encoding)
    };
  }

  // 📖 LIRE UN FICHIER
  async readFile(params) {
    const { path: filePath, encoding = 'utf8' } = params;
    
    const content = await fs.readFile(filePath, encoding);
    const stats = await fs.stat(filePath);
    
    return {
      success: true,
      content,
      path: filePath,
      size: stats.size,
      modified: stats.mtime
    };
  }

  // ✏️ ÉCRIRE DANS UN FICHIER
  async writeFile(params) {
    const { path: filePath, content, encoding = 'utf8', append = false } = params;
    
    if (append) {
      await fs.appendFile(filePath, content, encoding);
    } else {
      await fs.writeFile(filePath, content, encoding);
    }
    
    return {
      success: true,
      message: `Fichier ${append ? 'modifié' : 'écrit'}: ${filePath}`,
      path: filePath,
      size: Buffer.byteLength(content, encoding)
    };
  }

  // 🗑️ SUPPRIMER UN FICHIER
  async deleteFile(params) {
    const { path: filePath } = params;
    
    await fs.unlink(filePath);
    
    return {
      success: true,
      message: `Fichier supprimé: ${filePath}`,
      path: filePath
    };
  }

  // 📂 LISTER UN DOSSIER
  async listDirectory(params) {
    const { path: dirPath = '.', detailed = false } = params;
    
    const items = await fs.readdir(dirPath);
    
    if (detailed) {
      const detailedItems = [];
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        detailedItems.push({
          name: item,
          path: itemPath,
          type: stats.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime,
          permissions: stats.mode
        });
      }
      
      return {
        success: true,
        path: dirPath,
        items: detailedItems,
        count: detailedItems.length
      };
    }
    
    return {
      success: true,
      path: dirPath,
      items,
      count: items.length
    };
  }

  // 📁 CRÉER UN DOSSIER
  async createDirectory(params) {
    const { path: dirPath, recursive = true } = params;
    
    await fs.mkdir(dirPath, { recursive });
    
    return {
      success: true,
      message: `Dossier créé: ${dirPath}`,
      path: dirPath
    };
  }

  // 💻 EXÉCUTER UNE COMMANDE SYSTÈME
  async executeSystemCommand(command, options = {}) {
    try {
      this.config.stats.totalActions++;
      this.config.stats.systemCommands++;
      
      // Vérifier la sécurité de la commande
      if (this.isCommandBlocked(command)) {
        throw new Error(`Commande bloquée pour sécurité: ${command}`);
      }
      
      console.log(`💻 Exécution commande: ${command}`);
      
      return new Promise((resolve, reject) => {
        exec(command, {
          timeout: options.timeout || 30000,
          maxBuffer: options.maxBuffer || 1024 * 1024, // 1MB
          ...options
        }, (error, stdout, stderr) => {
          if (error) {
            console.error(`❌ Erreur commande: ${error.message}`);
            reject(error);
            return;
          }
          
          this.config.stats.successfulActions++;
          
          const result = {
            success: true,
            command,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            timestamp: new Date().toISOString()
          };
          
          this.emit('commandExecuted', result);
          
          resolve(result);
        });
      });
      
    } catch (error) {
      console.error(`❌ Erreur exécution commande: ${error.message}`);
      
      this.emit('actionError', {
        action: 'executeSystemCommand',
        command,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 🔒 VÉRIFIER SI LE CHEMIN EST AUTORISÉ
  isPathAllowed(filePath) {
    const absolutePath = path.resolve(filePath);
    
    return this.config.security.allowedPaths.some(allowedPath => {
      const resolvedAllowedPath = path.resolve(allowedPath);
      return absolutePath.startsWith(resolvedAllowedPath);
    });
  }

  // 🚫 VÉRIFIER SI LA COMMANDE EST BLOQUÉE
  isCommandBlocked(command) {
    const lowerCommand = command.toLowerCase();
    
    return this.config.security.blockedCommands.some(blocked => 
      lowerCommand.includes(blocked.toLowerCase())
    );
  }

  // 📊 OBTENIR LES STATISTIQUES
  getStats() {
    return {
      ...this.config.stats,
      platform: this.config.platform,
      applicationsDetected: this.applications.size,
      runningProcesses: this.runningProcesses.size,
      successRate: this.config.stats.totalActions > 0 
        ? (this.config.stats.successfulActions / this.config.stats.totalActions * 100).toFixed(1) + '%'
        : '0%'
    };
  }

  // 📱 OBTENIR LA LISTE DES APPLICATIONS
  getApplications() {
    return Array.from(this.applications.entries()).map(([key, app]) => ({
      id: key,
      ...app
    }));
  }

  // 🔄 OBTENIR LES PROCESSUS EN COURS
  getRunningProcesses() {
    return Array.from(this.runningProcesses.entries()).map(([id, process]) => ({
      id,
      ...process,
      pid: process.process.pid
    }));
  }
}

module.exports = DesktopActionsSystem;
