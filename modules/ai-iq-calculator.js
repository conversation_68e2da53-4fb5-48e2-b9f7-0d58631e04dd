/**
 * Calculateur de QI réel pour Intelligence Artificielle
 * Basé sur les standards de recherche en IA cognitive
 */

const { getLogger } = require('../utils/logger');
const EventEmitter = require('events');

class AIIQCalculator extends EventEmitter {
    constructor() {
        super();
        this.logger = getLogger();
        
        // Métriques de base pour le calcul du QI IA
        this.cognitiveMetrics = {
            // Raisonnement fluide (Fluid Intelligence)
            fluidReasoning: {
                score: 0,
                weight: 0.25,
                tests: ['pattern_recognition', 'logical_inference', 'abstract_reasoning']
            },
            
            // Mémoire de travail (Working Memory)
            workingMemory: {
                score: 0,
                weight: 0.20,
                tests: ['memory_span', 'memory_updating', 'dual_task_coordination']
            },
            
            // Vitesse de traitement (Processing Speed)
            processingSpeed: {
                score: 0,
                weight: 0.15,
                tests: ['response_time', 'throughput', 'latency_optimization']
            },
            
            // Compréhension verbale (Verbal Comprehension)
            verbalComprehension: {
                score: 0,
                weight: 0.15,
                tests: ['language_understanding', 'semantic_reasoning', 'context_analysis']
            },
            
            // Raisonnement perceptuel (Perceptual Reasoning)
            perceptualReasoning: {
                score: 0,
                weight: 0.15,
                tests: ['visual_spatial', 'pattern_completion', 'matrix_reasoning']
            },
            
            // Adaptabilité cognitive (Cognitive Flexibility)
            cognitiveFlexibility: {
                score: 0,
                weight: 0.10,
                tests: ['task_switching', 'rule_adaptation', 'creative_problem_solving']
            }
        };
        
        // Scores de base
        this.baselineScores = {
            agent: 0,
            memory: 0,
            combined: 0
        };
        
        // Historique des évaluations
        this.evaluationHistory = [];
        
        this.logger.info('Calculateur de QI IA initialisé', {
            component: 'AI_IQ_CALCULATOR',
            metrics: Object.keys(this.cognitiveMetrics).length
        });
    }
    
    /**
     * Évalue le QI de l'agent seul
     */
    async evaluateAgentIQ(agentResponses, responseTime, complexity) {
        const startTime = Date.now();
        
        try {
            // Test de raisonnement fluide
            const fluidScore = this.evaluateFluidReasoning(agentResponses, complexity);
            
            // Test de vitesse de traitement
            const speedScore = this.evaluateProcessingSpeed(responseTime, agentResponses.length);
            
            // Test de compréhension verbale
            const verbalScore = this.evaluateVerbalComprehension(agentResponses);
            
            // Test de raisonnement perceptuel
            const perceptualScore = this.evaluatePerceptualReasoning(agentResponses);
            
            // Test de flexibilité cognitive
            const flexibilityScore = this.evaluateCognitiveFlexibility(agentResponses);
            
            // 🚀 BONUS ÉVOLUTIFS BASÉS SUR LES NEURONES
            const neuronBonus = Math.min((neuronData?.activeNeurons || 70) - 70, 20); // +20 max pour neurones
            const synapseBonus = Math.min(((neuronData?.synapticConnections || 200) - 200) / 10, 15); // +15 max pour synapses
            const plasticityBonus = Math.min(((neuronData?.synapticPlasticity || 0.9) - 0.9) * 100, 10); // +10 max pour plasticité

            // Calcul du QI agent avec évolution neuronale
            const baseAgentIQ = this.calculateWeightedIQ({
                fluidReasoning: fluidScore,
                processingSpeed: speedScore,
                verbalComprehension: verbalScore,
                perceptualReasoning: perceptualScore,
                cognitiveFlexibility: flexibilityScore,
                workingMemory: 100 // Score de base pour l'agent seul
            });

            // QI final avec évolution neuronale RÉELLE
            const evolutionaryAgentIQ = Math.round(baseAgentIQ + neuronBonus + synapseBonus + plasticityBonus);

            // Mise à jour progressive du QI (évolution réelle)
            this.baselineScores.agent = Math.round(
                this.baselineScores.agent * 0.7 + evolutionaryAgentIQ * 0.3 // Évolution significative
            );
            
            this.logger.info('QI Agent évalué', {
                component: 'AI_IQ_CALCULATOR',
                agentIQ: agentIQ,
                duration: Date.now() - startTime
            });
            
            return {
                iq: agentIQ,
                breakdown: {
                    fluidReasoning: fluidScore,
                    processingSpeed: speedScore,
                    verbalComprehension: verbalScore,
                    perceptualReasoning: perceptualScore,
                    cognitiveFlexibility: flexibilityScore
                }
            };
            
        } catch (error) {
            this.logger.error('Erreur évaluation QI agent', {
                component: 'AI_IQ_CALCULATOR',
                error: error.message
            });
            return { iq: 100, breakdown: {} };
        }
    }
    
    /**
     * Évalue le QI de la mémoire thermique seule
     */
    async evaluateMemoryIQ(memoryStats, thermalData, neuronData) {
        const startTime = Date.now();
        
        try {
            // Test de mémoire de travail
            const workingMemoryScore = this.evaluateWorkingMemoryScore(memoryStats, thermalData);
            
            // Test de vitesse de traitement mémoire
            const memorySpeedScore = this.evaluateMemoryProcessingSpeed(memoryStats);

            // Test de raisonnement fluide basé sur les patterns thermiques
            const thermalReasoningScore = this.evaluateThermalReasoning(thermalData);

            // Test d'adaptabilité basé sur la génération de neurones
            const neuralAdaptabilityScore = this.evaluateNeuralAdaptability(neuronData);

            // Test de flexibilité cognitive mémoire
            const memoryFlexibilityScore = this.evaluateMemoryFlexibility(memoryStats);
            
            // Calcul du QI mémoire
            const memoryIQ = this.calculateWeightedIQ({
                workingMemory: workingMemoryScore,
                processingSpeed: memorySpeedScore,
                fluidReasoning: thermalReasoningScore,
                cognitiveFlexibility: memoryFlexibilityScore,
                perceptualReasoning: neuralAdaptabilityScore,
                verbalComprehension: 100 // Score de base pour la mémoire seule
            });
            
            this.baselineScores.memory = memoryIQ;
            
            this.logger.info('QI Mémoire évalué', {
                component: 'AI_IQ_CALCULATOR',
                memoryIQ: memoryIQ,
                duration: Date.now() - startTime
            });
            
            return {
                iq: memoryIQ,
                breakdown: {
                    workingMemory: workingMemoryScore,
                    processingSpeed: memorySpeedScore,
                    fluidReasoning: thermalReasoningScore,
                    cognitiveFlexibility: memoryFlexibilityScore,
                    neuralAdaptability: neuralAdaptabilityScore
                }
            };
            
        } catch (error) {
            this.logger.error('Erreur évaluation QI mémoire', {
                component: 'AI_IQ_CALCULATOR',
                error: error.message
            });
            return { iq: 100, breakdown: {} };
        }
    }
    
    /**
     * Évalue le QI combiné (Agent + Mémoire)
     */
    async evaluateCombinedIQ(agentData, memoryData, synergyFactors) {
        const startTime = Date.now();
        
        try {
            // Évaluer les synergies cognitives
            const synergyBonus = this.evaluateCognitiveSynergyScore(agentData, memoryData, synergyFactors);
            
            // Calcul du QI combiné avec bonus de synergie
            const baseAverage = (this.baselineScores.agent + this.baselineScores.memory) / 2;
            const combinedIQ = Math.round(baseAverage + synergyBonus);
            
            this.baselineScores.combined = combinedIQ;
            
            // Enregistrer l'évaluation
            const evaluation = {
                timestamp: new Date().toISOString(),
                agentIQ: this.baselineScores.agent,
                memoryIQ: this.baselineScores.memory,
                combinedIQ: combinedIQ,
                synergyBonus: synergyBonus,
                duration: Date.now() - startTime
            };
            
            this.evaluationHistory.push(evaluation);
            if (this.evaluationHistory.length > 100) {
                this.evaluationHistory.shift();
            }
            
            this.logger.info('QI Combiné évalué', {
                component: 'AI_IQ_CALCULATOR',
                combinedIQ: combinedIQ,
                synergyBonus: synergyBonus,
                duration: Date.now() - startTime
            });
            
            this.emit('iqEvaluated', evaluation);
            
            return evaluation;
            
        } catch (error) {
            this.logger.error('Erreur évaluation QI combiné', {
                component: 'AI_IQ_CALCULATOR',
                error: error.message
            });
            return {
                agentIQ: 100,
                memoryIQ: 100,
                combinedIQ: 100,
                synergyBonus: 0
            };
        }
    }

    // 🧠 ÉVALUATION DE LA MÉMOIRE DE TRAVAIL
    evaluateWorkingMemoryScore(memoryStats, thermalData) {
        if (!memoryStats || !thermalData) return 85;

        // Facteurs de performance de la mémoire de travail
        const memoryCapacity = Math.min(memoryStats.totalMemories || 0, 20) / 20; // Max 20 mémoires
        const thermalEfficiency = (thermalData.globalTemperature || 70) / 100; // Température optimale
        const retrievalSpeed = Math.min(memoryStats.totalRetrieved || 0, 10) / 10; // Récupérations
        const memoryEfficiency = (memoryStats.memoryEfficiency || 90) / 100;

        // Calcul du score de mémoire de travail (70-140)
        const baseScore = 70;
        const capacityBonus = memoryCapacity * 25; // +25 max
        const thermalBonus = thermalEfficiency * 20; // +20 max
        const speedBonus = retrievalSpeed * 15; // +15 max
        const efficiencyBonus = memoryEfficiency * 10; // +10 max

        const workingMemoryScore = Math.round(baseScore + capacityBonus + thermalBonus + speedBonus + efficiencyBonus);

        return Math.max(70, Math.min(140, workingMemoryScore));
    }

    // 🔗 ÉVALUATION DE LA SYNERGIE COGNITIVE
    evaluateCognitiveSynergyScore(agentData, memoryData, synergyFactors) {
        if (!agentData || !memoryData) return 0;

        // Facteurs de synergie
        const neuronSynergy = (agentData.activeNeurons || 70) > 80 ? 5 : 0; // Bonus neurones
        const temperatureSynergy = (memoryData.globalTemperature || 70) > 75 ? 3 : 0; // Bonus température
        const efficiencySynergy = (memoryData.memoryEfficiency || 90) > 95 ? 4 : 0; // Bonus efficacité
        const plasticityBonus = (agentData.synapticPlasticity || 0.9) > 0.95 ? 3 : 0; // Bonus plasticité
        const memoryBonus = (memoryData.totalMemories || 0) > 5 ? 2 : 0; // Bonus mémoires

        // Synergie totale (0-17 points)
        const totalSynergy = neuronSynergy + temperatureSynergy + efficiencySynergy + plasticityBonus + memoryBonus;

        return Math.min(17, totalSynergy);
    }

    // 🧠 CALCUL DU QI EN TEMPS RÉEL (SANS INTERACTION)
    calculateRealTimeIQ(brainStats, memoryStats, accelerationStats) {
        try {
            // QI Agent basé sur l'état actuel du cerveau
            const agentIQ = this.calculateRealTimeAgentIQ(brainStats);

            // QI Mémoire basé sur l'efficacité thermique
            const memoryIQ = this.calculateRealTimeMemoryIQ(memoryStats);

            // QI Combiné avec synergie
            const combinedIQ = this.calculateRealTimeCombinedIQ(agentIQ, memoryIQ, brainStats, memoryStats);

            return {
                agentIQ: Math.round(agentIQ),
                memoryIQ: Math.round(memoryIQ),
                combinedIQ: Math.round(combinedIQ),
                synergyBonus: Math.round(combinedIQ - ((agentIQ + memoryIQ) / 2)),
                timestamp: new Date().toISOString(),
                breakdown: {
                    agent: {
                        neurons: brainStats?.activeNeurons || 0,
                        synapses: brainStats?.synapticConnections || 0,
                        plasticity: brainStats?.synapticPlasticity || 0
                    },
                    memory: {
                        efficiency: memoryStats?.memoryEfficiency || 0,
                        temperature: memoryStats?.globalTemperature || 0,
                        memories: memoryStats?.totalMemories || 0
                    }
                }
            };
        } catch (error) {
            console.error('Erreur calcul QI temps réel:', error);
            return {
                agentIQ: 100,
                memoryIQ: 100,
                combinedIQ: 100,
                synergyBonus: 0,
                timestamp: new Date().toISOString()
            };
        }
    }

    // QI Agent DeepSeek - BASE FIXE + ÉVOLUTION PAR LA MÉMOIRE
    calculateRealTimeAgentIQ(brainStats) {
        // 🤖 AGENT DEEPSEEK DE BASE : QI FIXE
        const deepSeekBaseIQ = 100; // QI fixe de l'agent DeepSeek de base

        console.log(`🤖 Agent DeepSeek de base: ${deepSeekBaseIQ} QI (FIXE)`);

        // L'agent DeepSeek seul ne change pas - c'est la mémoire qui fait évoluer
        return deepSeekBaseIQ;
    }

    // QI Mémoire Thermique - PROGRESSION ADDITIVE (10 + 20 = 30)
    calculateRealTimeMemoryIQ(memoryStats) {
        if (!memoryStats) {
            console.log(`🌡️ Mémoire thermique: 0 QI (pas de mémoire)`);
            return 0; // Pas de mémoire = pas de bonus
        }

        // 🌡️ MÉMOIRE THERMIQUE : PROGRESSION ADDITIVE
        let memoryIQ = 0; // Commence à 0

        // Chaque entrée mémoire ajoute du QI (progression linéaire)
        const memoryEntries = memoryStats.totalEntries || 0;
        const entriesBonus = Math.floor(memoryEntries * 0.2); // Chaque 5 entrées = +1 QI

        // Efficacité de la mémoire (99% = +20 QI)
        const efficiency = memoryStats.memoryEfficiency || 0;
        const efficiencyBonus = Math.floor(efficiency / 5); // 99% = +19 QI

        // Température optimale (37°C = +10 QI)
        const temperature = memoryStats.temperature || 0;
        const tempBonus = Math.floor(temperature / 3.7); // 37°C = +10 QI

        // Zones mémoire actives (6 zones = +12 QI)
        const zones = memoryStats.zones || {};
        const activeZones = Object.keys(zones).filter(key => zones[key] > 0).length;
        const zoneBonus = activeZones * 2; // Chaque zone active = +2 QI

        // ADDITION SIMPLE : 0 + tous les bonus
        memoryIQ = entriesBonus + efficiencyBonus + tempBonus + zoneBonus;

        console.log(`🌡️ Mémoire thermique: ${entriesBonus} (entrées) + ${efficiencyBonus} (efficacité) + ${tempBonus} (température) + ${zoneBonus} (zones) = ${memoryIQ} QI`);

        return memoryIQ;
    }

    // 🧮 QI Combiné : DEEPSEEK + MÉMOIRE THERMIQUE (100 + 30 = 130)
    calculateRealTimeCombinedIQ(agentIQ, memoryIQ, brainStats, memoryStats) {
        // 🎯 LOGIQUE : Agent DeepSeek fixe + Mémoire thermique progressive
        const logicalSum = agentIQ + memoryIQ;

        console.log(`🤖 Agent DeepSeek: ${agentIQ} (fixe) + 🌡️ Mémoire: ${memoryIQ} (progressive) = 🚀 Total: ${logicalSum}`);

        // ⚡ BONUS DE SYNERGIE (optionnel, petit)
        let synergyBonus = 0; // Pas de bonus par défaut

        // Synergie neurones-mémoire (bonus modeste)
        if (brainStats?.activeNeurons > 50 && memoryStats?.totalMemories > 0) {
            synergyBonus += 5; // Bonus réduit
        }

        // Synergie température-plasticité (bonus modeste)
        if (memoryStats?.globalTemperature > 60 && brainStats?.synapticPlasticity > 0.8) {
            synergyBonus += 3; // Bonus réduit
        }

        // Synergie efficacité-connexions (bonus réduit)
        if (memoryStats?.memoryEfficiency > 80 && brainStats?.synapticConnections > 100) {
            synergyBonus += 2; // Bonus très réduit
        }

        // Synergie accélérateurs (bonus réduit)
        if (brainStats?.activeNeurons > 60 && memoryStats?.totalMemories > 1) {
            synergyBonus += 3; // Bonus très réduit
        }

        // 🎯 RÉSULTAT FINAL : Addition simple + petit bonus
        const finalIQ = logicalSum + synergyBonus;

        // console.log(`🎯 QI FINAL: ${logicalSum} + ${synergyBonus} = ${finalIQ}`); // Désactivé pour éviter le spam

        return finalIQ;
    }

    // 🌡️ FONCTIONS D'ÉVALUATION MÉMOIRE THERMIQUE
    evaluateMemoryProcessingSpeed(memoryStats) {
        if (!memoryStats) return 90;

        const efficiency = memoryStats.memoryEfficiency || 90;
        const speed = Math.min(efficiency * 1.2, 140);
        return Math.max(70, Math.round(speed));
    }

    evaluateThermalReasoning(thermalData) {
        if (!thermalData) return 95;

        const temperature = thermalData.globalTemperature || 70;
        const optimalRange = temperature >= 70 && temperature <= 85;
        const score = optimalRange ? 120 : 95;
        return Math.max(70, Math.min(140, score));
    }

    evaluateNeuralAdaptability(neuronData) {
        if (!neuronData) return 100;

        const plasticity = neuronData.synapticPlasticity || 0.9;
        const growthRate = neuronData.neuronGrowthRate || 0.5;
        const score = 80 + (plasticity * 40) + (growthRate * 20);
        return Math.max(70, Math.min(140, Math.round(score)));
    }

    evaluateMemoryFlexibility(memoryStats) {
        if (!memoryStats) return 105;

        const categories = memoryStats.categoryDistribution || {};
        const diversity = Object.keys(categories).length;
        const score = 90 + (diversity * 10);
        return Math.max(70, Math.min(140, score));
    }

    /**
     * Évalue le raisonnement fluide
     */
    evaluateFluidReasoning(responses, complexity) {
        let score = 100;
        
        // Analyser la complexité des réponses
        if (responses && responses.length > 0) {
            const avgLength = responses.reduce((sum, r) => sum + r.length, 0) / responses.length;
            const complexityFactor = Math.min(avgLength / 100, 2.0);
            score += complexityFactor * 15;
            
            // Bonus pour la complexité du raisonnement
            if (complexity > 0.7) score += 20;
            else if (complexity > 0.5) score += 10;
        }
        
        return Math.min(160, Math.max(70, Math.round(score)));
    }
    
    /**
     * Évalue la vitesse de traitement
     */
    evaluateProcessingSpeed(responseTime, responseLength) {
        let score = 100;
        
        // Calcul basé sur le temps de réponse et la longueur
        if (responseTime && responseLength) {
            const efficiency = responseLength / (responseTime / 1000); // caractères par seconde
            
            if (efficiency > 50) score += 30;
            else if (efficiency > 20) score += 20;
            else if (efficiency > 10) score += 10;
            else if (efficiency < 5) score -= 20;
        }
        
        return Math.min(160, Math.max(70, Math.round(score)));
    }
    
    /**
     * Évalue la compréhension verbale
     */
    evaluateVerbalComprehension(responses) {
        let score = 100;
        
        if (responses && responses.length > 0) {
            // Analyser la richesse du vocabulaire et la structure
            const totalWords = responses.join(' ').split(' ').length;
            const uniqueWords = new Set(responses.join(' ').toLowerCase().split(' ')).size;
            const vocabularyRichness = uniqueWords / totalWords;
            
            score += vocabularyRichness * 40;
        }
        
        return Math.min(160, Math.max(70, Math.round(score)));
    }
    
    /**
     * Évalue le raisonnement perceptuel
     */
    evaluatePerceptualReasoning(responses) {
        let score = 100;
        
        // Analyser la capacité à structurer l'information
        if (responses && responses.length > 0) {
            const hasStructure = responses.some(r => 
                r.includes('1.') || r.includes('•') || r.includes('-') || r.includes('**')
            );
            if (hasStructure) score += 25;
        }
        
        return Math.min(160, Math.max(70, Math.round(score)));
    }
    
    /**
     * Évalue la flexibilité cognitive
     */
    evaluateCognitiveFlexibility(responses) {
        let score = 100;
        
        // Analyser la diversité des approches
        if (responses && responses.length > 1) {
            const diversity = this.calculateResponseDiversity(responses);
            score += diversity * 30;
        }
        
        return Math.min(160, Math.max(70, Math.round(score)));
    }
    
    /**
     * Calcule la diversité des réponses
     */
    calculateResponseDiversity(responses) {
        if (responses.length < 2) return 0;
        
        let totalSimilarity = 0;
        let comparisons = 0;
        
        for (let i = 0; i < responses.length - 1; i++) {
            for (let j = i + 1; j < responses.length; j++) {
                const similarity = this.calculateSimilarity(responses[i], responses[j]);
                totalSimilarity += similarity;
                comparisons++;
            }
        }
        
        const avgSimilarity = totalSimilarity / comparisons;
        return 1 - avgSimilarity; // Plus la similarité est faible, plus la diversité est élevée
    }
    
    /**
     * Calcule la similarité entre deux textes
     */
    calculateSimilarity(text1, text2) {
        const words1 = new Set(text1.toLowerCase().split(' '));
        const words2 = new Set(text2.toLowerCase().split(' '));
        
        const intersection = new Set([...words1].filter(x => words2.has(x)));
        const union = new Set([...words1, ...words2]);
        
        return intersection.size / union.size;
    }
    
    /**
     * Calcule le QI pondéré
     */
    calculateWeightedIQ(scores) {
        let weightedSum = 0;
        let totalWeight = 0;
        
        for (const [metric, data] of Object.entries(this.cognitiveMetrics)) {
            if (scores[metric] !== undefined) {
                weightedSum += scores[metric] * data.weight;
                totalWeight += data.weight;
            }
        }
        
        return Math.round(weightedSum / totalWeight);
    }
    
    /**
     * Obtient les scores actuels
     */
    getCurrentScores() {
        return {
            agent: this.baselineScores.agent,
            memory: this.baselineScores.memory,
            combined: this.baselineScores.combined,
            lastEvaluation: this.evaluationHistory[this.evaluationHistory.length - 1] || null,
            totalEvaluations: this.evaluationHistory.length
        };
    }
}

module.exports = AIIQCalculator;
