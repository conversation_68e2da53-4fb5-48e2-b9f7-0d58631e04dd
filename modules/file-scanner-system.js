/**
 * 📁 SYSTÈME DE SCAN DE FICHIERS
 * Scanner ultra-rapide de fichiers et applications
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const crypto = require('crypto');

class FileScannerSystem extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      scanPaths: [
        os.homedir(),
        '/Applications', // macOS
        'C:\\Program Files', // Windows
        'C:\\Program Files (x86)', // Windows
        '/usr/bin', // Linux
        '/usr/local/bin', // Linux
        process.cwd()
      ],
      fileTypes: {
        applications: ['.app', '.exe', '.msi', '.deb', '.rpm', '.dmg'],
        documents: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
        images: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
        videos: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv'],
        audio: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
        code: ['.js', '.py', '.java', '.cpp', '.c', '.html', '.css'],
        archives: ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
      },
      scanOptions: {
        maxDepth: 5,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        excludeHidden: true,
        calculateHashes: false,
        followSymlinks: false
      },
      stats: {
        totalScans: 0,
        filesScanned: 0,
        directoriesScanned: 0,
        applicationsFound: 0,
        totalSize: 0,
        scanTime: 0
      }
    };
    
    this.logger = console;
    this.scanCache = new Map();
    this.scanResults = {
      files: new Map(),
      applications: new Map(),
      directories: new Map(),
      duplicates: new Map()
    };
    
    this.init();
  }

  async init() {
    console.log('📁 Initialisation du système de scan de fichiers...');
    
    // Filtrer les chemins selon la plateforme
    this.config.scanPaths = this.config.scanPaths.filter(scanPath => {
      try {
        return fs.access(scanPath).then(() => true).catch(() => false);
      } catch {
        return false;
      }
    });
    
    console.log('✅ Système de scan de fichiers initialisé');
  }

  // 🔍 SCANNER COMPLET DU SYSTÈME
  async performFullScan(options = {}) {
    try {
      const scanStart = Date.now();
      this.config.stats.totalScans++;
      
      console.log('🔍 Démarrage du scan complet du système...');
      
      const scanOptions = {
        ...this.config.scanOptions,
        ...options
      };
      
      // Réinitialiser les résultats
      this.scanResults = {
        files: new Map(),
        applications: new Map(),
        directories: new Map(),
        duplicates: new Map()
      };
      
      // Scanner chaque chemin
      for (const scanPath of this.config.scanPaths) {
        try {
          await this.scanDirectory(scanPath, scanOptions, 0);
        } catch (error) {
          console.log(`⚠️ Erreur scan ${scanPath}: ${error.message}`);
        }
      }
      
      // Détecter les doublons si demandé
      if (scanOptions.detectDuplicates) {
        await this.detectDuplicates();
      }
      
      const scanDuration = Date.now() - scanStart;
      this.config.stats.scanTime = scanDuration;
      
      console.log(`✅ Scan terminé en ${scanDuration}ms`);
      console.log(`📊 ${this.scanResults.files.size} fichiers, ${this.scanResults.applications.size} applications`);
      
      this.emit('scanCompleted', {
        duration: scanDuration,
        filesFound: this.scanResults.files.size,
        applicationsFound: this.scanResults.applications.size,
        directoriesFound: this.scanResults.directories.size,
        timestamp: new Date().toISOString()
      });
      
      return this.getScanSummary();
      
    } catch (error) {
      console.error(`❌ Erreur scan complet: ${error.message}`);
      
      this.emit('scanError', {
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 📂 SCANNER UN DOSSIER
  async scanDirectory(dirPath, options, currentDepth) {
    try {
      // Vérifier la profondeur maximale
      if (currentDepth >= options.maxDepth) {
        return;
      }
      
      // Vérifier l'accès au dossier
      try {
        await fs.access(dirPath);
      } catch {
        return; // Pas d'accès, passer
      }
      
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      // Enregistrer le dossier
      const dirStats = await fs.stat(dirPath);
      this.scanResults.directories.set(dirPath, {
        path: dirPath,
        name: path.basename(dirPath),
        size: dirStats.size,
        modified: dirStats.mtime,
        created: dirStats.birthtime,
        itemCount: items.length,
        depth: currentDepth
      });
      
      this.config.stats.directoriesScanned++;
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        // Ignorer les fichiers cachés si demandé
        if (options.excludeHidden && item.name.startsWith('.')) {
          continue;
        }
        
        try {
          if (item.isDirectory()) {
            // Scanner récursivement les sous-dossiers
            if (options.followSymlinks || !item.isSymbolicLink()) {
              await this.scanDirectory(itemPath, options, currentDepth + 1);
            }
          } else if (item.isFile()) {
            await this.scanFile(itemPath, options);
          }
        } catch (error) {
          // Continuer même en cas d'erreur sur un élément
          console.log(`⚠️ Erreur scan ${itemPath}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`❌ Erreur scan dossier ${dirPath}: ${error.message}`);
    }
  }

  // 📄 SCANNER UN FICHIER
  async scanFile(filePath, options) {
    try {
      const stats = await fs.stat(filePath);
      
      // Vérifier la taille maximale
      if (stats.size > options.maxFileSize) {
        return;
      }
      
      const fileName = path.basename(filePath);
      const fileExt = path.extname(filePath).toLowerCase();
      const fileType = this.getFileType(fileExt);
      
      const fileInfo = {
        path: filePath,
        name: fileName,
        extension: fileExt,
        type: fileType,
        size: stats.size,
        modified: stats.mtime,
        created: stats.birthtime,
        permissions: stats.mode
      };
      
      // Calculer le hash si demandé
      if (options.calculateHashes) {
        fileInfo.hash = await this.calculateFileHash(filePath);
      }
      
      // Enregistrer le fichier
      this.scanResults.files.set(filePath, fileInfo);
      this.config.stats.filesScanned++;
      this.config.stats.totalSize += stats.size;
      
      // Si c'est une application, l'enregistrer séparément
      if (this.isApplication(fileExt, filePath)) {
        await this.scanApplication(filePath, fileInfo);
      }
      
      this.emit('fileScanned', fileInfo);
      
    } catch (error) {
      console.log(`❌ Erreur scan fichier ${filePath}: ${error.message}`);
    }
  }

  // 📱 SCANNER UNE APPLICATION AVEC APPRENTISSAGE AUTOMATIQUE
  async scanApplication(appPath, fileInfo) {
    try {
      const appInfo = {
        ...fileInfo,
        isApplication: true,
        platform: os.platform(),
        learningData: {
          canLaunch: true,
          hasBeenUsed: false,
          lastUsed: null,
          usageCount: 0,
          capabilities: [],
          fileTypes: [],
          shortcuts: [],
          documentation: null
        }
      };

      // Informations spécifiques selon la plateforme
      switch (os.platform()) {
        case 'darwin': // macOS
          if (appPath.endsWith('.app')) {
            appInfo.bundleId = await this.getMacAppBundleId(appPath);
            appInfo.version = await this.getMacAppVersion(appPath);
            appInfo.learningData.capabilities = await this.analyzeMacAppCapabilities(appPath);
          }
          break;
        case 'win32': // Windows
          if (appPath.endsWith('.exe')) {
            appInfo.version = await this.getWindowsAppVersion(appPath);
            appInfo.learningData.capabilities = await this.analyzeWindowsAppCapabilities(appPath);
          }
          break;
      }

      // 🧠 APPRENTISSAGE AUTOMATIQUE DES CAPACITÉS
      await this.learnApplicationCapabilities(appInfo);

      this.scanResults.applications.set(appPath, appInfo);
      this.config.stats.applicationsFound++;

      this.emit('applicationFound', appInfo);

    } catch (error) {
      console.log(`❌ Erreur scan application ${appPath}: ${error.message}`);
    }
  }

  // 🧠 APPRENTISSAGE AUTOMATIQUE DES CAPACITÉS D'APPLICATION
  async learnApplicationCapabilities(appInfo) {
    try {
      const appName = appInfo.name.toLowerCase();

      // Base de connaissances des applications communes
      const knownApps = {
        'chrome': {
          capabilities: ['web_browsing', 'javascript', 'extensions', 'bookmarks'],
          fileTypes: ['.html', '.htm', '.pdf'],
          shortcuts: ['Cmd+T', 'Cmd+W', 'Cmd+R'],
          category: 'browser'
        },
        'safari': {
          capabilities: ['web_browsing', 'reading_list', 'icloud_sync'],
          fileTypes: ['.html', '.htm', '.pdf'],
          shortcuts: ['Cmd+T', 'Cmd+W', 'Cmd+R'],
          category: 'browser'
        },
        'firefox': {
          capabilities: ['web_browsing', 'addons', 'privacy_mode'],
          fileTypes: ['.html', '.htm', '.pdf'],
          shortcuts: ['Cmd+T', 'Cmd+W', 'Cmd+R'],
          category: 'browser'
        },
        'vscode': {
          capabilities: ['code_editing', 'debugging', 'git_integration', 'extensions'],
          fileTypes: ['.js', '.py', '.html', '.css', '.json', '.md'],
          shortcuts: ['Cmd+P', 'Cmd+Shift+P', 'Cmd+S'],
          category: 'development'
        },
        'photoshop': {
          capabilities: ['image_editing', 'layers', 'filters', 'color_correction'],
          fileTypes: ['.psd', '.jpg', '.png', '.gif'],
          shortcuts: ['Cmd+Z', 'Cmd+S', 'Cmd+N'],
          category: 'graphics'
        },
        'word': {
          capabilities: ['document_editing', 'formatting', 'spell_check', 'collaboration'],
          fileTypes: ['.docx', '.doc', '.pdf'],
          shortcuts: ['Cmd+S', 'Cmd+B', 'Cmd+I'],
          category: 'office'
        },
        'excel': {
          capabilities: ['spreadsheet', 'formulas', 'charts', 'data_analysis'],
          fileTypes: ['.xlsx', '.xls', '.csv'],
          shortcuts: ['Cmd+S', 'Cmd+C', 'Cmd+V'],
          category: 'office'
        },
        'finder': {
          capabilities: ['file_management', 'search', 'preview', 'organization'],
          fileTypes: ['*'],
          shortcuts: ['Cmd+F', 'Cmd+N', 'Space'],
          category: 'system'
        },
        'terminal': {
          capabilities: ['command_line', 'scripting', 'system_administration'],
          fileTypes: ['.sh', '.bash', '.zsh'],
          shortcuts: ['Cmd+T', 'Cmd+K', 'Ctrl+C'],
          category: 'development'
        }
      };

      // Recherche par nom exact ou partiel
      let matchedApp = null;
      for (const [key, data] of Object.entries(knownApps)) {
        if (appName.includes(key) || key.includes(appName)) {
          matchedApp = data;
          break;
        }
      }

      if (matchedApp) {
        appInfo.learningData.capabilities = matchedApp.capabilities;
        appInfo.learningData.fileTypes = matchedApp.fileTypes;
        appInfo.learningData.shortcuts = matchedApp.shortcuts;
        appInfo.learningData.category = matchedApp.category;
        appInfo.learningData.hasLearningData = true;
      } else {
        // Apprentissage basé sur l'extension et le nom
        appInfo.learningData.capabilities = this.inferCapabilitiesFromName(appName);
        appInfo.learningData.category = this.inferCategoryFromName(appName);
        appInfo.learningData.hasLearningData = false;
      }

      console.log(`🧠 Apprentissage: ${appInfo.name} - ${appInfo.learningData.capabilities.length} capacités détectées`);

    } catch (error) {
      console.log(`❌ Erreur apprentissage ${appInfo.name}: ${error.message}`);
    }
  }

  // 🔍 INFÉRER LES CAPACITÉS À PARTIR DU NOM
  inferCapabilitiesFromName(appName) {
    const capabilities = [];

    // Mots-clés pour détecter les capacités
    const keywords = {
      'edit': ['editing', 'text_editing'],
      'photo': ['image_editing', 'photo_manipulation'],
      'video': ['video_editing', 'media_playback'],
      'music': ['audio_playback', 'music_management'],
      'mail': ['email', 'communication'],
      'chat': ['messaging', 'communication'],
      'game': ['gaming', 'entertainment'],
      'calculator': ['calculation', 'math'],
      'calendar': ['scheduling', 'time_management'],
      'note': ['note_taking', 'text_editing'],
      'pdf': ['document_viewing', 'pdf_handling'],
      'zip': ['compression', 'archive_management'],
      'clean': ['system_maintenance', 'optimization'],
      'monitor': ['system_monitoring', 'performance'],
      'backup': ['data_backup', 'file_management']
    };

    for (const [keyword, caps] of Object.entries(keywords)) {
      if (appName.includes(keyword)) {
        capabilities.push(...caps);
      }
    }

    return capabilities.length > 0 ? capabilities : ['general_application'];
  }

  // 📂 INFÉRER LA CATÉGORIE À PARTIR DU NOM
  inferCategoryFromName(appName) {
    const categories = {
      'system': ['finder', 'terminal', 'activity', 'console', 'disk', 'system'],
      'development': ['code', 'xcode', 'git', 'terminal', 'sublime', 'atom'],
      'graphics': ['photoshop', 'illustrator', 'sketch', 'gimp', 'paint'],
      'office': ['word', 'excel', 'powerpoint', 'pages', 'numbers', 'keynote'],
      'browser': ['chrome', 'safari', 'firefox', 'edge', 'opera'],
      'media': ['vlc', 'quicktime', 'spotify', 'itunes', 'photos'],
      'communication': ['mail', 'messages', 'slack', 'discord', 'zoom'],
      'utility': ['calculator', 'calendar', 'notes', 'preview', 'archive']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => appName.includes(keyword))) {
        return category;
      }
    }

    return 'other';
  }

  // 🍎 OBTENIR L'ID BUNDLE MACOS
  async getMacAppBundleId(appPath) {
    try {
      const plistPath = path.join(appPath, 'Contents', 'Info.plist');
      const plistContent = await fs.readFile(plistPath, 'utf8');
      
      // Extraction simple du bundle ID (parsing XML basique)
      const bundleIdMatch = plistContent.match(/<key>CFBundleIdentifier<\/key>\s*<string>([^<]+)<\/string>/);
      return bundleIdMatch ? bundleIdMatch[1] : null;
      
    } catch {
      return null;
    }
  }

  // 🍎 OBTENIR LA VERSION MACOS
  async getMacAppVersion(appPath) {
    try {
      const plistPath = path.join(appPath, 'Contents', 'Info.plist');
      const plistContent = await fs.readFile(plistPath, 'utf8');
      
      // Extraction simple de la version
      const versionMatch = plistContent.match(/<key>CFBundleShortVersionString<\/key>\s*<string>([^<]+)<\/string>/);
      return versionMatch ? versionMatch[1] : null;
      
    } catch {
      return null;
    }
  }

  // 🪟 OBTENIR LA VERSION WINDOWS
  async getWindowsAppVersion(appPath) {
    try {
      // Utiliser une commande PowerShell pour obtenir la version
      const { exec } = require('child_process');
      
      return new Promise((resolve) => {
        exec(`powershell "(Get-ItemProperty '${appPath}').VersionInfo.FileVersion"`, (error, stdout) => {
          if (error) {
            resolve(null);
          } else {
            resolve(stdout.trim() || null);
          }
        });
      });
      
    } catch {
      return null;
    }
  }

  // 🔍 DÉTECTER LES DOUBLONS
  async detectDuplicates() {
    try {
      console.log('🔍 Détection des doublons...');
      
      const hashGroups = new Map();
      
      // Grouper les fichiers par hash
      for (const [filePath, fileInfo] of this.scanResults.files) {
        if (fileInfo.hash) {
          if (!hashGroups.has(fileInfo.hash)) {
            hashGroups.set(fileInfo.hash, []);
          }
          hashGroups.get(fileInfo.hash).push({ path: filePath, ...fileInfo });
        }
      }
      
      // Identifier les doublons
      for (const [hash, files] of hashGroups) {
        if (files.length > 1) {
          this.scanResults.duplicates.set(hash, {
            hash,
            files,
            count: files.length,
            totalSize: files[0].size * files.length,
            wastedSpace: files[0].size * (files.length - 1)
          });
        }
      }
      
      console.log(`🔍 ${this.scanResults.duplicates.size} groupes de doublons trouvés`);
      
    } catch (error) {
      console.error(`❌ Erreur détection doublons: ${error.message}`);
    }
  }

  // 🔐 CALCULER LE HASH D'UN FICHIER
  async calculateFileHash(filePath) {
    try {
      const fileBuffer = await fs.readFile(filePath);
      return crypto.createHash('md5').update(fileBuffer).digest('hex');
    } catch {
      return null;
    }
  }

  // 📋 OBTENIR LE TYPE DE FICHIER
  getFileType(extension) {
    for (const [type, extensions] of Object.entries(this.config.fileTypes)) {
      if (extensions.includes(extension)) {
        return type;
      }
    }
    return 'other';
  }

  // 📱 VÉRIFIER SI C'EST UNE APPLICATION
  isApplication(extension, filePath) {
    return this.config.fileTypes.applications.includes(extension) ||
           filePath.includes('/Applications/') ||
           filePath.includes('Program Files');
  }

  // 🔍 RECHERCHER DES FICHIERS
  searchFiles(query, options = {}) {
    const results = [];
    const lowerQuery = query.toLowerCase();
    
    for (const [filePath, fileInfo] of this.scanResults.files) {
      const matches = 
        fileInfo.name.toLowerCase().includes(lowerQuery) ||
        filePath.toLowerCase().includes(lowerQuery) ||
        (options.searchContent && fileInfo.type === 'documents');
      
      if (matches) {
        results.push(fileInfo);
      }
    }
    
    return results.slice(0, options.limit || 50);
  }

  // 📱 RECHERCHER DES APPLICATIONS
  searchApplications(query) {
    const results = [];
    const lowerQuery = query.toLowerCase();
    
    for (const [appPath, appInfo] of this.scanResults.applications) {
      if (appInfo.name.toLowerCase().includes(lowerQuery)) {
        results.push(appInfo);
      }
    }
    
    return results;
  }

  // 📊 OBTENIR LE RÉSUMÉ DU SCAN
  getScanSummary() {
    const totalFiles = this.scanResults.files.size;
    const totalApps = this.scanResults.applications.size;
    const totalDirs = this.scanResults.directories.size;
    const totalDuplicates = this.scanResults.duplicates.size;
    
    // Statistiques par type de fichier
    const fileTypeStats = {};
    for (const [type] of Object.entries(this.config.fileTypes)) {
      fileTypeStats[type] = 0;
    }
    fileTypeStats.other = 0;
    
    for (const [, fileInfo] of this.scanResults.files) {
      fileTypeStats[fileInfo.type]++;
    }
    
    return {
      success: true,
      summary: {
        totalFiles,
        totalApplications: totalApps,
        totalDirectories: totalDirs,
        totalDuplicates,
        totalSize: this.formatFileSize(this.config.stats.totalSize),
        scanDuration: this.config.stats.scanTime,
        fileTypeBreakdown: fileTypeStats
      },
      stats: this.config.stats
    };
  }

  // 📊 OBTENIR LES STATISTIQUES
  getStats() {
    return {
      ...this.config.stats,
      cacheSize: this.scanCache.size,
      lastScan: this.scanResults.files.size > 0 ? 'Completed' : 'Never'
    };
  }

  // 📁 OBTENIR LES RÉSULTATS
  getResults(type = 'all') {
    switch (type) {
      case 'files':
        return Array.from(this.scanResults.files.values());
      case 'applications':
        return Array.from(this.scanResults.applications.values());
      case 'directories':
        return Array.from(this.scanResults.directories.values());
      case 'duplicates':
        return Array.from(this.scanResults.duplicates.values());
      default:
        return {
          files: Array.from(this.scanResults.files.values()),
          applications: Array.from(this.scanResults.applications.values()),
          directories: Array.from(this.scanResults.directories.values()),
          duplicates: Array.from(this.scanResults.duplicates.values())
        };
    }
  }

  // 📏 FORMATER LA TAILLE DE FICHIER
  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // 🧹 NETTOYER LE CACHE
  clearCache() {
    this.scanCache.clear();
    console.log('🧹 Cache de scan nettoyé');
  }

  // 🔄 RAFRAÎCHIR LE SCAN
  async refreshScan() {
    this.clearCache();
    return await this.performFullScan();
  }
}

module.exports = FileScannerSystem;
