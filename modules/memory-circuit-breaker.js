/**
 * ⚡ SYSTÈME DE COUPURE MÉMOIRE (CIRCUIT BREAKER)
 * Coupe automatiquement la mémoire en cas de surcharge
 */

const EventEmitter = require('events');

class MemoryCircuitBreaker extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      thresholds: {
        memoryUsage: 1500, // 1.5GB en MB
        neuronCount: 1500, // Nombre max de neurones
        synapseCount: 15000, // Nombre max de synapses
        memoryEntries: 800, // Nombre max d'entrées mémoire
        responseTime: 20000 // 20 secondes max
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 3, // 3 échecs avant ouverture
        recoveryTimeout: 30000, // 30 secondes avant tentative de récupération
        halfOpenMaxCalls: 5 // 5 appels max en mode semi-ouvert
      },
      states: {
        CLOSED: 'closed', // Normal
        OPEN: 'open', // Coupé
        HALF_OPEN: 'half_open' // Test de récupération
      },
      stats: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        circuitOpens: 0,
        memoryClears: 0,
        neuronReductions: 0
      }
    };
    
    this.state = this.config.states.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.halfOpenCalls = 0;
    
    this.logger = console;
    
    this.init();
  }

  async init() {
    console.log('⚡ Initialisation du système de coupure mémoire...');
    
    // Démarrer la surveillance automatique
    this.startMonitoring();
    
    console.log('✅ Système de coupure mémoire initialisé');
  }

  // 🔍 DÉMARRER LA SURVEILLANCE
  startMonitoring() {
    setInterval(async () => {
      await this.checkMemoryHealth();
    }, 5000); // Vérification toutes les 5 secondes
    
    console.log('🔍 Surveillance mémoire démarrée');
  }

  // 🏥 VÉRIFIER LA SANTÉ MÉMOIRE
  async checkMemoryHealth() {
    try {
      this.config.stats.totalRequests++;
      
      const memoryMetrics = await this.getMemoryMetrics();
      
      // Vérifier si les seuils sont dépassés
      const isOverloaded = this.isMemoryOverloaded(memoryMetrics);
      
      if (isOverloaded) {
        await this.handleMemoryOverload(memoryMetrics);
      } else {
        await this.handleMemorySuccess();
      }
      
      this.emit('memoryHealthChecked', {
        metrics: memoryMetrics,
        state: this.state,
        isOverloaded,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur vérification santé mémoire:', error.message);
      await this.handleMemoryFailure(error);
    }
  }

  // 📊 OBTENIR LES MÉTRIQUES MÉMOIRE
  async getMemoryMetrics() {
    try {
      const metrics = {
        memoryUsage: 0,
        neuronCount: 0,
        synapseCount: 0,
        memoryEntries: 0,
        responseTime: 0
      };
      
      // Obtenir les métriques système
      const used = process.memoryUsage();
      metrics.memoryUsage = Math.round(used.rss / 1024 / 1024); // MB
      
      // Obtenir les métriques de l'agent si disponible
      if (global.artificialBrain) {
        const brainStats = global.artificialBrain.getStats();
        metrics.neuronCount = brainStats.neurons || 0;
        metrics.synapseCount = brainStats.synapses || 0;
      }
      
      if (global.thermalMemory) {
        const memoryStats = global.thermalMemory.getDetailedStats();
        metrics.memoryEntries = memoryStats.totalEntries || 0;
      }
      
      return metrics;
      
    } catch (error) {
      console.error('❌ Erreur obtention métriques mémoire:', error.message);
      
      return {
        memoryUsage: 0,
        neuronCount: 0,
        synapseCount: 0,
        memoryEntries: 0,
        responseTime: 0
      };
    }
  }

  // 🚨 VÉRIFIER SI LA MÉMOIRE EST SURCHARGÉE
  isMemoryOverloaded(metrics) {
    const overloads = [];
    
    if (metrics.memoryUsage > this.config.thresholds.memoryUsage) {
      overloads.push(`Mémoire: ${metrics.memoryUsage}MB > ${this.config.thresholds.memoryUsage}MB`);
    }
    
    if (metrics.neuronCount > this.config.thresholds.neuronCount) {
      overloads.push(`Neurones: ${metrics.neuronCount} > ${this.config.thresholds.neuronCount}`);
    }
    
    if (metrics.synapseCount > this.config.thresholds.synapseCount) {
      overloads.push(`Synapses: ${metrics.synapseCount} > ${this.config.thresholds.synapseCount}`);
    }
    
    if (metrics.memoryEntries > this.config.thresholds.memoryEntries) {
      overloads.push(`Entrées mémoire: ${metrics.memoryEntries} > ${this.config.thresholds.memoryEntries}`);
    }
    
    if (overloads.length > 0) {
      console.log(`⚠️ SURCHARGE DÉTECTÉE: ${overloads.join(', ')}`);
      return true;
    }
    
    return false;
  }

  // 🚨 GÉRER UNE SURCHARGE MÉMOIRE
  async handleMemoryOverload(metrics) {
    try {
      this.config.stats.failedRequests++;
      this.failureCount++;
      this.lastFailureTime = Date.now();
      
      console.log(`🚨 SURCHARGE MÉMOIRE DÉTECTÉE (${this.failureCount}/${this.config.circuitBreaker.failureThreshold})`);
      
      // Déclencher des actions correctives immédiates
      await this.performEmergencyCleanup(metrics);
      
      // Vérifier si le circuit breaker doit s'ouvrir
      if (this.failureCount >= this.config.circuitBreaker.failureThreshold && 
          this.state === this.config.states.CLOSED) {
        await this.openCircuit();
      }
      
      this.emit('memoryOverload', {
        metrics,
        failureCount: this.failureCount,
        state: this.state,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur gestion surcharge mémoire:', error.message);
    }
  }

  // ✅ GÉRER UN SUCCÈS MÉMOIRE
  async handleMemorySuccess() {
    try {
      this.config.stats.successfulRequests++;
      
      // Si on était en mode semi-ouvert, compter les succès
      if (this.state === this.config.states.HALF_OPEN) {
        this.halfOpenCalls++;
        
        // Si assez de succès, fermer le circuit
        if (this.halfOpenCalls >= this.config.circuitBreaker.halfOpenMaxCalls) {
          await this.closeCircuit();
        }
      }
      
    } catch (error) {
      console.error('❌ Erreur gestion succès mémoire:', error.message);
    }
  }

  // ❌ GÉRER UN ÉCHEC MÉMOIRE
  async handleMemoryFailure(error) {
    try {
      this.config.stats.failedRequests++;
      this.failureCount++;
      this.lastFailureTime = Date.now();
      
      console.log(`❌ ÉCHEC MÉMOIRE: ${error.message}`);
      
      // Si on était en mode semi-ouvert, rouvrir le circuit
      if (this.state === this.config.states.HALF_OPEN) {
        await this.openCircuit();
      }
      
    } catch (err) {
      console.error('❌ Erreur gestion échec mémoire:', err.message);
    }
  }

  // 🚨 OUVRIR LE CIRCUIT (COUPURE)
  async openCircuit() {
    try {
      this.state = this.config.states.OPEN;
      this.config.stats.circuitOpens++;
      
      console.log('🚨 CIRCUIT BREAKER OUVERT - COUPURE MÉMOIRE ACTIVÉE');
      
      // Programmer la tentative de récupération
      setTimeout(async () => {
        await this.attemptRecovery();
      }, this.config.circuitBreaker.recoveryTimeout);
      
      this.emit('circuitOpened', {
        failureCount: this.failureCount,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur ouverture circuit:', error.message);
    }
  }

  // 🔄 FERMER LE CIRCUIT (RÉCUPÉRATION)
  async closeCircuit() {
    try {
      this.state = this.config.states.CLOSED;
      this.failureCount = 0;
      this.halfOpenCalls = 0;
      
      console.log('✅ CIRCUIT BREAKER FERMÉ - RÉCUPÉRATION RÉUSSIE');
      
      this.emit('circuitClosed', {
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur fermeture circuit:', error.message);
    }
  }

  // 🔄 TENTATIVE DE RÉCUPÉRATION
  async attemptRecovery() {
    try {
      if (this.state === this.config.states.OPEN) {
        this.state = this.config.states.HALF_OPEN;
        this.halfOpenCalls = 0;
        
        console.log('🔄 TENTATIVE DE RÉCUPÉRATION - MODE SEMI-OUVERT');
        
        this.emit('recoveryAttempt', {
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error) {
      console.error('❌ Erreur tentative récupération:', error.message);
    }
  }

  // 🧹 NETTOYAGE D'URGENCE
  async performEmergencyCleanup(metrics) {
    try {
      console.log('🧹 NETTOYAGE D\'URGENCE EN COURS...');
      
      const actions = [];
      
      // Nettoyer la mémoire thermique si surchargée
      if (metrics.memoryEntries > this.config.thresholds.memoryEntries) {
        await this.clearThermalMemory();
        actions.push('thermal_memory_cleared');
      }
      
      // Réduire les neurones si trop nombreux
      if (metrics.neuronCount > this.config.thresholds.neuronCount) {
        await this.reduceNeurons();
        actions.push('neurons_reduced');
      }
      
      // Réduire les synapses si trop nombreuses
      if (metrics.synapseCount > this.config.thresholds.synapseCount) {
        await this.reduceSynapses();
        actions.push('synapses_reduced');
      }
      
      // Forcer le garbage collection
      if (global.gc) {
        global.gc();
        actions.push('garbage_collection');
      }
      
      console.log(`✅ Nettoyage d'urgence terminé: ${actions.join(', ')}`);
      
      this.emit('emergencyCleanup', {
        metrics,
        actions,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur nettoyage d\'urgence:', error.message);
    }
  }

  // 🌡️ NETTOYER LA MÉMOIRE THERMIQUE
  async clearThermalMemory() {
    try {
      if (global.thermalMemory) {
        // Garder seulement les 100 entrées les plus importantes
        const stats = global.thermalMemory.getDetailedStats();
        const entriesToRemove = Math.max(0, stats.totalEntries - 100);
        
        if (entriesToRemove > 0) {
          // Simulation de nettoyage (à implémenter dans thermalMemory)
          console.log(`🌡️ ${entriesToRemove} entrées mémoire thermique supprimées`);
          this.config.stats.memoryClears++;
        }
      }
      
    } catch (error) {
      console.error('❌ Erreur nettoyage mémoire thermique:', error.message);
    }
  }

  // 🧠 RÉDUIRE LES NEURONES
  async reduceNeurons() {
    try {
      if (global.artificialBrain) {
        // Simulation de réduction de neurones (à implémenter dans artificialBrain)
        console.log('🧠 Neurones réduits de 20%');
        this.config.stats.neuronReductions++;
      }
      
    } catch (error) {
      console.error('❌ Erreur réduction neurones:', error.message);
    }
  }

  // 🔗 RÉDUIRE LES SYNAPSES
  async reduceSynapses() {
    try {
      if (global.artificialBrain) {
        // Simulation de réduction de synapses (à implémenter dans artificialBrain)
        console.log('🔗 Synapses réduites de 15%');
      }
      
    } catch (error) {
      console.error('❌ Erreur réduction synapses:', error.message);
    }
  }

  // 🚫 VÉRIFIER SI L'OPÉRATION EST AUTORISÉE
  isOperationAllowed() {
    if (!this.config.circuitBreaker.enabled) {
      return true;
    }
    
    return this.state !== this.config.states.OPEN;
  }

  // 📊 OBTENIR LES STATISTIQUES
  getStats() {
    return {
      ...this.config.stats,
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      halfOpenCalls: this.halfOpenCalls,
      isEnabled: this.config.circuitBreaker.enabled
    };
  }

  // ⚙️ OBTENIR LA CONFIGURATION
  getConfig() {
    return {
      thresholds: this.config.thresholds,
      circuitBreaker: this.config.circuitBreaker
    };
  }

  // 🔧 METTRE À JOUR LA CONFIGURATION
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
    
    console.log('🔧 Configuration circuit breaker mise à jour');
    
    this.emit('configUpdated', {
      config: this.config,
      timestamp: new Date().toISOString()
    });
  }

  // 🔄 RÉINITIALISER LE CIRCUIT BREAKER
  reset() {
    this.state = this.config.states.CLOSED;
    this.failureCount = 0;
    this.halfOpenCalls = 0;
    this.lastFailureTime = null;
    
    console.log('🔄 Circuit breaker réinitialisé');
    
    this.emit('circuitReset', {
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = MemoryCircuitBreaker;
