/**
 * Surveillance système pour éviter les crashes
 */

const { getLogger } = require('../utils/logger');
const EventEmitter = require('events');

class SystemMonitor extends EventEmitter {
    constructor() {
        super();
        this.logger = getLogger('SYSTEM_MONITOR');
        
        // Seuils de sécurité (AUGMENTÉS POUR ÉVITER LES FAUSSES ALERTES)
        this.safetyThresholds = {
            maxNeurons: 2000,           // Augmenté: Maximum 2000 neurones
            maxSynapses: 15000,         // Augmenté: Maximum 15000 synapses
            maxTemperature: 120,        // Augmenté: Maximum 120°C
            maxMemoryEntries: 5000,     // Augmenté: Maximum 5000 entrées mémoire
            maxCPUUsage: 95,            // Augmenté: Maximum 95% CPU
            maxMemoryUsage: 90          // Augmenté: Maximum 90% RAM
        };
        
        // État du système
        this.systemState = {
            isStable: true,
            lastCheck: null,
            warnings: [],
            emergencyMode: false
        };
        
        // Intervalle de surveillance
        this.monitoringInterval = null;
        
        this.logger.info('Surveillance système initialisée', {
            component: 'SYSTEM_MONITOR',
            thresholds: this.safetyThresholds
        });
    }
    
    /**
     * Démarre la surveillance système
     */
    startMonitoring() {
        if (this.monitoringInterval) {
            this.logger.warn('Surveillance déjà active');
            return;
        }
        
        this.logger.info('🔍 DÉMARRAGE SURVEILLANCE SYSTÈME');
        
        this.monitoringInterval = setInterval(() => {
            this.performSystemCheck();
        }, 30000); // Vérification toutes les 30 secondes
    }
    
    /**
     * Arrête la surveillance système
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            this.logger.info('🛑 Surveillance système arrêtée');
        }
    }
    
    /**
     * Effectue une vérification système complète
     */
    performSystemCheck() {
        try {
            this.systemState.lastCheck = new Date().toISOString();
            this.systemState.warnings = [];
            
            // Vérifier le cerveau artificiel
            this.checkBrainHealth();
            
            // Vérifier la mémoire thermique
            this.checkThermalMemory();
            
            // Vérifier les ressources système
            this.checkSystemResources();
            
            // Déterminer l'état global
            this.updateSystemState();
            
            // Émettre les alertes si nécessaire
            if (this.systemState.warnings.length > 0) {
                this.emit('systemWarning', {
                    warnings: this.systemState.warnings,
                    timestamp: this.systemState.lastCheck
                });
            }
            
            if (!this.systemState.isStable) {
                this.emit('systemUnstable', {
                    state: this.systemState,
                    timestamp: this.systemState.lastCheck
                });
            }
            
        } catch (error) {
            this.logger.error('Erreur surveillance système', {
                component: 'SYSTEM_MONITOR',
                error: error.message
            });
        }
    }
    
    /**
     * Vérifie la santé du cerveau artificiel
     */
    checkBrainHealth() {
        if (!global.artificialBrain) return;
        
        const brain = global.artificialBrain;
        
        // Vérifier le nombre de neurones
        if (brain.activeNeurons > this.safetyThresholds.maxNeurons) {
            this.systemState.warnings.push({
                type: 'BRAIN_OVERLOAD',
                message: `Trop de neurones actifs: ${brain.activeNeurons}/${this.safetyThresholds.maxNeurons}`,
                severity: 'HIGH',
                action: 'Arrêter la génération de neurones'
            });
        }
        
        // Vérifier le nombre de synapses
        if (brain.synapticConnections > this.safetyThresholds.maxSynapses) {
            this.systemState.warnings.push({
                type: 'SYNAPTIC_OVERLOAD',
                message: `Trop de connexions synaptiques: ${brain.synapticConnections}/${this.safetyThresholds.maxSynapses}`,
                severity: 'HIGH',
                action: 'Limiter la croissance synaptique'
            });
        }
        
        // Vérifier la densité neuronale (SEUIL AUGMENTÉ)
        const density = brain.synapticConnections / brain.activeNeurons;
        if (density > 25) { // Augmenté de 10 → 25
            this.systemState.warnings.push({
                type: 'NEURAL_DENSITY_HIGH',
                message: `Densité neuronale élevée: ${density.toFixed(2)}`,
                severity: 'MEDIUM',
                action: 'Optimiser les connexions'
            });
        }
    }
    
    /**
     * Vérifie la mémoire thermique
     */
    checkThermalMemory() {
        if (!global.thermalMemory) return;
        
        try {
            const stats = global.thermalMemory.getDetailedStats();
            
            // Vérifier la température
            if (stats.globalTemperature > this.safetyThresholds.maxTemperature) {
                this.systemState.warnings.push({
                    type: 'THERMAL_OVERLOAD',
                    message: `Température trop élevée: ${stats.globalTemperature.toFixed(1)}°C/${this.safetyThresholds.maxTemperature}°C`,
                    severity: 'CRITICAL',
                    action: 'Refroidissement d\'urgence requis'
                });
            }
            
            // Vérifier le nombre d'entrées
            if (stats.activeEntries > this.safetyThresholds.maxMemoryEntries) {
                this.systemState.warnings.push({
                    type: 'MEMORY_OVERLOAD',
                    message: `Trop d\'entrées mémoire: ${stats.activeEntries}/${this.safetyThresholds.maxMemoryEntries}`,
                    severity: 'MEDIUM',
                    action: 'Nettoyer la mémoire thermique'
                });
            }
            
        } catch (error) {
            this.systemState.warnings.push({
                type: 'THERMAL_ERROR',
                message: `Erreur mémoire thermique: ${error.message}`,
                severity: 'HIGH',
                action: 'Redémarrer la mémoire thermique'
            });
        }
    }
    
    /**
     * Vérifie les ressources système
     */
    checkSystemResources() {
        try {
            const memUsage = process.memoryUsage();
            const memUsedMB = Math.round(memUsage.rss / 1024 / 1024);
            
            // Vérifier l'utilisation mémoire (approximative)
            if (memUsedMB > 2000) { // Plus de 2GB
                this.systemState.warnings.push({
                    type: 'MEMORY_USAGE_HIGH',
                    message: `Utilisation mémoire élevée: ${memUsedMB}MB`,
                    severity: 'MEDIUM',
                    action: 'Optimiser l\'utilisation mémoire'
                });
            }
            
            // Vérifier les handles ouverts
            if (process._getActiveHandles().length > 100) {
                this.systemState.warnings.push({
                    type: 'HANDLE_LEAK',
                    message: `Trop de handles ouverts: ${process._getActiveHandles().length}`,
                    severity: 'MEDIUM',
                    action: 'Vérifier les fuites de ressources'
                });
            }
            
        } catch (error) {
            this.logger.warn('Impossible de vérifier les ressources système', {
                error: error.message
            });
        }
    }
    
    /**
     * Met à jour l'état global du système
     */
    updateSystemState() {
        const criticalWarnings = this.systemState.warnings.filter(w => w.severity === 'CRITICAL');
        const highWarnings = this.systemState.warnings.filter(w => w.severity === 'HIGH');
        
        // Mode d'urgence si alertes critiques
        if (criticalWarnings.length > 0) {
            this.systemState.emergencyMode = true;
            this.systemState.isStable = false;
            this.logger.error('🚨 MODE D\'URGENCE ACTIVÉ', {
                criticalWarnings: criticalWarnings.length
            });
        }
        // Système instable si alertes élevées
        else if (highWarnings.length > 2) {
            this.systemState.isStable = false;
            this.systemState.emergencyMode = false;
            this.logger.warn('⚠️ SYSTÈME INSTABLE', {
                highWarnings: highWarnings.length
            });
        }
        // Système stable
        else {
            this.systemState.isStable = true;
            this.systemState.emergencyMode = false;
        }
    }
    
    /**
     * Force l'arrêt d'urgence des processus dangereux
     */
    emergencyShutdown() {
        this.logger.error('🚨 ARRÊT D\'URGENCE SYSTÈME');
        
        // Arrêter la formation accélérée
        if (global.acceleratedTraining) {
            try {
                global.acceleratedTraining.stopContinuousTraining();
                this.logger.info('✅ Formation accélérée arrêtée');
            } catch (error) {
                this.logger.error('Erreur arrêt formation', { error: error.message });
            }
        }
        
        // Refroidir la mémoire thermique
        if (global.thermalMemory) {
            try {
                // Ajouter des entrées froides pour refroidir
                for (let i = 0; i < 5; i++) {
                    global.thermalMemory.add('emergency_cooling', 'Refroidissement d\'urgence', 0.1, 'emergency');
                }
                this.logger.info('✅ Refroidissement d\'urgence appliqué');
            } catch (error) {
                this.logger.error('Erreur refroidissement', { error: error.message });
            }
        }
        
        this.emit('emergencyShutdown', {
            timestamp: new Date().toISOString(),
            reason: 'Seuils critiques dépassés'
        });
    }
    
    /**
     * Obtient l'état actuel du système
     */
    getSystemState() {
        return {
            ...this.systemState,
            thresholds: this.safetyThresholds,
            monitoring: !!this.monitoringInterval
        };
    }
}

module.exports = SystemMonitor;
