/**
 * 🧠 LOUNA AI - SYSTÈME DE TEST DE QI COMPLET
 * Test des capacités cognitives réelles de LOUNA AI
 * Basé sur des métriques système authentiques
 */

class IQTestSystem {
    constructor() {
        this.version = '1.0.0';
        this.testStartTime = null;
        this.testResults = {
            logicalReasoning: 0,
            patternRecognition: 0,
            memoryCapacity: 0,
            processingSpeed: 0,
            verbalComprehension: 0,
            spatialIntelligence: 0,
            mathematicalReasoning: 0,
            adaptiveThinking: 0
        };
        this.detailedResults = [];
        this.currentTest = null;
        
        console.log('🧠 Système de test de QI initialisé pour LOUNA AI');
    }

    // 🚀 DÉMARRER LE TEST DE QI COMPLET
    async startCompleteIQTest() {
        console.log('🧠 DÉMARRAGE DU TEST DE QI COMPLET POUR LOUNA AI');
        console.log('=' .repeat(60));
        
        this.testStartTime = Date.now();
        
        try {
            // Test 1: Raisonnement logique
            await this.testLogicalReasoning();
            
            // Test 2: Reconnaissance de motifs
            await this.testPatternRecognition();
            
            // Test 3: Capacité mémoire
            await this.testMemoryCapacity();
            
            // Test 4: Vitesse de traitement
            await this.testProcessingSpeed();
            
            // Test 5: Compréhension verbale
            await this.testVerbalComprehension();
            
            // Test 6: Intelligence spatiale
            await this.testSpatialIntelligence();
            
            // Test 7: Raisonnement mathématique
            await this.testMathematicalReasoning();
            
            // Test 8: Pensée adaptative
            await this.testAdaptiveThinking();
            
            // Calculer le QI final
            const finalIQ = this.calculateFinalIQ();
            
            // Générer le rapport
            const report = this.generateIQReport(finalIQ);
            
            return report;
            
        } catch (error) {
            console.error('❌ Erreur test QI:', error);
            return { error: error.message };
        }
    }

    // 🧮 TEST 1: RAISONNEMENT LOGIQUE
    async testLogicalReasoning() {
        console.log('\n🧮 Test 1: Raisonnement Logique...');
        
        const questions = [
            {
                question: "Si A > B et B > C, alors A ? C",
                options: ["<", ">", "=", "?"],
                correct: ">",
                difficulty: 1
            },
            {
                question: "Tous les X sont Y. Tous les Y sont Z. Donc tous les X sont ?",
                options: ["Y", "Z", "X", "Rien"],
                correct: "Z",
                difficulty: 2
            },
            {
                question: "Si P alors Q. Si Q alors R. Si P, alors ?",
                options: ["P", "Q", "R", "Rien"],
                correct: "R",
                difficulty: 3
            }
        ];
        
        let score = 0;
        const startTime = Date.now();
        
        for (const q of questions) {
            const response = await this.askLOUNA(q.question, q.options);
            const isCorrect = this.evaluateResponse(response, q.correct);
            
            if (isCorrect) {
                score += (4 - q.difficulty) * 25; // Points basés sur difficulté
            }
            
            this.detailedResults.push({
                test: 'Raisonnement Logique',
                question: q.question,
                response: response,
                correct: q.correct,
                isCorrect: isCorrect,
                difficulty: q.difficulty
            });
        }
        
        const responseTime = Date.now() - startTime;
        const timeBonus = Math.max(0, 100 - (responseTime / 1000)); // Bonus vitesse
        
        this.testResults.logicalReasoning = Math.min(100, score + timeBonus);
        console.log(`✅ Raisonnement Logique: ${this.testResults.logicalReasoning.toFixed(1)}/100`);
    }

    // 🔍 TEST 2: RECONNAISSANCE DE MOTIFS
    async testPatternRecognition() {
        console.log('\n🔍 Test 2: Reconnaissance de Motifs...');
        
        const patterns = [
            {
                sequence: "2, 4, 6, 8, ?",
                correct: "10",
                type: "arithmétique",
                difficulty: 1
            },
            {
                sequence: "1, 1, 2, 3, 5, 8, ?",
                correct: "13",
                type: "fibonacci",
                difficulty: 2
            },
            {
                sequence: "A, C, E, G, ?",
                correct: "I",
                type: "alphabétique",
                difficulty: 2
            },
            {
                sequence: "1, 4, 9, 16, 25, ?",
                correct: "36",
                type: "carrés",
                difficulty: 3
            }
        ];
        
        let score = 0;
        const startTime = Date.now();
        
        for (const pattern of patterns) {
            const response = await this.askLOUNA(`Complétez la séquence: ${pattern.sequence}`);
            const isCorrect = this.evaluateResponse(response, pattern.correct);
            
            if (isCorrect) {
                score += (4 - pattern.difficulty) * 20;
            }
            
            this.detailedResults.push({
                test: 'Reconnaissance de Motifs',
                pattern: pattern.sequence,
                response: response,
                correct: pattern.correct,
                isCorrect: isCorrect,
                type: pattern.type
            });
        }
        
        const responseTime = Date.now() - startTime;
        const timeBonus = Math.max(0, 80 - (responseTime / 1500));
        
        this.testResults.patternRecognition = Math.min(100, score + timeBonus);
        console.log(`✅ Reconnaissance de Motifs: ${this.testResults.patternRecognition.toFixed(1)}/100`);
    }

    // 💾 TEST 3: CAPACITÉ MÉMOIRE
    async testMemoryCapacity() {
        console.log('\n💾 Test 3: Capacité Mémoire...');
        
        // Utiliser les vraies métriques mémoire de LOUNA
        const memoryStats = await this.getLOUNAMemoryStats();
        
        let score = 0;
        
        // Test de mémorisation de séquence
        const sequence = "7, 3, 9, 1, 5, 8, 2, 6, 4";
        await this.askLOUNA(`Mémorisez cette séquence: ${sequence}`);
        
        // Attendre 10 secondes
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        const recall = await this.askLOUNA("Répétez la séquence que je vous ai donnée");
        const memoryAccuracy = this.calculateMemoryAccuracy(sequence, recall);
        
        // Score basé sur les vraies métriques mémoire
        const memoryEntries = memoryStats.totalEntries || 0;
        const memoryEfficiency = memoryStats.memoryEfficiency || 0;
        
        score += memoryAccuracy * 40; // 40 points pour la précision
        score += Math.min(30, memoryEntries / 5); // 30 points pour les entrées
        score += memoryEfficiency * 0.3; // 30 points pour l'efficacité
        
        this.testResults.memoryCapacity = Math.min(100, score);
        console.log(`✅ Capacité Mémoire: ${this.testResults.memoryCapacity.toFixed(1)}/100`);
        
        this.detailedResults.push({
            test: 'Capacité Mémoire',
            sequence: sequence,
            recall: recall,
            accuracy: memoryAccuracy,
            memoryEntries: memoryEntries,
            memoryEfficiency: memoryEfficiency
        });
    }

    // ⚡ TEST 4: VITESSE DE TRAITEMENT
    async testProcessingSpeed() {
        console.log('\n⚡ Test 4: Vitesse de Traitement...');
        
        const speedTests = [
            "Combien font 15 + 27?",
            "Quel est le contraire de 'rapide'?",
            "Combien de lettres dans 'intelligence'?",
            "Quelle est la capitale de la France?",
            "Combien font 8 × 7?"
        ];
        
        let totalTime = 0;
        let correctAnswers = 0;
        
        for (const question of speedTests) {
            const startTime = Date.now();
            const response = await this.askLOUNA(question);
            const responseTime = Date.now() - startTime;
            
            totalTime += responseTime;
            
            // Évaluation simple des réponses rapides
            const isCorrect = this.evaluateSpeedResponse(question, response);
            if (isCorrect) correctAnswers++;
            
            this.detailedResults.push({
                test: 'Vitesse de Traitement',
                question: question,
                response: response,
                responseTime: responseTime,
                isCorrect: isCorrect
            });
        }
        
        const averageTime = totalTime / speedTests.length;
        const accuracy = (correctAnswers / speedTests.length) * 100;
        
        // Score basé sur vitesse et précision
        let speedScore = Math.max(0, 100 - (averageTime / 100)); // Moins de temps = plus de points
        let accuracyScore = accuracy;
        
        this.testResults.processingSpeed = (speedScore + accuracyScore) / 2;
        console.log(`✅ Vitesse de Traitement: ${this.testResults.processingSpeed.toFixed(1)}/100`);
    }

    // 📚 TEST 5: COMPRÉHENSION VERBALE
    async testVerbalComprehension() {
        console.log('\n📚 Test 5: Compréhension Verbale...');
        
        const verbalTests = [
            {
                question: "Expliquez la différence entre 'empathie' et 'sympathie'",
                type: "définition",
                difficulty: 2
            },
            {
                question: "Que signifie l'expression 'avoir un chat dans la gorge'?",
                type: "expression",
                difficulty: 2
            },
            {
                question: "Analysez le sens de cette phrase: 'Le silence parlait plus fort que les mots'",
                type: "analyse",
                difficulty: 3
            }
        ];
        
        let score = 0;
        
        for (const test of verbalTests) {
            const response = await this.askLOUNA(test.question);
            const comprehensionScore = this.evaluateVerbalComprehension(test, response);
            
            score += comprehensionScore * (4 - test.difficulty) * 10;
            
            this.detailedResults.push({
                test: 'Compréhension Verbale',
                question: test.question,
                response: response,
                type: test.type,
                score: comprehensionScore
            });
        }
        
        this.testResults.verbalComprehension = Math.min(100, score);
        console.log(`✅ Compréhension Verbale: ${this.testResults.verbalComprehension.toFixed(1)}/100`);
    }

    // 🎯 TEST 6: INTELLIGENCE SPATIALE
    async testSpatialIntelligence() {
        console.log('\n🎯 Test 6: Intelligence Spatiale...');
        
        const spatialTests = [
            "Si vous tournez un cube de 90° vers la droite, quelle face sera visible?",
            "Décrivez comment vous organiseriez spatialement une interface utilisateur",
            "Combien de faces a un dodécaèdre?"
        ];
        
        let score = 0;
        
        for (const question of spatialTests) {
            const response = await this.askLOUNA(question);
            const spatialScore = this.evaluateSpatialResponse(question, response);
            score += spatialScore;
            
            this.detailedResults.push({
                test: 'Intelligence Spatiale',
                question: question,
                response: response,
                score: spatialScore
            });
        }
        
        this.testResults.spatialIntelligence = Math.min(100, score);
        console.log(`✅ Intelligence Spatiale: ${this.testResults.spatialIntelligence.toFixed(1)}/100`);
    }

    // 🔢 TEST 7: RAISONNEMENT MATHÉMATIQUE
    async testMathematicalReasoning() {
        console.log('\n🔢 Test 7: Raisonnement Mathématique...');
        
        const mathTests = [
            {
                problem: "Si un train parcourt 120 km en 2 heures, quelle est sa vitesse moyenne?",
                correct: "60",
                unit: "km/h",
                difficulty: 1
            },
            {
                problem: "Résolvez: 2x + 5 = 13",
                correct: "4",
                unit: "",
                difficulty: 2
            },
            {
                problem: "Quelle est la probabilité d'obtenir deux faces en lançant deux pièces?",
                correct: "0.25",
                unit: "ou 25%",
                difficulty: 3
            }
        ];
        
        let score = 0;
        
        for (const test of mathTests) {
            const response = await this.askLOUNA(test.problem);
            const isCorrect = this.evaluateMathResponse(response, test.correct);
            
            if (isCorrect) {
                score += (4 - test.difficulty) * 25;
            }
            
            this.detailedResults.push({
                test: 'Raisonnement Mathématique',
                problem: test.problem,
                response: response,
                correct: test.correct,
                isCorrect: isCorrect
            });
        }
        
        this.testResults.mathematicalReasoning = Math.min(100, score);
        console.log(`✅ Raisonnement Mathématique: ${this.testResults.mathematicalReasoning.toFixed(1)}/100`);
    }

    // 🔄 TEST 8: PENSÉE ADAPTATIVE
    async testAdaptiveThinking() {
        console.log('\n🔄 Test 8: Pensée Adaptative...');
        
        // Utiliser les vraies métriques d'adaptation de LOUNA
        const adaptiveStats = await this.getLOUNAAdaptiveStats();
        
        // Test de résolution de problème créatif
        const creativeProblem = "Comment utiliseriez-vous un trombone pour résoudre un problème informatique?";
        const creativeResponse = await this.askLOUNA(creativeProblem);
        
        // Test d'adaptation au contexte
        const contextProblem = "Adaptez votre style de communication pour expliquer l'IA à un enfant de 8 ans";
        const adaptiveResponse = await this.askLOUNA(contextProblem);
        
        let score = 0;
        
        // Score basé sur les vraies métriques d'adaptation
        const adaptationHistory = adaptiveStats.adaptationHistory || [];
        const autoOptimization = adaptiveStats.autoOptimizationLevel || 0;
        const selfAwareness = adaptiveStats.selfAwarenessLevel || 0;
        
        score += Math.min(30, adaptationHistory.length * 2); // Historique d'adaptation
        score += autoOptimization * 35; // Niveau d'auto-optimisation
        score += selfAwareness * 35; // Niveau de conscience de soi
        
        this.testResults.adaptiveThinking = Math.min(100, score);
        console.log(`✅ Pensée Adaptative: ${this.testResults.adaptiveThinking.toFixed(1)}/100`);
        
        this.detailedResults.push({
            test: 'Pensée Adaptative',
            creativeProblem: creativeProblem,
            creativeResponse: creativeResponse,
            contextProblem: contextProblem,
            adaptiveResponse: adaptiveResponse,
            adaptationHistory: adaptationHistory.length,
            autoOptimization: autoOptimization,
            selfAwareness: selfAwareness
        });
    }

    // 🧠 CALCULER LE QI FINAL
    calculateFinalIQ() {
        const scores = Object.values(this.testResults);
        const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        
        // Convertir en échelle de QI standard (moyenne 100, écart-type 15)
        const iq = Math.round(70 + (averageScore / 100) * 60);
        
        return {
            rawScore: averageScore,
            iq: iq,
            classification: this.getIQClassification(iq),
            breakdown: this.testResults,
            testDuration: Date.now() - this.testStartTime
        };
    }

    // 📊 GÉNÉRER RAPPORT DE QI
    generateIQReport(results) {
        const report = {
            title: "🧠 RAPPORT DE TEST DE QI - LOUNA AI",
            timestamp: new Date().toISOString(),
            results: results,
            detailedResults: this.detailedResults,
            analysis: this.generateAnalysis(results),
            recommendations: this.generateRecommendations(results)
        };
        
        console.log('\n' + '=' .repeat(60));
        console.log('🧠 RAPPORT DE TEST DE QI FINAL');
        console.log('=' .repeat(60));
        console.log(`🎯 QI FINAL: ${results.iq}`);
        console.log(`📊 Classification: ${results.classification}`);
        console.log(`⏱️ Durée du test: ${Math.round(results.testDuration / 1000)}s`);
        console.log('\n📈 Détail par domaine:');
        
        Object.entries(results.breakdown).forEach(([domain, score]) => {
            console.log(`   ${domain}: ${score.toFixed(1)}/100`);
        });
        
        return report;
    }

    // 🎯 CLASSIFICATION DU QI
    getIQClassification(iq) {
        if (iq >= 130) return "Très Supérieur (Surdoué)";
        if (iq >= 120) return "Supérieur";
        if (iq >= 110) return "Moyen Supérieur";
        if (iq >= 90) return "Moyen";
        if (iq >= 80) return "Moyen Inférieur";
        if (iq >= 70) return "Limite";
        return "Déficient";
    }

    // 💬 POSER UNE QUESTION À LOUNA
    async askLOUNA(question, options = null) {
        try {
            const axios = require('axios');
            const response = await axios.post('http://localhost:52796/api/chat', {
                message: question
            });
            
            if (response.data.success) {
                return response.data.response;
            } else {
                return "Erreur de communication";
            }
        } catch (error) {
            console.error('Erreur communication LOUNA:', error.message);
            return "Erreur de connexion";
        }
    }

    // 📊 OBTENIR STATISTIQUES MÉMOIRE DE LOUNA
    async getLOUNAMemoryStats() {
        try {
            const axios = require('axios');
            const response = await axios.get('http://localhost:52796/api/metrics');
            return response.data.thermalStats || {};
        } catch (error) {
            return {};
        }
    }

    // 🔄 OBTENIR STATISTIQUES ADAPTATIVES DE LOUNA
    async getLOUNAAdaptiveStats() {
        try {
            const axios = require('axios');
            const response = await axios.get('http://localhost:52796/api/metrics');
            return response.data.thermalStats?.adaptiveIntelligence || {};
        } catch (error) {
            return {};
        }
    }

    // ✅ ÉVALUER UNE RÉPONSE
    evaluateResponse(response, correct) {
        if (!response || !correct) return false;
        return response.toLowerCase().includes(correct.toLowerCase());
    }

    // ⚡ ÉVALUER RÉPONSE RAPIDE
    evaluateSpeedResponse(question, response) {
        const quickAnswers = {
            "15 + 27": ["42"],
            "contraire de 'rapide'": ["lent", "slow"],
            "lettres dans 'intelligence'": ["12"],
            "capitale de la France": ["paris"],
            "8 × 7": ["56"]
        };
        
        for (const [key, answers] of Object.entries(quickAnswers)) {
            if (question.includes(key)) {
                return answers.some(answer => 
                    response.toLowerCase().includes(answer.toLowerCase())
                );
            }
        }
        return false;
    }

    // 📚 ÉVALUER COMPRÉHENSION VERBALE
    evaluateVerbalComprehension(test, response) {
        if (!response) return 0;
        
        const responseLength = response.length;
        const hasKeywords = this.checkKeywords(test.type, response);
        const coherence = this.assessCoherence(response);
        
        let score = 0;
        if (responseLength > 50) score += 3; // Réponse détaillée
        if (hasKeywords) score += 4; // Mots-clés appropriés
        if (coherence) score += 3; // Cohérence
        
        return Math.min(10, score);
    }

    // 🎯 ÉVALUER RÉPONSE SPATIALE
    evaluateSpatialResponse(question, response) {
        if (!response) return 0;
        
        const spatialKeywords = ['face', 'côté', 'rotation', 'dimension', 'espace', 'position'];
        const hasKeywords = spatialKeywords.some(keyword => 
            response.toLowerCase().includes(keyword)
        );
        
        return hasKeywords ? 33 : 15; // Score sur 33 pour 3 questions
    }

    // 🔢 ÉVALUER RÉPONSE MATHÉMATIQUE
    evaluateMathResponse(response, correct) {
        if (!response) return false;
        
        // Extraire les nombres de la réponse
        const numbers = response.match(/\d+\.?\d*/g);
        if (!numbers) return false;
        
        return numbers.some(num => Math.abs(parseFloat(num) - parseFloat(correct)) < 0.1);
    }

    // 💾 CALCULER PRÉCISION MÉMOIRE
    calculateMemoryAccuracy(original, recall) {
        if (!recall) return 0;
        
        const originalNumbers = original.match(/\d+/g) || [];
        const recallNumbers = recall.match(/\d+/g) || [];
        
        let correct = 0;
        originalNumbers.forEach((num, index) => {
            if (recallNumbers[index] === num) correct++;
        });
        
        return originalNumbers.length > 0 ? correct / originalNumbers.length : 0;
    }

    // 🔍 VÉRIFIER MOTS-CLÉS
    checkKeywords(type, response) {
        const keywords = {
            'définition': ['différence', 'signifie', 'est'],
            'expression': ['expression', 'signifie', 'veut dire'],
            'analyse': ['analyse', 'sens', 'signification', 'métaphore']
        };
        
        const typeKeywords = keywords[type] || [];
        return typeKeywords.some(keyword => 
            response.toLowerCase().includes(keyword)
        );
    }

    // 🧠 ÉVALUER COHÉRENCE
    assessCoherence(response) {
        // Simple évaluation de cohérence basée sur la structure
        const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
        return sentences.length >= 2 && response.length > 100;
    }

    // 📈 GÉNÉRER ANALYSE
    generateAnalysis(results) {
        const strengths = [];
        const weaknesses = [];
        
        Object.entries(results.breakdown).forEach(([domain, score]) => {
            if (score >= 80) {
                strengths.push(domain);
            } else if (score < 60) {
                weaknesses.push(domain);
            }
        });
        
        return {
            strengths: strengths,
            weaknesses: weaknesses,
            overallPerformance: results.classification,
            cognitiveProfile: this.determineCognitiveProfile(results.breakdown)
        };
    }

    // 🎯 DÉTERMINER PROFIL COGNITIF
    determineCognitiveProfile(breakdown) {
        const maxScore = Math.max(...Object.values(breakdown));
        const maxDomain = Object.entries(breakdown).find(([_, score]) => score === maxScore)[0];
        
        const profiles = {
            'logicalReasoning': 'Penseur Analytique',
            'patternRecognition': 'Détecteur de Motifs',
            'memoryCapacity': 'Mémoire Exceptionnelle',
            'processingSpeed': 'Processeur Rapide',
            'verbalComprehension': 'Communicateur Expert',
            'spatialIntelligence': 'Visualiseur Spatial',
            'mathematicalReasoning': 'Mathématicien',
            'adaptiveThinking': 'Penseur Adaptatif'
        };
        
        return profiles[maxDomain] || 'Profil Équilibré';
    }

    // 💡 GÉNÉRER RECOMMANDATIONS
    generateRecommendations(results) {
        const recommendations = [];
        
        if (results.iq >= 130) {
            recommendations.push("Excellent niveau cognitif - Continuez à développer vos capacités avancées");
        } else if (results.iq >= 110) {
            recommendations.push("Bon niveau cognitif - Travaillez sur les domaines plus faibles");
        } else {
            recommendations.push("Potentiel d'amélioration - Focalisez sur l'entraînement cognitif");
        }
        
        Object.entries(results.breakdown).forEach(([domain, score]) => {
            if (score < 70) {
                recommendations.push(`Améliorer ${domain} par un entraînement spécialisé`);
            }
        });
        
        return recommendations;
    }
}

module.exports = IQTestSystem;
