/**
 * 🎯 LOUNA AI - ÉVALUATEUR DE PERFORMANCE
 * Système d'évaluation pour mesurer les améliorations auto-générées
 * Inspiré des benchmarks de Darwin Gödel Machine
 */

class PerformanceEvaluator {
    constructor() {
        this.version = '1.0.0';
        this.benchmarks = new Map();
        this.evaluationHistory = [];
        this.baselineScores = null;
        this.currentScores = null;
        
        console.log('🎯 Évaluateur de performance LOUNA initialisé');
        this.initializeBenchmarks();
    }

    // 🏁 INITIALISER LES BENCHMARKS
    initializeBenchmarks() {
        // Benchmark 1: Qualité des réponses chat
        this.benchmarks.set('chat_quality', {
            name: 'Qualité des réponses chat',
            description: 'Évalue la pertinence et la qualité des réponses',
            weight: 0.3,
            testCases: [
                { input: 'Bonjour, comment ça va ?', expectedType: 'greeting', minLength: 20 },
                { input: 'Explique-moi la mémoire thermique', expectedType: 'technical', minLength: 100 },
                { input: 'Quel est ton QI actuel ?', expectedType: 'status', minLength: 50 },
                { input: 'Peux-tu m\'aider avec un problème ?', expectedType: 'assistance', minLength: 30 }
            ]
        });

        // Benchmark 2: Efficacité thermique
        this.benchmarks.set('thermal_efficiency', {
            name: 'Efficacité thermique',
            description: 'Mesure l\'efficacité du système thermique',
            weight: 0.25,
            metrics: ['temperature_stability', 'memory_efficiency', 'zone_utilization']
        });

        // Benchmark 3: Temps de réponse
        this.benchmarks.set('response_time', {
            name: 'Temps de réponse',
            description: 'Mesure la rapidité des réponses',
            weight: 0.2,
            targetTime: 2000, // 2 secondes max
            testRequests: 10
        });

        // Benchmark 4: Croissance neuronale
        this.benchmarks.set('neural_growth', {
            name: 'Croissance neuronale',
            description: 'Évalue la génération de neurones',
            weight: 0.15,
            targetGrowthRate: 10 // neurones par minute
        });

        // Benchmark 5: Utilisation mémoire
        this.benchmarks.set('memory_utilization', {
            name: 'Utilisation mémoire',
            description: 'Efficacité d\'utilisation de la mémoire',
            weight: 0.1,
            maxMemoryUsage: 500 * 1024 * 1024 // 500MB
        });

        console.log(`🏁 ${this.benchmarks.size} benchmarks initialisés`);
    }

    // 📊 ÉVALUER PERFORMANCE COMPLÈTE
    async evaluateFullPerformance(chatAPI, thermalMemory, artificialBrain) {
        console.log('📊 Évaluation complète de performance...');
        
        const evaluation = {
            timestamp: new Date().toISOString(),
            scores: {},
            overallScore: 0,
            improvements: [],
            regressions: [],
            details: {}
        };

        try {
            // Évaluer chaque benchmark
            for (const [benchmarkId, benchmark] of this.benchmarks) {
                console.log(`🎯 Évaluation: ${benchmark.name}`);
                
                const score = await this.evaluateBenchmark(
                    benchmarkId, 
                    benchmark, 
                    chatAPI, 
                    thermalMemory, 
                    artificialBrain
                );
                
                evaluation.scores[benchmarkId] = score;
                evaluation.details[benchmarkId] = score.details;
            }

            // Calculer le score global
            evaluation.overallScore = this.calculateOverallScore(evaluation.scores);

            // Comparer avec les scores précédents
            if (this.currentScores) {
                const comparison = this.compareScores(this.currentScores, evaluation.scores);
                evaluation.improvements = comparison.improvements;
                evaluation.regressions = comparison.regressions;
            }

            // Mettre à jour les scores actuels
            this.currentScores = evaluation.scores;
            
            // Établir la baseline si c'est la première évaluation
            if (!this.baselineScores) {
                this.baselineScores = { ...evaluation.scores };
                console.log('📊 Baseline établie');
            }

            // Ajouter à l'historique
            this.evaluationHistory.push(evaluation);

            console.log(`📊 Évaluation terminée - Score global: ${evaluation.overallScore.toFixed(2)}`);
            return evaluation;

        } catch (error) {
            console.error('❌ Erreur évaluation performance:', error);
            return null;
        }
    }

    // 🎯 ÉVALUER UN BENCHMARK SPÉCIFIQUE
    async evaluateBenchmark(benchmarkId, benchmark, chatAPI, thermalMemory, artificialBrain) {
        const score = {
            value: 0,
            maxValue: 100,
            weight: benchmark.weight,
            details: {},
            timestamp: new Date().toISOString()
        };

        try {
            switch (benchmarkId) {
                case 'chat_quality':
                    score.value = await this.evaluateChatQuality(benchmark, chatAPI);
                    break;
                case 'thermal_efficiency':
                    score.value = await this.evaluateThermalEfficiency(benchmark, thermalMemory);
                    break;
                case 'response_time':
                    score.value = await this.evaluateResponseTime(benchmark, chatAPI);
                    break;
                case 'neural_growth':
                    score.value = await this.evaluateNeuralGrowth(benchmark, artificialBrain);
                    break;
                case 'memory_utilization':
                    score.value = await this.evaluateMemoryUtilization(benchmark);
                    break;
            }

            score.value = Math.max(0, Math.min(100, score.value));

        } catch (error) {
            console.error(`❌ Erreur évaluation ${benchmarkId}:`, error);
            score.value = 0;
            score.details.error = error.message;
        }

        return score;
    }

    // 💬 ÉVALUER QUALITÉ CHAT
    async evaluateChatQuality(benchmark, chatAPI) {
        if (!chatAPI) return 0;

        let totalScore = 0;
        const testResults = [];

        for (const testCase of benchmark.testCases) {
            try {
                const startTime = Date.now();
                
                // Simuler une requête chat
                const response = await this.simulateChatRequest(chatAPI, testCase.input);
                const responseTime = Date.now() - startTime;

                const caseScore = this.scoreChatResponse(response, testCase, responseTime);
                testResults.push({
                    input: testCase.input,
                    response: response,
                    score: caseScore,
                    responseTime: responseTime
                });

                totalScore += caseScore;

            } catch (error) {
                console.error('❌ Erreur test chat:', error);
                testResults.push({
                    input: testCase.input,
                    score: 0,
                    error: error.message
                });
            }
        }

        const averageScore = totalScore / benchmark.testCases.length;
        return Math.round(averageScore);
    }

    // 🌡️ ÉVALUER EFFICACITÉ THERMIQUE
    async evaluateThermalEfficiency(benchmark, thermalMemory) {
        if (!thermalMemory) return 0;

        try {
            const stats = thermalMemory.getDetailedStats();
            let score = 0;

            // Stabilité température (0-40 points)
            const tempStability = this.evaluateTemperatureStability(stats.temperature);
            score += tempStability * 0.4;

            // Efficacité mémoire (0-40 points)
            const memoryEff = Math.min(100, stats.memoryEfficiency || 0);
            score += memoryEff * 0.4;

            // Utilisation zones (0-20 points)
            const zoneUtil = this.evaluateZoneUtilization(stats.zones);
            score += zoneUtil * 0.2;

            return Math.round(score);

        } catch (error) {
            console.error('❌ Erreur évaluation thermique:', error);
            return 0;
        }
    }

    // ⏱️ ÉVALUER TEMPS DE RÉPONSE
    async evaluateResponseTime(benchmark, chatAPI) {
        if (!chatAPI) return 0;

        const responseTimes = [];
        const testMessage = 'Test de performance';

        for (let i = 0; i < benchmark.testRequests; i++) {
            try {
                const startTime = Date.now();
                await this.simulateChatRequest(chatAPI, testMessage);
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);

            } catch (error) {
                responseTimes.push(benchmark.targetTime * 2); // Pénalité
            }
        }

        const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
        
        // Score inversement proportionnel au temps
        const score = Math.max(0, 100 - (averageTime / benchmark.targetTime) * 50);
        return Math.round(score);
    }

    // 🧠 ÉVALUER CROISSANCE NEURONALE
    async evaluateNeuralGrowth(benchmark, artificialBrain) {
        if (!artificialBrain) return 0;

        try {
            const stats = artificialBrain.getStats();
            const currentNeurons = stats.activeNeurons || 0;

            // Simuler une période d'observation
            await new Promise(resolve => setTimeout(resolve, 60000)); // 1 minute

            const newStats = artificialBrain.getStats();
            const newNeurons = newStats.activeNeurons || 0;
            const growth = newNeurons - currentNeurons;

            // Score basé sur le taux de croissance
            const growthRate = growth; // neurones par minute
            const score = Math.min(100, (growthRate / benchmark.targetGrowthRate) * 100);

            return Math.round(Math.max(0, score));

        } catch (error) {
            console.error('❌ Erreur évaluation croissance neuronale:', error);
            return 0;
        }
    }

    // 💾 ÉVALUER UTILISATION MÉMOIRE
    async evaluateMemoryUtilization(benchmark) {
        try {
            const memUsage = process.memoryUsage();
            const heapUsed = memUsage.heapUsed;

            // Score inversement proportionnel à l'utilisation
            const utilizationRatio = heapUsed / benchmark.maxMemoryUsage;
            const score = Math.max(0, 100 - (utilizationRatio * 100));

            return Math.round(score);

        } catch (error) {
            console.error('❌ Erreur évaluation mémoire:', error);
            return 0;
        }
    }

    // 🎯 SCORER UNE RÉPONSE CHAT
    scoreChatResponse(response, testCase, responseTime) {
        let score = 0;

        if (!response || typeof response !== 'string') {
            return 0;
        }

        // Longueur appropriée (0-30 points)
        if (response.length >= testCase.minLength) {
            score += 30;
        } else {
            score += (response.length / testCase.minLength) * 30;
        }

        // Pertinence du contenu (0-40 points)
        const relevanceScore = this.evaluateRelevance(response, testCase.expectedType);
        score += relevanceScore * 0.4;

        // Temps de réponse (0-30 points)
        const timeScore = Math.max(0, 30 - (responseTime / 100)); // Pénalité par 100ms
        score += Math.min(30, timeScore);

        return Math.round(score);
    }

    // 🔍 ÉVALUER PERTINENCE
    evaluateRelevance(response, expectedType) {
        const keywords = {
            greeting: ['bonjour', 'salut', 'hello', 'ça va', 'comment'],
            technical: ['mémoire', 'thermique', 'neurone', 'système', 'température'],
            status: ['qi', 'neurone', 'performance', 'état', 'statistique'],
            assistance: ['aide', 'aider', 'problème', 'solution', 'support']
        };

        const typeKeywords = keywords[expectedType] || [];
        const responseLower = response.toLowerCase();
        
        let matches = 0;
        for (const keyword of typeKeywords) {
            if (responseLower.includes(keyword)) {
                matches++;
            }
        }

        return Math.min(100, (matches / typeKeywords.length) * 100);
    }

    // 🌡️ ÉVALUER STABILITÉ TEMPÉRATURE
    evaluateTemperatureStability(temperature) {
        const optimal = 37.0;
        const deviation = Math.abs(temperature - optimal);
        
        if (deviation <= 1.0) return 100;
        if (deviation <= 2.0) return 80;
        if (deviation <= 3.0) return 60;
        if (deviation <= 5.0) return 40;
        return 20;
    }

    // 🗂️ ÉVALUER UTILISATION ZONES
    evaluateZoneUtilization(zones) {
        if (!zones) return 0;

        const activeZones = Object.values(zones).filter(zone => 
            zone && zone.entries > 0).length;
        const totalZones = 6;

        return (activeZones / totalZones) * 100;
    }

    // 🧪 SIMULER REQUÊTE CHAT
    async simulateChatRequest(chatAPI, message) {
        // Cette fonction devra être adaptée selon votre API chat
        try {
            if (typeof chatAPI === 'function') {
                return await chatAPI(message);
            } else if (chatAPI && typeof chatAPI.processMessage === 'function') {
                return await chatAPI.processMessage(message);
            } else {
                // Simulation basique
                return `Réponse simulée pour: ${message}`;
            }
        } catch (error) {
            throw new Error(`Erreur simulation chat: ${error.message}`);
        }
    }

    // 📊 CALCULER SCORE GLOBAL
    calculateOverallScore(scores) {
        let weightedSum = 0;
        let totalWeight = 0;

        for (const [benchmarkId, score] of Object.entries(scores)) {
            const benchmark = this.benchmarks.get(benchmarkId);
            if (benchmark) {
                weightedSum += score.value * benchmark.weight;
                totalWeight += benchmark.weight;
            }
        }

        return totalWeight > 0 ? weightedSum / totalWeight : 0;
    }

    // 🔄 COMPARER SCORES
    compareScores(previousScores, currentScores) {
        const improvements = [];
        const regressions = [];

        for (const [benchmarkId, currentScore] of Object.entries(currentScores)) {
            const previousScore = previousScores[benchmarkId];
            
            if (previousScore) {
                const difference = currentScore.value - previousScore.value;
                
                if (difference > 5) { // Amélioration significative
                    improvements.push({
                        benchmark: benchmarkId,
                        improvement: difference,
                        previous: previousScore.value,
                        current: currentScore.value
                    });
                } else if (difference < -5) { // Régression significative
                    regressions.push({
                        benchmark: benchmarkId,
                        regression: Math.abs(difference),
                        previous: previousScore.value,
                        current: currentScore.value
                    });
                }
            }
        }

        return { improvements, regressions };
    }

    // 📈 OBTENIR STATISTIQUES
    getStats() {
        return {
            version: this.version,
            totalEvaluations: this.evaluationHistory.length,
            currentScores: this.currentScores,
            baselineScores: this.baselineScores,
            benchmarks: Array.from(this.benchmarks.keys()),
            lastEvaluation: this.evaluationHistory[this.evaluationHistory.length - 1] || null
        };
    }

    // 📊 OBTENIR HISTORIQUE PERFORMANCE
    getPerformanceHistory() {
        return this.evaluationHistory.map(evaluation => ({
            timestamp: evaluation.timestamp,
            overallScore: evaluation.overallScore,
            improvements: evaluation.improvements.length,
            regressions: evaluation.regressions.length
        }));
    }
}

module.exports = PerformanceEvaluator;
