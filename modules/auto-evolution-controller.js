/**
 * 🚀 LOUNA AI - CONTRÔLEUR D'AUTO-ÉVOLUTION
 * Orchestre l'auto-modification intelligente guidée par la mémoire thermique
 * Combine Darwin Gödel Machine + Intelligence thermique LOUNA
 */

const SelfModificationEngine = require('./self-modification-engine');
const PerformanceEvaluator = require('./performance-evaluator');

class AutoEvolutionController {
    constructor() {
        this.version = '1.0.0';
        this.isActive = false;
        this.evolutionCycle = 0;
        this.lastEvolution = null;
        
        // Composants principaux
        this.modificationEngine = new SelfModificationEngine();
        this.performanceEvaluator = new PerformanceEvaluator();
        
        // Références aux systèmes LOUNA
        this.thermalMemory = null;
        this.artificialBrain = null;
        this.chatAPI = null;
        
        // Configuration
        this.config = {
            evolutionInterval: 30 * 60 * 1000, // 30 minutes
            minPerformanceGain: 5, // 5% minimum d'amélioration
            maxEvolutionsPerDay: 10,
            safetyMode: true,
            thermalGuidance: true
        };
        
        // Métriques d'évolution
        this.evolutionStats = {
            totalEvolutions: 0,
            successfulEvolutions: 0,
            failedEvolutions: 0,
            averageImprovement: 0,
            bestPerformance: 0,
            evolutionHistory: []
        };
        
        console.log('🚀 Contrôleur d\'auto-évolution LOUNA initialisé');
    }

    // 🔗 CONNECTER AUX SYSTÈMES LOUNA
    connectSystems(thermalMemory, artificialBrain, chatAPI) {
        this.thermalMemory = thermalMemory;
        this.artificialBrain = artificialBrain;
        this.chatAPI = chatAPI;
        
        // Connecter le moteur de modification à la mémoire thermique
        this.modificationEngine.connectThermalGuidance(thermalMemory);
        
        console.log('🔗 Systèmes LOUNA connectés au contrôleur d\'évolution');
    }

    // 🚀 DÉMARRER L'AUTO-ÉVOLUTION
    async startEvolution() {
        if (this.isActive) {
            console.log('⚠️ Auto-évolution déjà active');
            return;
        }

        console.log('🚀 Démarrage de l\'auto-évolution LOUNA...');
        this.isActive = true;

        try {
            // 1. Évaluation baseline
            console.log('📊 Évaluation baseline...');
            const baselineEvaluation = await this.performanceEvaluator.evaluateFullPerformance(
                this.chatAPI, this.thermalMemory, this.artificialBrain
            );

            if (baselineEvaluation) {
                console.log(`📊 Performance baseline: ${baselineEvaluation.overallScore.toFixed(2)}`);
                this.evolutionStats.bestPerformance = baselineEvaluation.overallScore;
            }

            // 2. Démarrer le cycle d'évolution
            this.startEvolutionCycle();

            console.log('✅ Auto-évolution démarrée avec succès');

        } catch (error) {
            console.error('❌ Erreur démarrage auto-évolution:', error);
            this.isActive = false;
        }
    }

    // 🔄 DÉMARRER CYCLE D'ÉVOLUTION
    startEvolutionCycle() {
        if (!this.isActive) return;

        console.log('🔄 Démarrage du cycle d\'évolution automatique');

        // Cycle d'évolution principal
        this.evolutionTimer = setInterval(async () => {
            if (this.isActive && this.canEvolve()) {
                await this.performEvolutionCycle();
            }
        }, this.config.evolutionInterval);

        // Premier cycle immédiat (après 5 minutes)
        setTimeout(async () => {
            if (this.isActive) {
                await this.performEvolutionCycle();
            }
        }, 5 * 60 * 1000);
    }

    // 🧬 EFFECTUER UN CYCLE D'ÉVOLUTION
    async performEvolutionCycle() {
        this.evolutionCycle++;
        console.log(`🧬 Cycle d'évolution #${this.evolutionCycle}`);

        const evolutionResult = {
            cycle: this.evolutionCycle,
            timestamp: new Date().toISOString(),
            success: false,
            modification: null,
            performanceBefore: null,
            performanceAfter: null,
            improvement: 0,
            error: null
        };

        try {
            // 1. Évaluation pré-modification
            console.log('📊 Évaluation pré-modification...');
            evolutionResult.performanceBefore = await this.performanceEvaluator.evaluateFullPerformance(
                this.chatAPI, this.thermalMemory, this.artificialBrain
            );

            if (!evolutionResult.performanceBefore) {
                throw new Error('Impossible d\'évaluer la performance actuelle');
            }

            // 2. Analyser l'état thermique pour guidage
            const thermalGuidance = this.analyzeThermalGuidance();

            // 3. Proposer une modification
            console.log('🧬 Proposition de modification...');
            const proposedModification = await this.modificationEngine.proposeModification();

            if (!proposedModification) {
                console.log('ℹ️ Aucune modification proposée pour ce cycle');
                return;
            }

            evolutionResult.modification = proposedModification;

            // 4. Appliquer la modification
            console.log(`🔧 Application de la modification: ${proposedModification.description}`);
            const applicationResult = await this.modificationEngine.applyModification(proposedModification);

            if (!applicationResult.success) {
                throw new Error(`Échec application: ${applicationResult.reason}`);
            }

            // 5. Évaluation post-modification
            console.log('📊 Évaluation post-modification...');
            await new Promise(resolve => setTimeout(resolve, 30000)); // Attendre 30s pour stabilisation

            evolutionResult.performanceAfter = await this.performanceEvaluator.evaluateFullPerformance(
                this.chatAPI, this.thermalMemory, this.artificialBrain
            );

            if (!evolutionResult.performanceAfter) {
                throw new Error('Impossible d\'évaluer la performance après modification');
            }

            // 6. Calculer l'amélioration
            evolutionResult.improvement = evolutionResult.performanceAfter.overallScore - 
                                        evolutionResult.performanceBefore.overallScore;

            // 7. Valider l'amélioration
            if (evolutionResult.improvement >= this.config.minPerformanceGain) {
                evolutionResult.success = true;
                this.evolutionStats.successfulEvolutions++;
                this.evolutionStats.bestPerformance = Math.max(
                    this.evolutionStats.bestPerformance, 
                    evolutionResult.performanceAfter.overallScore
                );

                console.log(`✅ Évolution réussie! Amélioration: +${evolutionResult.improvement.toFixed(2)} points`);

                // Enregistrer dans la mémoire thermique
                this.recordEvolutionInMemory(evolutionResult);

            } else {
                console.log(`⚠️ Amélioration insuffisante: +${evolutionResult.improvement.toFixed(2)} points (min: ${this.config.minPerformanceGain})`);
                
                // Optionnel: revenir en arrière si pas d'amélioration
                if (evolutionResult.improvement < 0) {
                    console.log('🔄 Régression détectée, restauration...');
                    // La restauration est gérée automatiquement par le moteur de modification
                }
            }

        } catch (error) {
            console.error('❌ Erreur cycle d\'évolution:', error);
            evolutionResult.error = error.message;
            this.evolutionStats.failedEvolutions++;
        }

        // Mettre à jour les statistiques
        this.evolutionStats.totalEvolutions++;
        this.evolutionStats.averageImprovement = this.calculateAverageImprovement();
        this.evolutionStats.evolutionHistory.push(evolutionResult);
        this.lastEvolution = evolutionResult;

        // Limiter l'historique
        if (this.evolutionStats.evolutionHistory.length > 100) {
            this.evolutionStats.evolutionHistory = this.evolutionStats.evolutionHistory.slice(-100);
        }

        console.log(`🧬 Cycle d'évolution #${this.evolutionCycle} terminé`);
    }

    // 🌡️ ANALYSER GUIDAGE THERMIQUE
    analyzeThermalGuidance() {
        if (!this.thermalMemory || !this.config.thermalGuidance) {
            return null;
        }

        try {
            const thermalStats = this.thermalMemory.getDetailedStats();
            
            const guidance = {
                temperature: thermalStats.temperature,
                efficiency: thermalStats.memoryEfficiency,
                activeZones: Object.values(thermalStats.zones || {}).filter(zone => zone.entries > 0).length,
                totalEntries: thermalStats.totalEntries,
                recommendations: []
            };

            // Analyser et générer des recommandations
            if (guidance.efficiency < 95) {
                guidance.recommendations.push({
                    type: 'efficiency',
                    priority: 'high',
                    message: 'Améliorer l\'efficacité de la mémoire thermique'
                });
            }

            if (guidance.activeZones < 3) {
                guidance.recommendations.push({
                    type: 'zones',
                    priority: 'medium',
                    message: 'Augmenter l\'utilisation des zones mémoire'
                });
            }

            if (Math.abs(guidance.temperature - 37.0) > 2.0) {
                guidance.recommendations.push({
                    type: 'temperature',
                    priority: 'medium',
                    message: 'Optimiser la régulation thermique'
                });
            }

            return guidance;

        } catch (error) {
            console.error('❌ Erreur analyse guidage thermique:', error);
            return null;
        }
    }

    // 💾 ENREGISTRER ÉVOLUTION DANS MÉMOIRE
    recordEvolutionInMemory(evolutionResult) {
        if (!this.thermalMemory) return;

        try {
            const memoryEntry = {
                type: 'self_evolution',
                cycle: evolutionResult.cycle,
                improvement: evolutionResult.improvement,
                modification: evolutionResult.modification.description,
                performance: evolutionResult.performanceAfter.overallScore
            };

            this.thermalMemory.add(
                'self_evolution',
                `Évolution #${evolutionResult.cycle}: ${evolutionResult.modification.description} (+${evolutionResult.improvement.toFixed(2)} points)`,
                0.9, // Importance élevée
                'core_identity'
            );

            console.log('💾 Évolution enregistrée dans la mémoire thermique');

        } catch (error) {
            console.error('❌ Erreur enregistrement évolution:', error);
        }
    }

    // 📊 CALCULER AMÉLIORATION MOYENNE
    calculateAverageImprovement() {
        const successfulEvolutions = this.evolutionStats.evolutionHistory.filter(e => e.success);
        
        if (successfulEvolutions.length === 0) return 0;

        const totalImprovement = successfulEvolutions.reduce((sum, e) => sum + e.improvement, 0);
        return totalImprovement / successfulEvolutions.length;
    }

    // ✅ VÉRIFIER SI ÉVOLUTION POSSIBLE
    canEvolve() {
        // Vérifier les limites quotidiennes
        const today = new Date().toDateString();
        const todayEvolutions = this.evolutionStats.evolutionHistory.filter(e => 
            new Date(e.timestamp).toDateString() === today
        ).length;

        if (todayEvolutions >= this.config.maxEvolutionsPerDay) {
            console.log('⚠️ Limite quotidienne d\'évolutions atteinte');
            return false;
        }

        // Vérifier l'état des systèmes
        if (!this.thermalMemory || !this.artificialBrain) {
            console.log('⚠️ Systèmes LOUNA non connectés');
            return false;
        }

        return true;
    }

    // ⏹️ ARRÊTER L'AUTO-ÉVOLUTION
    stopEvolution() {
        console.log('⏹️ Arrêt de l\'auto-évolution...');
        
        this.isActive = false;
        
        if (this.evolutionTimer) {
            clearInterval(this.evolutionTimer);
            this.evolutionTimer = null;
        }

        console.log('✅ Auto-évolution arrêtée');
    }

    // ⚙️ CONFIGURER L'ÉVOLUTION
    configure(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ Configuration d\'évolution mise à jour');
    }

    // 📊 OBTENIR STATISTIQUES
    getStats() {
        return {
            version: this.version,
            isActive: this.isActive,
            evolutionCycle: this.evolutionCycle,
            config: this.config,
            evolutionStats: this.evolutionStats,
            lastEvolution: this.lastEvolution,
            modificationEngineStats: this.modificationEngine.getStats(),
            performanceEvaluatorStats: this.performanceEvaluator.getStats()
        };
    }

    // 📈 OBTENIR RAPPORT D'ÉVOLUTION
    getEvolutionReport() {
        const recentEvolutions = this.evolutionStats.evolutionHistory.slice(-10);
        const successRate = this.evolutionStats.totalEvolutions > 0 ? 
            (this.evolutionStats.successfulEvolutions / this.evolutionStats.totalEvolutions) * 100 : 0;

        return {
            summary: {
                totalEvolutions: this.evolutionStats.totalEvolutions,
                successfulEvolutions: this.evolutionStats.successfulEvolutions,
                successRate: successRate.toFixed(1) + '%',
                averageImprovement: this.evolutionStats.averageImprovement.toFixed(2),
                bestPerformance: this.evolutionStats.bestPerformance.toFixed(2)
            },
            recentEvolutions: recentEvolutions.map(e => ({
                cycle: e.cycle,
                timestamp: e.timestamp,
                success: e.success,
                improvement: e.improvement?.toFixed(2) || 'N/A',
                description: e.modification?.description || 'Aucune modification'
            })),
            currentStatus: {
                isActive: this.isActive,
                nextEvolution: this.isActive ? 
                    new Date(Date.now() + this.config.evolutionInterval).toISOString() : 'Arrêté'
            }
        };
    }

    // 🔧 FORCER UNE ÉVOLUTION MANUELLE
    async forceEvolution() {
        if (!this.isActive) {
            throw new Error('Auto-évolution non active');
        }

        console.log('🔧 Évolution manuelle forcée...');
        await this.performEvolutionCycle();
    }
}

module.exports = AutoEvolutionController;
