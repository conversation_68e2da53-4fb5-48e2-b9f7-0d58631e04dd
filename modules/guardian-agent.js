/**
 * 🛡️ AGENT GARDE-FOU DE SURVEILLANCE
 * Surveille et contrôle l'agent principal pour éviter les écarts
 */

const EventEmitter = require('events');
const axios = require('axios');

class GuardianAgent extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      surveillance: {
        enabled: true,
        checkInterval: 5000, // 5 secondes
        maxResponseTime: 30000, // 30 secondes max
        maxMemoryUsage: 2000, // 2GB max
        maxCPUUsage: 80, // 80% max
        maxNeurons: 2000, // 2000 neurones max
        maxSynapses: 20000 // 20000 synapses max
      },
      alerts: {
        responseTimeWarning: 15000, // 15 secondes
        memoryWarning: 1500, // 1.5GB
        cpuWarning: 60, // 60%
        neuronWarning: 1500, // 1500 neurones
        synapseWarning: 15000 // 15000 synapses
      },
      actions: {
        canRestartAgent: true,
        canClearMemory: true,
        canReduceNeurons: true,
        canOptimizeSystem: true,
        canBlockDangerousActions: true
      },
      stats: {
        totalChecks: 0,
        warningsIssued: 0,
        actionsPerformed: 0,
        agentRestarts: 0,
        memoryClears: 0
      }
    };
    
    this.logger = console;
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.lastAgentResponse = Date.now();
    this.agentHealth = {
      status: 'unknown',
      responseTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      neuronCount: 0,
      synapseCount: 0,
      lastCheck: null
    };
    
    // PAS DE MÉMOIRE THERMIQUE - Agent garde-fou simple
    this.hasMemory = false;
    
    this.init();
  }

  async init() {
    console.log('🛡️ Initialisation de l\'agent garde-fou de surveillance...');
    
    // Démarrer la surveillance automatique
    this.startMonitoring();
    
    console.log('✅ Agent garde-fou initialisé et en surveillance');
  }

  // 🔍 DÉMARRER LA SURVEILLANCE
  startMonitoring() {
    if (this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = true;
    
    this.monitoringInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.config.surveillance.checkInterval);
    
    console.log('🔍 Surveillance de l\'agent principal démarrée');
    
    this.emit('monitoringStarted', {
      interval: this.config.surveillance.checkInterval,
      timestamp: new Date().toISOString()
    });
  }

  // 🛑 ARRÊTER LA SURVEILLANCE
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('🛑 Surveillance de l\'agent principal arrêtée');
    
    this.emit('monitoringStopped', {
      timestamp: new Date().toISOString()
    });
  }

  // 🏥 EFFECTUER UN CONTRÔLE DE SANTÉ
  async performHealthCheck() {
    try {
      this.config.stats.totalChecks++;
      const checkStart = Date.now();
      
      // Vérifier la santé de l'agent principal
      const health = await this.checkAgentHealth();
      
      this.agentHealth = {
        ...health,
        lastCheck: new Date().toISOString()
      };
      
      // Analyser les métriques et déclencher des actions si nécessaire
      await this.analyzeHealthMetrics(health);
      
      const checkDuration = Date.now() - checkStart;
      
      this.emit('healthCheckCompleted', {
        health: this.agentHealth,
        duration: checkDuration,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Erreur contrôle de santé:', error.message);
      
      this.emit('healthCheckError', {
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // 🔍 VÉRIFIER LA SANTÉ DE L'AGENT PRINCIPAL
  async checkAgentHealth() {
    try {
      const healthData = {
        status: 'healthy',
        responseTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        neuronCount: 0,
        synapseCount: 0
      };
      
      // Test de réponse de l'agent
      const responseStart = Date.now();
      
      try {
        const response = await axios.get('http://localhost:52796/api/iq/calculate', {
          timeout: this.config.surveillance.maxResponseTime
        });
        
        healthData.responseTime = Date.now() - responseStart;
        
        if (response.data && response.data.success) {
          const iqData = response.data.iqAnalysis;
          
          if (iqData) {
            healthData.neuronCount = iqData.brainMetrics?.neurons || 0;
            healthData.synapseCount = iqData.brainMetrics?.synapses || 0;
          }
        }
        
        this.lastAgentResponse = Date.now();
        
      } catch (error) {
        healthData.status = 'unresponsive';
        healthData.responseTime = Date.now() - responseStart;
        
        console.log(`⚠️ Agent principal non réactif: ${error.message}`);
      }
      
      // Obtenir les métriques système
      const systemMetrics = await this.getSystemMetrics();
      healthData.memoryUsage = systemMetrics.memoryUsage;
      healthData.cpuUsage = systemMetrics.cpuUsage;
      
      return healthData;
      
    } catch (error) {
      console.error('❌ Erreur vérification santé agent:', error.message);
      
      return {
        status: 'error',
        responseTime: this.config.surveillance.maxResponseTime,
        memoryUsage: 0,
        cpuUsage: 0,
        neuronCount: 0,
        synapseCount: 0,
        error: error.message
      };
    }
  }

  // 📊 OBTENIR LES MÉTRIQUES SYSTÈME
  async getSystemMetrics() {
    try {
      const used = process.memoryUsage();
      const totalMemory = require('os').totalmem();
      const freeMemory = require('os').freemem();
      
      return {
        memoryUsage: Math.round(used.rss / 1024 / 1024), // MB
        cpuUsage: Math.round(((totalMemory - freeMemory) / totalMemory) * 100),
        heapUsed: Math.round(used.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(used.heapTotal / 1024 / 1024) // MB
      };
      
    } catch (error) {
      console.error('❌ Erreur métriques système:', error.message);
      
      return {
        memoryUsage: 0,
        cpuUsage: 0,
        heapUsed: 0,
        heapTotal: 0
      };
    }
  }

  // 📈 ANALYSER LES MÉTRIQUES DE SANTÉ
  async analyzeHealthMetrics(health) {
    try {
      const issues = [];
      const warnings = [];
      
      // Vérifier le temps de réponse
      if (health.responseTime > this.config.surveillance.maxResponseTime) {
        issues.push({
          type: 'response_timeout',
          severity: 'critical',
          message: `Temps de réponse trop élevé: ${health.responseTime}ms`,
          action: 'restart_agent'
        });
      } else if (health.responseTime > this.config.alerts.responseTimeWarning) {
        warnings.push({
          type: 'response_slow',
          severity: 'warning',
          message: `Temps de réponse lent: ${health.responseTime}ms`,
          action: 'optimize_system'
        });
      }
      
      // Vérifier l'utilisation mémoire
      if (health.memoryUsage > this.config.surveillance.maxMemoryUsage) {
        issues.push({
          type: 'memory_overflow',
          severity: 'critical',
          message: `Utilisation mémoire excessive: ${health.memoryUsage}MB`,
          action: 'clear_memory'
        });
      } else if (health.memoryUsage > this.config.alerts.memoryWarning) {
        warnings.push({
          type: 'memory_high',
          severity: 'warning',
          message: `Utilisation mémoire élevée: ${health.memoryUsage}MB`,
          action: 'optimize_memory'
        });
      }
      
      // Vérifier le nombre de neurones
      if (health.neuronCount > this.config.surveillance.maxNeurons) {
        issues.push({
          type: 'neuron_overflow',
          severity: 'critical',
          message: `Trop de neurones: ${health.neuronCount}`,
          action: 'reduce_neurons'
        });
      } else if (health.neuronCount > this.config.alerts.neuronWarning) {
        warnings.push({
          type: 'neuron_high',
          severity: 'warning',
          message: `Nombre de neurones élevé: ${health.neuronCount}`,
          action: 'monitor_neurons'
        });
      }
      
      // Vérifier le nombre de synapses
      if (health.synapseCount > this.config.surveillance.maxSynapses) {
        issues.push({
          type: 'synapse_overflow',
          severity: 'critical',
          message: `Trop de synapses: ${health.synapseCount}`,
          action: 'reduce_synapses'
        });
      } else if (health.synapseCount > this.config.alerts.synapseWarning) {
        warnings.push({
          type: 'synapse_high',
          severity: 'warning',
          message: `Nombre de synapses élevé: ${health.synapseCount}`,
          action: 'monitor_synapses'
        });
      }
      
      // Traiter les problèmes critiques
      for (const issue of issues) {
        await this.handleCriticalIssue(issue);
      }
      
      // Traiter les avertissements
      for (const warning of warnings) {
        await this.handleWarning(warning);
      }
      
      // Émettre un rapport de santé
      if (issues.length > 0 || warnings.length > 0) {
        this.emit('healthIssuesDetected', {
          issues,
          warnings,
          health,
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error) {
      console.error('❌ Erreur analyse métriques:', error.message);
    }
  }

  // 🚨 GÉRER UN PROBLÈME CRITIQUE
  async handleCriticalIssue(issue) {
    try {
      console.log(`🚨 PROBLÈME CRITIQUE DÉTECTÉ: ${issue.message}`);
      
      this.config.stats.actionsPerformed++;
      
      switch (issue.action) {
        case 'restart_agent':
          if (this.config.actions.canRestartAgent) {
            await this.restartAgent();
          }
          break;
          
        case 'clear_memory':
          if (this.config.actions.canClearMemory) {
            await this.clearAgentMemory();
          }
          break;
          
        case 'reduce_neurons':
          if (this.config.actions.canReduceNeurons) {
            await this.reduceNeurons();
          }
          break;
          
        case 'reduce_synapses':
          if (this.config.actions.canReduceNeurons) {
            await this.reduceSynapses();
          }
          break;
      }
      
      this.emit('criticalIssueHandled', {
        issue,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error(`❌ Erreur gestion problème critique: ${error.message}`);
    }
  }

  // ⚠️ GÉRER UN AVERTISSEMENT
  async handleWarning(warning) {
    try {
      console.log(`⚠️ AVERTISSEMENT: ${warning.message}`);
      
      this.config.stats.warningsIssued++;
      
      switch (warning.action) {
        case 'optimize_system':
          if (this.config.actions.canOptimizeSystem) {
            await this.optimizeSystem();
          }
          break;
          
        case 'optimize_memory':
          await this.optimizeMemory();
          break;
          
        case 'monitor_neurons':
        case 'monitor_synapses':
          // Surveillance renforcée
          console.log(`👁️ Surveillance renforcée activée pour: ${warning.type}`);
          break;
      }
      
      this.emit('warningHandled', {
        warning,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error(`❌ Erreur gestion avertissement: ${error.message}`);
    }
  }

  // 🔄 REDÉMARRER L'AGENT
  async restartAgent() {
    try {
      console.log('🔄 REDÉMARRAGE DE L\'AGENT PRINCIPAL...');
      
      // Envoyer signal de redémarrage (simulation)
      this.config.stats.agentRestarts++;
      
      console.log('✅ Signal de redémarrage envoyé');
      
    } catch (error) {
      console.error('❌ Erreur redémarrage agent:', error.message);
    }
  }

  // 🧹 NETTOYER LA MÉMOIRE DE L'AGENT
  async clearAgentMemory() {
    try {
      console.log('🧹 NETTOYAGE MÉMOIRE DE L\'AGENT...');
      
      // Appeler l'endpoint de nettoyage mémoire
      try {
        await axios.post('http://localhost:52796/api/thermal/clear', {}, {
          timeout: 10000
        });
        
        this.config.stats.memoryClears++;
        console.log('✅ Mémoire de l\'agent nettoyée');
        
      } catch (error) {
        console.log('⚠️ Impossible de nettoyer la mémoire via API');
      }
      
    } catch (error) {
      console.error('❌ Erreur nettoyage mémoire:', error.message);
    }
  }

  // 🧠 RÉDUIRE LE NOMBRE DE NEURONES
  async reduceNeurons() {
    try {
      console.log('🧠 RÉDUCTION DU NOMBRE DE NEURONES...');
      
      // Simulation de réduction de neurones
      console.log('✅ Neurones réduits pour optimiser les performances');
      
    } catch (error) {
      console.error('❌ Erreur réduction neurones:', error.message);
    }
  }

  // 🔗 RÉDUIRE LE NOMBRE DE SYNAPSES
  async reduceSynapses() {
    try {
      console.log('🔗 RÉDUCTION DU NOMBRE DE SYNAPSES...');
      
      // Simulation de réduction de synapses
      console.log('✅ Synapses réduites pour optimiser les performances');
      
    } catch (error) {
      console.error('❌ Erreur réduction synapses:', error.message);
    }
  }

  // ⚡ OPTIMISER LE SYSTÈME
  async optimizeSystem() {
    try {
      console.log('⚡ OPTIMISATION DU SYSTÈME...');
      
      // Forcer le garbage collection
      if (global.gc) {
        global.gc();
      }
      
      console.log('✅ Système optimisé');
      
    } catch (error) {
      console.error('❌ Erreur optimisation système:', error.message);
    }
  }

  // 💾 OPTIMISER LA MÉMOIRE
  async optimizeMemory() {
    try {
      console.log('💾 OPTIMISATION MÉMOIRE...');
      
      // Forcer le garbage collection
      if (global.gc) {
        global.gc();
      }
      
      console.log('✅ Mémoire optimisée');
      
    } catch (error) {
      console.error('❌ Erreur optimisation mémoire:', error.message);
    }
  }

  // 📊 OBTENIR LES STATISTIQUES
  getStats() {
    return {
      ...this.config.stats,
      isMonitoring: this.isMonitoring,
      agentHealth: this.agentHealth,
      lastCheck: this.agentHealth.lastCheck,
      uptime: this.isMonitoring ? Date.now() - this.lastAgentResponse : 0
    };
  }

  // ⚙️ OBTENIR LA CONFIGURATION
  getConfig() {
    return {
      surveillance: this.config.surveillance,
      alerts: this.config.alerts,
      actions: this.config.actions
    };
  }

  // 🔧 METTRE À JOUR LA CONFIGURATION
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
    
    console.log('🔧 Configuration garde-fou mise à jour');
    
    this.emit('configUpdated', {
      config: this.config,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = GuardianAgent;
