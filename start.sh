#!/bin/bash

echo "🚀 Démarrage LOUNA AI..."
echo "📍 Répertoire: $(pwd)"
echo "🔧 Node version: $(node --version)"
echo "📦 NPM version: $(npm --version)"

echo "🧪 Test des modules..."
if [ -f "server-working.js" ]; then
    echo "✅ server-working.js trouvé"
else
    echo "❌ server-working.js manquant"
    exit 1
fi

if [ -d "public" ]; then
    echo "✅ Dossier public trouvé"
else
    echo "❌ Dossier public manquant"
    exit 1
fi

if [ -d "node_modules" ]; then
    echo "✅ node_modules trouvé"
else
    echo "⚠️ node_modules manquant - Installation..."
    npm install
fi

echo "🚀 Lancement du serveur..."
node server-working.js

echo "🛑 Serveur arrêté"
