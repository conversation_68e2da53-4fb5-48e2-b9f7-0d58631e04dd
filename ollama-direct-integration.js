/**
 * INTÉGRATION DIRECTE D'OLLAMA DANS L'APPLICATION
 * Évite les dépendances externes et les plantages
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const util = require('util');

const execAsync = util.promisify(exec);

class OllamaDirectIntegration {
    constructor() {
        this.ollamaProcess = null;
        this.isRunning = false;
        this.port = 11435; // Nouveau serveur Ollama 0.9.0
        this.baseUrl = `http://localhost:${this.port}`;
        // Utiliser le chemin par défaut d'Ollama où les modèles existent déjà
        this.modelsPath = null; // Laisser Ollama utiliser son chemin par défaut
        this.ollamaPath = null;
        
        console.log('🤖 Intégration directe Ollama initialisée');
    }

    /**
     * Détecte l'installation d'Ollama
     */
    async detectOllamaInstallation() {
        try {
            // Vérifier les chemins communs d'Ollama (NOUVEAU binaire en premier)
            const possiblePaths = [
                path.join(process.env.HOME, 'ollama-latest'), // NOUVEAU binaire 0.9.0
                '/usr/local/bin/ollama',
                '/opt/homebrew/bin/ollama',
                '/usr/bin/ollama',
                path.join(process.env.HOME, '.ollama', 'bin', 'ollama'),
                'ollama' // Dans le PATH
            ];

            for (const ollamaPath of possiblePaths) {
                try {
                    const { stdout } = await execAsync(`${ollamaPath} --version`);
                    if (stdout.includes('ollama version')) {
                        this.ollamaPath = ollamaPath;
                        console.log(`✅ Ollama trouvé: ${ollamaPath}`);

                        // Vérifier la version
                        const versionMatch = stdout.match(/ollama version is ([\d.]+)/);
                        if (versionMatch) {
                            const version = versionMatch[1];
                            console.log(`📋 Version Ollama détectée: ${version}`);

                            // Préférer la version 0.9.0+ pour DeepSeek R1-0528
                            const versionParts = version.split('.').map(Number);
                            const isNewVersion = versionParts[0] > 0 || (versionParts[0] === 0 && versionParts[1] >= 9);

                            if (isNewVersion) {
                                console.log(`🎉 Version compatible avec DeepSeek R1-0528 trouvée !`);
                                return true;
                            } else {
                                console.log(`⚠️ Version trop ancienne pour DeepSeek R1-0528, continuons la recherche...`);
                            }
                        }
                        return true;
                    }
                } catch (error) {
                    // Continuer la recherche
                }
            }

            console.log('❌ Ollama non trouvé dans les chemins standards');
            return false;
        } catch (error) {
            console.error('❌ Erreur détection Ollama:', error.message);
            return false;
        }
    }

    /**
     * Vérifier et mettre à jour Ollama automatiquement
     */
    async checkAndUpdateOllama() {
        try {
            // Vérifier la version actuelle
            const { stdout } = await execAsync('ollama --version');
            const currentVersion = stdout.match(/ollama version is ([\d.]+)/)?.[1];

            if (currentVersion) {
                console.log(`📋 Version actuelle d'Ollama: ${currentVersion}`);
                console.log('✅ Utilisation de la version existante (mise à jour automatique désactivée)');
                return false; // Pas de mise à jour automatique
            }
        } catch (error) {
            console.log('⚠️ Impossible de vérifier la version d\'Ollama');
        }
        return false;
    }

    /**
     * Mettre à jour Ollama
     */
    async updateOllama() {
        try {
            console.log('🔄 Mise à jour d\'Ollama en cours...');

            // Sur macOS, télécharger et installer la dernière version
            if (process.platform === 'darwin') {
                const downloadUrl = 'https://ollama.com/download/Ollama-darwin.zip';
                console.log('📥 Téléchargement de la dernière version d\'Ollama...');

                // Créer le répertoire de téléchargement
                const downloadDir = path.join(process.cwd(), 'temp');
                await execAsync(`mkdir -p ${downloadDir}`);

                // Télécharger
                await execAsync(`curl -L -o ${downloadDir}/Ollama-latest.zip ${downloadUrl}`);

                // Extraire et installer
                await execAsync(`cd ${downloadDir} && unzip -o Ollama-latest.zip`);
                await execAsync(`cp -R ${downloadDir}/Ollama.app /Applications/`);

                // Nettoyer
                await execAsync(`rm -rf ${downloadDir}`);

                console.log('✅ Ollama mis à jour avec succès');
                return true;
            } else {
                // Sur Linux, utiliser le script d'installation
                await execAsync('curl -fsSL https://ollama.com/install.sh | sh');
                console.log('✅ Ollama mis à jour avec succès');
                return true;
            }
        } catch (error) {
            console.log('❌ Erreur lors de la mise à jour d\'Ollama:', error.message);
            return false;
        }
    }

    /**
     * Installe Ollama si nécessaire
     */
    async installOllamaIfNeeded() {
        try {
            const isInstalled = await this.detectOllamaInstallation();
            
            if (!isInstalled) {
                console.log('📦 Installation d\'Ollama...');
                
                // Installation via curl (méthode officielle)
                const installCommand = 'curl -fsSL https://ollama.ai/install.sh | sh';
                await this.execPromise(installCommand);
                
                // Re-vérifier l'installation
                const isNowInstalled = await this.detectOllamaInstallation();
                if (!isNowInstalled) {
                    throw new Error('Installation d\'Ollama échouée');
                }
                
                console.log('✅ Ollama installé avec succès');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Erreur installation Ollama:', error.message);
            return false;
        }
    }

    /**
     * Démarre Ollama en mode intégré
     */
    async startOllamaIntegrated() {
        try {
            if (this.isRunning) {
                console.log('⚠️ Ollama déjà en cours d\'exécution');
                return true;
            }

            console.log('🚀 Démarrage d\'Ollama intégré...');

            // Configurer les variables d'environnement ULTRA-RAPIDES
            const env = {
                ...process.env,
                OLLAMA_HOST: `0.0.0.0:${this.port}`,
                // OPTIMISATIONS OLLAMA VALIDES
                OLLAMA_NUM_PARALLEL: '4',
                OLLAMA_MAX_LOADED_MODELS: '3',
                OLLAMA_KEEP_ALIVE: '-1',
                OLLAMA_LOAD_TIMEOUT: '30s',
                OLLAMA_GPU_LAYERS: '999'
            };

            // Démarrer Ollama
            this.ollamaProcess = spawn(this.ollamaPath, ['serve'], {
                env: env,
                stdio: ['pipe', 'pipe', 'pipe'],
                detached: false
            });

            // Gérer les événements
            this.ollamaProcess.stdout.on('data', (data) => {
                console.log(`[Ollama] ${data.toString().trim()}`);
            });

            this.ollamaProcess.stderr.on('data', (data) => {
                console.log(`[Ollama Error] ${data.toString().trim()}`);
            });

            this.ollamaProcess.on('close', (code) => {
                console.log(`[Ollama] Processus fermé avec le code ${code}`);
                this.isRunning = false;
                this.ollamaProcess = null;
            });

            this.ollamaProcess.on('error', (error) => {
                console.error(`[Ollama] Erreur processus:`, error.message);
                this.isRunning = false;
            });

            // Attendre que le serveur soit prêt
            await this.waitForOllamaReady();
            
            this.isRunning = true;
            console.log('✅ Ollama intégré démarré avec succès');
            
            return true;
        } catch (error) {
            console.error('❌ Erreur démarrage Ollama:', error.message);
            return false;
        }
    }

    /**
     * Attend qu'Ollama soit prêt
     */
    async waitForOllamaReady(maxAttempts = 30) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                const response = await axios.get(`${this.baseUrl}/api/version`, { timeout: 2000 });
                if (response.status === 200) {
                    console.log('✅ Ollama prêt');
                    return true;
                }
            } catch (error) {
                // Attendre et réessayer
                await this.sleep(1000);
            }
        }
        
        throw new Error('Ollama n\'a pas démarré dans les temps');
    }

    /**
     * S'assurer que les modèles nécessaires sont disponibles avec téléchargement automatique
     */
    async ensureModelsAvailable() {
        try {
            console.log('📋 Vérification des modèles existants...');

            // Modèles DeepSeek R1 disponibles
            const requiredModels = [
                'deepseek-r1:8b',  // Modèle principal disponible
                'deepseek-r1:7b'   // Modèle de fallback disponible
            ];

            const availableModels = await this.getAvailableModels();
            const availableModelNames = availableModels.map(m => m.name);

            for (const modelName of requiredModels) {
                if (availableModelNames.includes(modelName)) {
                    console.log(`✅ Modèle disponible: ${modelName} - PRÊT À UTILISER`);
                } else {
                    console.log(`⚠️ Modèle manquant: ${modelName} - IGNORÉ`);
                    // Pas de téléchargement automatique pour éviter les mises à jour
                }
            }

            return true;
        } catch (error) {
            console.error('❌ Erreur vérification modèles:', error.message);
            return false;
        }
    }

    /**
     * Télécharger automatiquement le dernier modèle
     */
    async downloadLatestModel(modelName) {
        try {
            console.log(`📥 Téléchargement du dernier ${modelName}...`);

            // Utiliser ollama pull pour télécharger le modèle
            const { stdout, stderr } = await execAsync(`ollama pull ${modelName}`);

            if (stderr && stderr.includes('requires a newer version')) {
                console.log('⚠️ Version d\'Ollama trop ancienne pour ce modèle');
                console.log('❌ Mise à jour automatique désactivée - utilisez les modèles existants');
                throw new Error('Version Ollama incompatible - utilisez les modèles déjà installés');
            } else {
                console.log(`✅ ${modelName} téléchargé avec succès !`);
            }

            return true;
        } catch (error) {
            console.log(`❌ Erreur téléchargement ${modelName}:`, error.message);
            return false;
        }
    }

    /**
     * Télécharge un modèle
     */
    async pullModel(modelName) {
        try {
            console.log(`📥 Téléchargement de ${modelName}...`);
            
            const response = await axios.post(`${this.baseUrl}/api/pull`, {
                name: modelName
            }, {
                timeout: 600000 // 10 minutes
            });

            console.log(`✅ Modèle ${modelName} téléchargé`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur téléchargement ${modelName}:`, error.message);
            return false;
        }
    }

    /**
     * Obtient la liste des modèles disponibles
     */
    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            return response.data.models || [];
        } catch (error) {
            console.error('❌ Erreur récupération modèles:', error.message);
            return [];
        }
    }

    /**
     * Pré-charge un modèle pour éviter les timeouts
     */
    async preloadModel(modelName) {
        try {
            console.log(`🔄 Pré-chargement du modèle: ${modelName}`);

            // Faire un appel simple pour charger le modèle en mémoire
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: "Hello",
                stream: false,
                options: {
                    num_predict: 1,
                    temperature: 0.1
                }
            }, {
                timeout: 300000 // 5 minutes pour le chargement initial
            });

            console.log(`✅ Modèle ${modelName} pré-chargé avec succès`);

            // Démarrer le maintien en vie pour ce modèle
            this.startKeepAlive(modelName);

            return true;
        } catch (error) {
            console.warn(`⚠️ Échec pré-chargement ${modelName}:`, error.message);
            return false;
        }
    }

    /**
     * Maintient un modèle en vie (ping toutes les 3 minutes)
     */
    startKeepAlive(modelName) {
        if (this.keepAliveIntervals && this.keepAliveIntervals[modelName]) {
            return; // Déjà en cours
        }

        if (!this.keepAliveIntervals) {
            this.keepAliveIntervals = {};
        }

        console.log(`🔄 Démarrage du maintien en vie pour ${modelName}`);

        this.keepAliveIntervals[modelName] = setInterval(async () => {
            try {
                await axios.post(`${this.baseUrl}/api/generate`, {
                    model: modelName,
                    prompt: "ping",
                    stream: false,
                    options: {
                        num_predict: 1,
                        temperature: 0.1
                    },
                    keep_alive: -1 // FORCER le maintien permanent
                }, { timeout: 5000 });

                console.log(`💓 Ping maintien en vie PERMANENT: ${modelName}`);
            } catch (error) {
                console.warn(`⚠️ Échec ping ${modelName}:`, error.message);
                // Réessayer le pré-chargement si échec
                setTimeout(() => this.preloadModel(modelName), 10000);
            }
        }, 120000); // Toutes les 2 minutes (plus fréquent)
    }

    /**
     * Arrête le maintien en vie d'un modèle
     */
    stopKeepAlive(modelName) {
        if (this.keepAliveIntervals && this.keepAliveIntervals[modelName]) {
            clearInterval(this.keepAliveIntervals[modelName]);
            delete this.keepAliveIntervals[modelName];
            console.log(`🛑 Arrêt du maintien en vie pour ${modelName}`);
        }
    }

    /**
     * Génère une réponse avec un modèle (avec gestion intelligente des timeouts)
     */
    async generateResponse(modelName, prompt, options = {}) {
        try {
            console.log(`🤖 Appel agent intégré: ${modelName} ${options.stream ? '(STREAMING)' : ''}`);

            // STREAMING SUPPORT pour voir la réflexion en temps réel !
            if (options.stream) {
                return this.generateStreamingResponse(modelName, prompt, options);
            }

            // Pour les gros modèles, essayer de pré-charger d'abord
            if (modelName.includes('34b') || modelName.includes('19gb') || modelName.includes('codellama')) {
                console.log(`🚀 Modèle lourd détecté (${modelName}), vérification du chargement...`);

                // Vérifier si le modèle est déjà chargé avec un ping rapide
                try {
                    await axios.post(`${this.baseUrl}/api/generate`, {
                        model: modelName,
                        prompt: "test",
                        stream: false,
                        options: { num_predict: 1 }
                    }, { timeout: 10000 }); // 10 secondes pour vérifier
                    console.log(`✅ Modèle ${modelName} déjà chargé`);
                } catch (pingError) {
                    console.log(`🔄 Modèle non chargé, pré-chargement en cours...`);
                    await this.preloadModel(modelName);
                }
            }

            // Générer la réponse principale (AVEC MAINTIEN PERMANENT)
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: prompt,
                stream: false,
                keep_alive: -1, // MAINTIEN PERMANENT DU MODÈLE
                options: {
                    temperature: 0.5,
                    top_p: 0.6,
                    num_predict: 200,          // Paramètre valide Ollama
                    num_ctx: 512,             // Paramètre valide Ollama
                    num_thread: Math.min(require('os').cpus().length, 4),
                    repeat_penalty: 1.01,
                    top_k: 5,
                    ...options
                }
            }, {
                timeout: 60000 // 60 secondes pour éviter timeouts
            });

            if (response.data && response.data.response) {
                console.log(`✅ Réponse générée: ${response.data.response.length} caractères`);
                return response.data.response;
            } else {
                console.warn('⚠️ Réponse vide du modèle');
                return null;
            }
        } catch (error) {
            console.error(`❌ Erreur génération ${modelName}:`, error.message);

            // Si c'est un timeout et le prompt est long, essayer avec un prompt plus court
            if (error.message.includes('timeout') && prompt.length > 500) {
                console.log(`🔄 Retry avec prompt raccourci pour ${modelName}...`);
                const shortPrompt = prompt.substring(0, 300) + "\n\nRéponds brièvement.";
                return await this.generateResponseSimple(modelName, shortPrompt, options);
            }

            return null;
        }
    }

    /**
     * Génération STREAMING pour voir la réflexion DeepSeek R1 en temps réel !
     */
    async generateStreamingResponse(modelName, prompt, options = {}) {
        try {
            console.log(`🌊 STREAMING activé pour ${modelName} - Réflexion visible en temps réel !`);

            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: prompt,
                stream: true,  // STREAMING ACTIVÉ !
                keep_alive: -1,
                options: {
                    temperature: options.temperature || 0.7,
                    top_p: options.top_p || 0.8,
                    num_predict: options.num_predict || 1024,
                    num_ctx: options.num_ctx || 4096,
                    repeat_penalty: options.repeat_penalty || 1.1,
                    ...options
                }
            }, {
                timeout: 120000, // 2 minutes pour streaming
                responseType: 'stream'
            });

            // Retourner le stream pour traitement en temps réel
            return response.data;

        } catch (error) {
            console.error(`❌ Erreur streaming ${modelName}:`, error.message);
            throw error;
        }
    }

    /**
     * Version simplifiée pour les retry
     */
    async generateResponseSimple(modelName, prompt, options = {}) {
        try {
            console.log(`🔄 Génération simplifiée pour ${modelName}`);
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: prompt,
                stream: false,
                keep_alive: -1, // MAINTIEN PERMANENT même en retry
                options: {
                    temperature: 0.7,
                    num_predict: Math.min(options.num_predict || options.max_tokens || 400, 400),
                    num_thread: 2
                }
            }, {
                timeout: 120000 // 2 minutes pour le retry
            });

            if (response.data && response.data.response) {
                console.log(`✅ Réponse retry générée: ${response.data.response.length} caractères`);
                return response.data.response;
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur retry ${modelName}:`, error.message);
            return null;
        }
    }

    /**
     * Arrête Ollama
     */
    async stopOllama() {
        try {
            if (this.ollamaProcess) {
                console.log('🛑 Arrêt d\'Ollama...');
                this.ollamaProcess.kill('SIGTERM');
                
                // Attendre l'arrêt
                await this.sleep(2000);
                
                if (this.ollamaProcess && !this.ollamaProcess.killed) {
                    this.ollamaProcess.kill('SIGKILL');
                }
                
                this.isRunning = false;
                this.ollamaProcess = null;
                console.log('✅ Ollama arrêté');
            }
        } catch (error) {
            console.error('❌ Erreur arrêt Ollama:', error.message);
        }
    }

    /**
     * Utilitaires
     */

    execPromise(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve({ stdout, stderr });
                }
            });
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtient le statut d'Ollama
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            port: this.port,
            baseUrl: this.baseUrl,
            ollamaPath: this.ollamaPath,
            modelsPath: this.modelsPath
        };
    }
}

// Instance globale
const ollamaIntegration = new OllamaDirectIntegration();

module.exports = {
    OllamaDirectIntegration,
    ollamaIntegration
};
