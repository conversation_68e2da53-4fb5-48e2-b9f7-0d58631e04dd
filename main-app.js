#!/usr/bin/env node

/**
 * 🧠 MAIN LOUNA AI - APPLICATION DESKTOP PRINCIPALE
 * Fichier principal pour l'application desktop LOUNA AI
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 DÉMARRAGE APPLICATION DESKTOP LOUNA AI');
console.log('==========================================');

const appPath = '/Applications/Louna AI.app/Contents/Resources';
const serverScript = path.join(appPath, 'server.js');

// Vérifier que le serveur existe
if (!fs.existsSync(serverScript)) {
    console.error('❌ Fichier serveur manquant:', serverScript);
    console.log('🔧 Utilisation du serveur local...');
    
    // Utiliser le serveur du projet local
    const localServer = path.join(__dirname, 'server.js');
    if (fs.existsSync(localServer)) {
        console.log('✅ Serveur local trouvé');
        startServer(localServer, __dirname);
    } else {
        console.error('❌ Aucun serveur disponible');
        process.exit(1);
    }
} else {
    startServer(serverScript, appPath);
}

function startServer(scriptPath, workingDir) {
    console.log('🔧 Démarrage du serveur backend...');
    console.log('📍 Script:', scriptPath);
    console.log('📁 Répertoire:', workingDir);

    // Démarrer le serveur
    const serverProcess = spawn('node', [scriptPath], {
        stdio: 'inherit',
        cwd: workingDir,
        env: { ...process.env, PORT: '52796' }
    });

    serverProcess.on('error', (error) => {
        console.error('❌ Erreur serveur:', error);
        process.exit(1);
    });

    serverProcess.on('exit', (code) => {
        console.log(`🛑 Serveur arrêté avec le code: ${code}`);
        process.exit(code);
    });

    // Attendre un peu puis ouvrir l'interface
    setTimeout(() => {
        console.log('🖥️ Ouverture de l\'interface desktop...');
        
        const { exec } = require('child_process');
        
        // Essayer Chrome en mode app
        exec('open -na "Google Chrome" --args --app="http://localhost:52796" --new-window --window-size=1400,900 --window-position=100,100 --user-data-dir="/tmp/louna-desktop-app"', (error) => {
            if (error) {
                console.warn('⚠️ Chrome non disponible, essai Safari...');
                exec('open -a Safari "http://localhost:52796"', (error2) => {
                    if (error2) {
                        console.warn('⚠️ Safari non disponible, navigateur par défaut...');
                        exec('open "http://localhost:52796"');
                    }
                });
            } else {
                console.log('✅ Interface desktop ouverte avec Chrome');
            }
        });
    }, 5000);

    // Gestion de l'arrêt
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt de LOUNA AI...');
        if (serverProcess) {
            serverProcess.kill('SIGTERM');
        }
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 Arrêt de LOUNA AI...');
        if (serverProcess) {
            serverProcess.kill('SIGTERM');
        }
        process.exit(0);
    });
}

console.log('✅ Application desktop LOUNA AI initialisée');
