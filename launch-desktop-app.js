#!/usr/bin/env node

/**
 * 🖥️ LANCEUR APPLICATION DESKTOP LOUNA AI
 * Lance l'application desktop native sans Electron
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🖥️ LANCEMENT APPLICATION DESKTOP LOUNA AI');
console.log('==========================================');

// Démarrer le serveur en arrière-plan
function startServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Démarrage du serveur backend...');
        
        const serverProcess = spawn('node', ['server.js'], {
            stdio: 'pipe',
            env: { ...process.env, PORT: '52796' },
            cwd: __dirname
        });

        let serverReady = false;

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('📡 Serveur:', output.trim());
            
            if (output.includes('SERVEUR LOUNA AI DÉMARRÉ') || 
                output.includes('Toutes les fonctionnalités') ||
                output.includes('port 52796')) {
                if (!serverReady) {
                    serverReady = true;
                    console.log('✅ Serveur backend prêt !');
                    resolve(serverProcess);
                }
            }
        });

        serverProcess.stderr.on('data', (data) => {
            console.error('❌ Erreur serveur:', data.toString());
        });

        serverProcess.on('error', (error) => {
            console.error('❌ Impossible de démarrer le serveur:', error);
            reject(error);
        });

        // Timeout de sécurité
        setTimeout(() => {
            if (!serverReady) {
                console.log('⏰ Timeout - Serveur considéré comme prêt');
                resolve(serverProcess);
            }
        }, 15000);
    });
}

// Lancer l'application dans le navigateur par défaut en mode app
function launchDesktopApp() {
    const url = 'http://localhost:52796';
    
    console.log('🖥️ Lancement de l\'application desktop...');
    console.log(`🌐 URL: ${url}`);
    
    // Commandes pour lancer en mode application selon l'OS
    const commands = {
        darwin: [
            // Chrome/Chromium en mode app (macOS)
            `open -a "Google Chrome" --args --app="${url}" --new-window --disable-web-security --allow-running-insecure-content`,
            // Safari en mode app
            `open -a Safari "${url}"`,
            // Navigateur par défaut
            `open "${url}"`
        ],
        win32: [
            // Chrome en mode app (Windows)
            `start chrome --app="${url}" --new-window`,
            // Edge en mode app
            `start msedge --app="${url}"`,
            // Navigateur par défaut
            `start "${url}"`
        ],
        linux: [
            // Chrome/Chromium en mode app (Linux)
            `google-chrome --app="${url}" --new-window`,
            `chromium-browser --app="${url}" --new-window`,
            // Firefox
            `firefox "${url}"`,
            // Navigateur par défaut
            `xdg-open "${url}"`
        ]
    };

    const osCommands = commands[process.platform] || commands.linux;
    
    // Essayer chaque commande jusqu'à ce qu'une fonctionne
    function tryCommand(index = 0) {
        if (index >= osCommands.length) {
            console.error('❌ Impossible de lancer l\'application desktop');
            return;
        }

        const command = osCommands[index];
        console.log(`🔄 Tentative ${index + 1}: ${command}`);
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.warn(`⚠️ Commande ${index + 1} échouée:`, error.message);
                tryCommand(index + 1);
            } else {
                console.log('✅ Application desktop lancée avec succès !');
                console.log('🎯 LOUNA AI est maintenant accessible en mode desktop');
                
                // Afficher les informations de l'application
                setTimeout(() => {
                    console.log('\n🧠 LOUNA AI - APPLICATION DESKTOP ACTIVE');
                    console.log('==========================================');
                    console.log('🌡️ Mémoire thermique: ACTIVE');
                    console.log('🧬 Neurogenèse: EN COURS');
                    console.log('🎓 Formation avancée: ACTIVE');
                    console.log('🎤👁️ Analyse multimodale: ACTIVE');
                    console.log('💻 Interface desktop: OPÉRATIONNELLE');
                    console.log('==========================================');
                    console.log('✨ Votre IA autonome est prête !');
                }, 3000);
            }
        });
    }

    tryCommand();
}

// Fonction principale
async function main() {
    try {
        console.log('🔧 Vérification des prérequis...');
        
        // Vérifier que le serveur existe
        if (!fs.existsSync(path.join(__dirname, 'server.js'))) {
            throw new Error('Fichier server.js introuvable');
        }
        
        // Vérifier que le dossier public existe
        if (!fs.existsSync(path.join(__dirname, 'public'))) {
            throw new Error('Dossier public introuvable');
        }
        
        console.log('✅ Prérequis validés');
        
        // Démarrer le serveur
        const serverProcess = await startServer();
        
        // Attendre que le serveur soit complètement prêt
        console.log('⏳ Attente de la stabilisation du serveur...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Lancer l'application desktop
        launchDesktopApp();
        
        // Gestion de l'arrêt propre
        process.on('SIGINT', () => {
            console.log('\n🛑 Arrêt de LOUNA AI...');
            if (serverProcess) {
                serverProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
        
        process.on('SIGTERM', () => {
            console.log('\n🛑 Arrêt de LOUNA AI...');
            if (serverProcess) {
                serverProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ Erreur lors du lancement:', error.message);
        process.exit(1);
    }
}

// Lancer l'application
main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
});

console.log('🚀 Lanceur desktop LOUNA AI initialisé...');
