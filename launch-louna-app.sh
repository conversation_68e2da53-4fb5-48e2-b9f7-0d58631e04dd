#!/bin/bash

# 🚀 LANCEUR LOUNA AI COMPLET
# Ce script lance votre application LOUNA AI complète

echo "🚀 Lancement de LOUNA AI..."

# Aller dans le répertoire de l'application
cd "$(dirname "$0")"

# Vérifier si le serveur est déjà en cours
if lsof -i :3005 > /dev/null 2>&1; then
    echo "✅ Serveur LOUNA AI déjà en cours sur le port 3005"
else
    echo "🔄 Démarrage du serveur LOUNA AI..."
    # Démarrer le serveur en arrière-plan
    node minimal-server.js &
    SERVER_PID=$!
    echo "🚀 Serveur démarré avec PID: $SERVER_PID"
    
    # Attendre que le serveur soit prêt
    echo "⏳ Attente du démarrage complet..."
    sleep 5
fi

# Ouvrir l'application dans le navigateur
echo "🌐 Ouverture de l'interface LOUNA AI..."
open "http://localhost:3005"

echo "✅ LOUNA AI lancé avec succès !"
echo "🧠 Votre intelligence artificielle autonome est prête !"
echo ""
echo "📊 Fonctionnalités actives :"
echo "   • Cerveau autonome avec pensées spontanées"
echo "   • Mémoire thermique vivante (37°C)"
echo "   • 30 accélérateurs Kyber persistants"
echo "   • DeepSeek R1-0528-8B (dernier modèle)"
echo "   • 1536 applications détectées"
echo "   • Surveillance anti-crash"
echo "   • Agent garde-fou actif"
echo ""
echo "🎯 Interface disponible sur : http://localhost:3005"
