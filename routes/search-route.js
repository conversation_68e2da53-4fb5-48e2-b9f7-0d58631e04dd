/**
 * Route de recherche pour Louna AI
 * Gère les requêtes de recherche et les résultats
 */

const express = require('express');
const { getLogger } = require('../utils/logger');

const router = express.Router();
const logger = getLogger();

// SUPPRIMÉ - Plus de base de données simulée
// Utilise maintenant la vraie mémoire thermique de LOUNA
const getRealSearchData = () => {
  // Récupérer les vraies données de la mémoire thermique
  if (global.thermalMemory) {
    const memoryEntries = global.thermalMemory.getAllEntries ? global.thermalMemory.getAllEntries() : [];
    return {
      documents: memoryEntries.map((entry, index) => ({
        id: index + 1,
        title: `Mémoire ${entry.type || 'Système'}`,
        content: entry.content || entry.data || 'Données mémoire thermique',
        category: entry.type || 'MEMORY',
        tags: [entry.type, 'mémoire', 'thermique', 'réel'],
        timestamp: entry.timestamp || new Date().toISOString(),
        importance: entry.importance || 0.5,
        temperature: entry.temperature || 0.37
      })),
    {
      id: 2,
      title: "Apprentissage Automatique et Réseaux de Neurones",
      content: "Les réseaux de neurones sont la base de l'apprentissage automatique moderne.",
      category: "ML",
      tags: ["apprentissage", "neurones", "réseaux", "machine learning"],
      timestamp: new Date().toISOString()
    },
    {
      id: 3,
      title: "Éthique en Intelligence Artificielle",
      content: "L'éthique est cruciale dans le développement d'IA responsable et bénéfique.",
      category: "Ethics",
      tags: ["éthique", "responsabilité", "développement", "bénéfique"],
      timestamp: new Date().toISOString()
    },
    {
      id: 4,
      title: "Traitement du Langage Naturel",
      content: "Le NLP permet aux machines de comprendre et traiter le langage humain.",
      category: "NLP",
      tags: ["langage", "naturel", "traitement", "compréhension"],
      timestamp: new Date().toISOString()
    },
    {
      id: 5,
      title: "Vision par Ordinateur",
      content: "La vision par ordinateur permet aux machines de voir et interpréter le monde visuel.",
      category: "CV",
      tags: ["vision", "ordinateur", "image", "interprétation"],
      timestamp: new Date().toISOString()
    }
  ],
  conversations: [
    {
      id: 1,
      query: "Qu'est-ce que l'intelligence artificielle ?",
      response: "L'intelligence artificielle est la capacité des machines à imiter l'intelligence humaine.",
      timestamp: new Date().toISOString()
    },
    {
      id: 2,
      query: "Comment fonctionne l'apprentissage automatique ?",
      response: "L'apprentissage automatique utilise des algorithmes pour apprendre à partir de données.",
      timestamp: new Date().toISOString()
    }
  ]
};

/**
 * Route principale de recherche
 * GET /search?q=terme&type=all&limit=10&offset=0
 */
router.get('/', async (req, res) => {
  try {
    const searchStart = Date.now();
    const {
      q: query = '',
      type = 'all',
      limit = 10,
      offset = 0,
      category = '',
      sort = 'relevance'
    } = req.query;

    logger.info('Requête de recherche reçue', {
      component: 'SEARCH_ROUTE',
      query,
      type,
      limit: parseInt(limit),
      offset: parseInt(offset),
      category,
      sort
    });

    // Validation des paramètres
    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Paramètre de recherche manquant',
        message: 'Le paramètre "q" est requis'
      });
    }

    // Effectuer la recherche
    const searchResults = await performSearch({
      query: query.trim(),
      type,
      limit: parseInt(limit),
      offset: parseInt(offset),
      category,
      sort
    });

    const searchDuration = Date.now() - searchStart;

    logger.info('Recherche effectuée avec succès', {
      component: 'SEARCH_ROUTE',
      query,
      resultsCount: searchResults.results.length,
      totalResults: searchResults.total,
      duration: searchDuration
    });

    res.json({
      success: true,
      query,
      results: searchResults.results,
      pagination: {
        total: searchResults.total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: searchResults.total > parseInt(offset) + parseInt(limit)
      },
      metadata: {
        searchTime: searchDuration,
        type,
        category,
        sort
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la recherche', {
      component: 'SEARCH_ROUTE',
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur',
      message: 'Une erreur est survenue lors de la recherche'
    });
  }
});

/**
 * Route de recherche avancée
 * POST /search/advanced
 */
router.post('/advanced', async (req, res) => {
  try {
    const searchStart = Date.now();
    const {
      query = '',
      filters = {},
      facets = [],
      boost = {},
      explain = false
    } = req.body;

    logger.info('Recherche avancée reçue', {
      component: 'SEARCH_ROUTE',
      query,
      filters,
      facets,
      boost,
      explain
    });

    // Effectuer la recherche avancée
    const searchResults = await performAdvancedSearch({
      query,
      filters,
      facets,
      boost,
      explain
    });

    const searchDuration = Date.now() - searchStart;

    res.json({
      success: true,
      query,
      results: searchResults.results,
      facets: searchResults.facets,
      aggregations: searchResults.aggregations,
      explanation: explain ? searchResults.explanation : undefined,
      metadata: {
        searchTime: searchDuration,
        totalResults: searchResults.total
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la recherche avancée', {
      component: 'SEARCH_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur',
      message: 'Une erreur est survenue lors de la recherche avancée'
    });
  }
});

/**
 * Route de suggestions de recherche
 * GET /search/suggestions?q=terme&limit=5
 */
router.get('/suggestions', async (req, res) => {
  try {
    const { q: query = '', limit = 5 } = req.query;

    if (!query || query.trim().length < 2) {
      return res.json({
        success: true,
        suggestions: []
      });
    }

    const suggestions = await generateSuggestions(query.trim(), parseInt(limit));

    res.json({
      success: true,
      query,
      suggestions
    });

  } catch (error) {
    logger.error('Erreur lors de la génération de suggestions', {
      component: 'SEARCH_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Route de recherche dans l'historique
 * GET /search/history?limit=20
 */
router.get('/history', async (req, res) => {
  try {
    const { limit = 20, offset = 0 } = req.query;

    const history = await getSearchHistory(parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      history: history.results,
      pagination: {
        total: history.total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique', {
      component: 'SEARCH_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Effectue une recherche simple
 */
async function performSearch(params) {
  const { query, type, limit, offset, category, sort } = params;
  
  let results = [];
  
  // Rechercher dans les documents
  if (type === 'all' || type === 'documents') {
    const documentResults = searchInDocuments(query, category);
    results = results.concat(documentResults.map(doc => ({
      ...doc,
      type: 'document',
      relevance: calculateRelevance(query, doc.title + ' ' + doc.content)
    })));
  }
  
  // Rechercher dans les conversations
  if (type === 'all' || type === 'conversations') {
    const conversationResults = searchInConversations(query);
    results = results.concat(conversationResults.map(conv => ({
      ...conv,
      type: 'conversation',
      relevance: calculateRelevance(query, conv.query + ' ' + conv.response)
    })));
  }
  
  // Trier les résultats
  if (sort === 'relevance') {
    results.sort((a, b) => b.relevance - a.relevance);
  } else if (sort === 'date') {
    results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }
  
  // Pagination
  const total = results.length;
  const paginatedResults = results.slice(offset, offset + limit);
  
  return {
    results: paginatedResults,
    total
  };
}

/**
 * Effectue une recherche avancée
 */
async function performAdvancedSearch(params) {
  const { query, filters, facets, boost, explain } = params;
  
  // Simulation d'une recherche avancée
  let results = searchDatabase.documents.filter(doc => {
    let matches = true;
    
    // Appliquer les filtres
    if (filters.category && doc.category !== filters.category) {
      matches = false;
    }
    
    if (filters.tags && filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => 
        doc.tags.some(docTag => docTag.toLowerCase().includes(tag.toLowerCase()))
      );
      if (!hasMatchingTag) matches = false;
    }
    
    // Recherche textuelle
    if (query) {
      const searchText = (doc.title + ' ' + doc.content).toLowerCase();
      const queryLower = query.toLowerCase();
      if (!searchText.includes(queryLower)) {
        matches = false;
      }
    }
    
    return matches;
  });
  
  // Appliquer les boosts
  results = results.map(doc => ({
    ...doc,
    relevance: calculateRelevance(query, doc.title + ' ' + doc.content, boost)
  }));
  
  // Générer les facettes
  const generatedFacets = generateFacets(results, facets);
  
  // Générer les agrégations
  const aggregations = generateAggregations(results);
  
  return {
    results: results.slice(0, 20), // Limiter à 20 résultats
    total: results.length,
    facets: generatedFacets,
    aggregations,
    explanation: explain ? generateExplanation(query, results) : undefined
  };
}

/**
 * Recherche dans les documents
 */
function searchInDocuments(query, category) {
  const queryLower = query.toLowerCase();
  
  return searchDatabase.documents.filter(doc => {
    // Filtrer par catégorie si spécifiée
    if (category && doc.category !== category) {
      return false;
    }
    
    // Recherche textuelle
    const searchText = (doc.title + ' ' + doc.content + ' ' + doc.tags.join(' ')).toLowerCase();
    return searchText.includes(queryLower);
  });
}

/**
 * Recherche dans les conversations
 */
function searchInConversations(query) {
  const queryLower = query.toLowerCase();
  
  return searchDatabase.conversations.filter(conv => {
    const searchText = (conv.query + ' ' + conv.response).toLowerCase();
    return searchText.includes(queryLower);
  });
}

/**
 * Calcule la pertinence d'un résultat
 */
function calculateRelevance(query, text, boost = {}) {
  const queryLower = query.toLowerCase();
  const textLower = text.toLowerCase();
  
  let relevance = 0;
  
  // Correspondance exacte
  if (textLower.includes(queryLower)) {
    relevance += 10;
  }
  
  // Correspondance des mots individuels
  const queryWords = queryLower.split(/\s+/);
  queryWords.forEach(word => {
    if (textLower.includes(word)) {
      relevance += 5;
    }
  });
  
  // Appliquer les boosts
  if (boost.title && textLower.startsWith(queryLower)) {
    relevance *= boost.title;
  }
  
  return relevance;
}

/**
 * Génère des suggestions de recherche
 */
async function generateSuggestions(query, limit) {
  const queryLower = query.toLowerCase();
  const suggestions = [];
  
  // Suggestions basées sur les titres de documents
  searchDatabase.documents.forEach(doc => {
    if (doc.title.toLowerCase().includes(queryLower)) {
      suggestions.push({
        text: doc.title,
        type: 'document',
        category: doc.category
      });
    }
  });
  
  // Suggestions basées sur les tags
  const allTags = searchDatabase.documents.flatMap(doc => doc.tags);
  const uniqueTags = [...new Set(allTags)];
  
  uniqueTags.forEach(tag => {
    if (tag.toLowerCase().includes(queryLower)) {
      suggestions.push({
        text: tag,
        type: 'tag'
      });
    }
  });
  
  // Suggestions basées sur les requêtes précédentes
  searchDatabase.conversations.forEach(conv => {
    if (conv.query.toLowerCase().includes(queryLower)) {
      suggestions.push({
        text: conv.query,
        type: 'previous_query'
      });
    }
  });
  
  // Limiter et dédupliquer
  const uniqueSuggestions = suggestions
    .filter((suggestion, index, self) => 
      index === self.findIndex(s => s.text === suggestion.text)
    )
    .slice(0, limit);
  
  return uniqueSuggestions;
}

/**
 * Récupère l'historique de recherche
 */
async function getSearchHistory(limit, offset) {
  // Simulation de l'historique
  const history = searchDatabase.conversations
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    .slice(offset, offset + limit);
  
  return {
    results: history,
    total: searchDatabase.conversations.length
  };
}

/**
 * Génère les facettes
 */
function generateFacets(results, requestedFacets) {
  const facets = {};
  
  if (requestedFacets.includes('category')) {
    const categories = {};
    results.forEach(result => {
      if (result.category) {
        categories[result.category] = (categories[result.category] || 0) + 1;
      }
    });
    facets.category = Object.entries(categories).map(([name, count]) => ({ name, count }));
  }
  
  if (requestedFacets.includes('tags')) {
    const tags = {};
    results.forEach(result => {
      if (result.tags) {
        result.tags.forEach(tag => {
          tags[tag] = (tags[tag] || 0) + 1;
        });
      }
    });
    facets.tags = Object.entries(tags)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
  
  return facets;
}

/**
 * Génère les agrégations
 */
function generateAggregations(results) {
  return {
    totalResults: results.length,
    averageRelevance: results.length > 0 ? 
      results.reduce((sum, r) => sum + (r.relevance || 0), 0) / results.length : 0,
    categoriesCount: new Set(results.map(r => r.category).filter(Boolean)).size,
    tagsCount: new Set(results.flatMap(r => r.tags || [])).size
  };
}

/**
 * Génère une explication de la recherche
 */
function generateExplanation(query, results) {
  return {
    query,
    strategy: 'text_matching_with_relevance_scoring',
    factors: [
      'Correspondance exacte de la requête',
      'Correspondance des mots individuels',
      'Pondération par type de contenu',
      'Tri par pertinence'
    ],
    resultsAnalysis: {
      totalFound: results.length,
      averageRelevance: results.length > 0 ? 
        results.reduce((sum, r) => sum + (r.relevance || 0), 0) / results.length : 0,
      topRelevance: results.length > 0 ? Math.max(...results.map(r => r.relevance || 0)) : 0
    }
  };
}

module.exports = router;
