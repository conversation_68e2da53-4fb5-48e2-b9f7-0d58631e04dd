/**
 * Route de navigateur web pour Louna AI
 * Gère la navigation web et l'extraction de contenu
 */

const express = require('express');
const { getLogger } = require('../utils/logger');

const router = express.Router();
const logger = getLogger();

// Simulateur de navigateur web
const webBrowser = {
  sessions: [],
  bookmarks: [
    {
      id: 1,
      title: 'Documentation Louna AI',
      url: 'https://docs.louna-ai.com',
      category: 'documentation',
      createdAt: new Date().toISOString()
    },
    {
      id: 2,
      title: 'GitHub Repository',
      url: 'https://github.com/louna-ai/core',
      category: 'development',
      createdAt: new Date().toISOString()
    },
    {
      id: 3,
      title: 'OpenAI API Documentation',
      url: 'https://platform.openai.com/docs',
      category: 'api',
      createdAt: new Date().toISOString()
    }
  ],
  history: [],
  
  /**
   * Crée une nouvelle session de navigation
   */
  createSession: (userAgent = 'Louna-AI-Browser/1.0') => {
    const session = {
      id: Date.now(),
      userAgent,
      cookies: [],
      currentUrl: null,
      history: [],
      tabs: [],
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
    
    webBrowser.sessions.push(session);
    
    // Garder seulement les 10 dernières sessions
    if (webBrowser.sessions.length > 10) {
      webBrowser.sessions.shift();
    }
    
    return session;
  },
  
  /**
   * Simule la navigation vers une URL
   */
  navigateToUrl: (sessionId, url) => {
    const session = webBrowser.sessions.find(s => s.id === sessionId);
    if (!session) return null;
    
    // Simuler le contenu de la page
    const pageContent = webBrowser.simulatePageContent(url);
    
    const navigationEntry = {
      url,
      title: pageContent.title,
      timestamp: new Date().toISOString(),
      loadTime: Math.floor(Math.random() * 2000) + 500, // 500-2500ms
      status: 'success'
    };
    
    session.currentUrl = url;
    session.history.push(navigationEntry);
    session.lastActivity = new Date().toISOString();
    
    // Ajouter à l'historique global
    webBrowser.history.push({
      sessionId,
      ...navigationEntry
    });
    
    // Garder seulement les 100 dernières entrées d'historique
    if (webBrowser.history.length > 100) {
      webBrowser.history.shift();
    }
    
    return {
      session,
      page: pageContent,
      navigation: navigationEntry
    };
  },
  
  /**
   * SUPPRIMÉ - Plus de simulation de contenu web
   * Utilise maintenant web-fetch pour récupérer le vrai contenu
   */
  getRealPageContent: async (url) => {
    try {
      // Utiliser web-fetch pour récupérer le vrai contenu
      const response = await fetch(url);
      const content = await response.text();

      return {
        title: `Page réelle - ${new URL(url).hostname}`,
        content: content.substring(0, 1000) + '...', // Limiter la taille
        type: 'real_content',
        language: 'auto-detect',
        url: url,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        title: `Erreur - ${new URL(url).hostname}`,
        content: `Impossible de récupérer le contenu réel de ${url}: ${error.message}`,
        type: 'error',
        language: 'fr',
        url: url,
        timestamp: new Date().toISOString()
      };
    }
    
    return {
      url,
      title: template.title,
      content: template.content,
      type: template.type,
      language: template.language,
      metadata: {
        description: `Description de ${template.title}`,
        keywords: ['louna', 'ai', 'intelligence', 'artificielle'],
        author: 'Louna AI Team',
        lastModified: new Date().toISOString()
      },
      links: [
        { text: 'Accueil', url: `https://${domain}/` },
        { text: 'Documentation', url: `https://${domain}/docs` },
        { text: 'Contact', url: `https://${domain}/contact` }
      ],
      images: [
        { alt: 'Logo Louna AI', src: `https://${domain}/logo.png` },
        { alt: 'Screenshot', src: `https://${domain}/screenshot.png` }
      ]
    };
  },
  
  /**
   * Extrait du texte d'une page
   */
  extractText: (url) => {
    const pageContent = webBrowser.simulatePageContent(url);
    return {
      url,
      title: pageContent.title,
      text: pageContent.content,
      wordCount: pageContent.content.split(' ').length,
      extractedAt: new Date().toISOString()
    };
  }
};

/**
 * Créer une nouvelle session de navigation
 * POST /api/web-browser/sessions
 */
router.post('/sessions', async (req, res) => {
  try {
    const { userAgent } = req.body;

    logger.info('Requête de création de session de navigation reçue', {
      component: 'WEB_BROWSER_ROUTE',
      userAgent
    });

    const session = webBrowser.createSession(userAgent);

    logger.info('Session de navigation créée avec succès', {
      component: 'WEB_BROWSER_ROUTE',
      sessionId: session.id
    });

    res.status(201).json({
      success: true,
      session,
      message: 'Session de navigation créée avec succès',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la création de session', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Naviguer vers une URL
 * POST /api/web-browser/navigate
 */
router.post('/navigate', async (req, res) => {
  try {
    const { sessionId, url } = req.body;

    logger.info('Requête de navigation reçue', {
      component: 'WEB_BROWSER_ROUTE',
      sessionId,
      url
    });

    if (!sessionId || !url) {
      return res.status(400).json({
        success: false,
        error: 'Session ID et URL requis'
      });
    }

    // Valider l'URL
    try {
      new URL(url);
    } catch {
      return res.status(400).json({
        success: false,
        error: 'URL invalide'
      });
    }

    const result = webBrowser.navigateToUrl(sessionId, url);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Session non trouvée'
      });
    }

    logger.info('Navigation effectuée avec succès', {
      component: 'WEB_BROWSER_ROUTE',
      sessionId,
      url,
      loadTime: result.navigation.loadTime
    });

    res.json({
      success: true,
      result,
      message: 'Navigation effectuée avec succès',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la navigation', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Extraire le contenu d'une page
 * POST /api/web-browser/extract
 */
router.post('/extract', async (req, res) => {
  try {
    const { url, type = 'text' } = req.body;

    logger.info('Requête d\'extraction de contenu reçue', {
      component: 'WEB_BROWSER_ROUTE',
      url,
      type
    });

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL requise'
      });
    }

    // Valider l'URL
    try {
      new URL(url);
    } catch {
      return res.status(400).json({
        success: false,
        error: 'URL invalide'
      });
    }

    let extractedContent;

    switch (type) {
      case 'text':
        extractedContent = webBrowser.extractText(url);
        break;
      case 'full':
        extractedContent = webBrowser.simulatePageContent(url);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Type d\'extraction non supporté'
        });
    }

    logger.info('Extraction de contenu effectuée', {
      component: 'WEB_BROWSER_ROUTE',
      url,
      type,
      contentLength: extractedContent.content?.length || extractedContent.text?.length
    });

    res.json({
      success: true,
      content: extractedContent,
      type,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'extraction', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Obtenir l'historique de navigation
 * GET /api/web-browser/history
 */
router.get('/history', async (req, res) => {
  try {
    const { sessionId, limit = 50 } = req.query;

    logger.info('Requête d\'historique de navigation reçue', {
      component: 'WEB_BROWSER_ROUTE',
      sessionId,
      limit: parseInt(limit)
    });

    let history = [...webBrowser.history];

    // Filtrer par session si spécifié
    if (sessionId) {
      history = history.filter(entry => entry.sessionId === parseInt(sessionId));
    }

    // Trier par timestamp (plus récent en premier)
    history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Limiter les résultats
    history = history.slice(0, parseInt(limit));

    res.json({
      success: true,
      history,
      count: history.length,
      filters: { sessionId, limit: parseInt(limit) },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Obtenir les signets
 * GET /api/web-browser/bookmarks
 */
router.get('/bookmarks', async (req, res) => {
  try {
    const { category } = req.query;

    logger.info('Requête de signets reçue', {
      component: 'WEB_BROWSER_ROUTE',
      category
    });

    let bookmarks = [...webBrowser.bookmarks];

    // Filtrer par catégorie si spécifié
    if (category) {
      bookmarks = bookmarks.filter(bookmark => bookmark.category === category);
    }

    // Trier par titre
    bookmarks.sort((a, b) => a.title.localeCompare(b.title));

    res.json({
      success: true,
      bookmarks,
      count: bookmarks.length,
      categories: [...new Set(webBrowser.bookmarks.map(b => b.category))],
      filters: { category },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des signets', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Ajouter un signet
 * POST /api/web-browser/bookmarks
 */
router.post('/bookmarks', async (req, res) => {
  try {
    const { title, url, category = 'general' } = req.body;

    logger.info('Requête d\'ajout de signet reçue', {
      component: 'WEB_BROWSER_ROUTE',
      title,
      url,
      category
    });

    if (!title || !url) {
      return res.status(400).json({
        success: false,
        error: 'Titre et URL requis'
      });
    }

    // Valider l'URL
    try {
      new URL(url);
    } catch {
      return res.status(400).json({
        success: false,
        error: 'URL invalide'
      });
    }

    const bookmark = {
      id: Date.now(),
      title,
      url,
      category,
      createdAt: new Date().toISOString()
    };

    webBrowser.bookmarks.push(bookmark);

    logger.info('Signet ajouté avec succès', {
      component: 'WEB_BROWSER_ROUTE',
      bookmarkId: bookmark.id,
      title
    });

    res.status(201).json({
      success: true,
      bookmark,
      message: 'Signet ajouté avec succès',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'ajout de signet', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Obtenir les sessions actives
 * GET /api/web-browser/sessions
 */
router.get('/sessions', async (req, res) => {
  try {
    logger.info('Requête de sessions actives reçue', {
      component: 'WEB_BROWSER_ROUTE'
    });

    const sessions = webBrowser.sessions.map(session => ({
      ...session,
      isActive: Date.now() - new Date(session.lastActivity).getTime() < 300000, // 5 minutes
      historyCount: session.history.length
    }));

    res.json({
      success: true,
      sessions,
      count: sessions.length,
      activeSessions: sessions.filter(s => s.isActive).length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des sessions', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

/**
 * Rechercher dans l'historique
 * GET /api/web-browser/search
 */
router.get('/search', async (req, res) => {
  try {
    const { query, limit = 20 } = req.query;

    logger.info('Requête de recherche dans l\'historique reçue', {
      component: 'WEB_BROWSER_ROUTE',
      query,
      limit: parseInt(limit)
    });

    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Terme de recherche requis'
      });
    }

    const searchTerm = query.toLowerCase();
    const results = webBrowser.history.filter(entry => 
      entry.title.toLowerCase().includes(searchTerm) ||
      entry.url.toLowerCase().includes(searchTerm)
    );

    // Trier par pertinence (titre en premier, puis URL)
    results.sort((a, b) => {
      const aTitle = a.title.toLowerCase().includes(searchTerm);
      const bTitle = b.title.toLowerCase().includes(searchTerm);
      
      if (aTitle && !bTitle) return -1;
      if (!aTitle && bTitle) return 1;
      
      return new Date(b.timestamp) - new Date(a.timestamp);
    });

    const limitedResults = results.slice(0, parseInt(limit));

    res.json({
      success: true,
      results: limitedResults,
      count: limitedResults.length,
      totalMatches: results.length,
      query,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la recherche', {
      component: 'WEB_BROWSER_ROUTE',
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
