class NeuralAnimation {
    constructor(canvasId, options = {}) {
        this.canvas = document.getElementById(canvasId);
        if (!this.canvas) {
            console.error('Canvas element not found:', canvasId);
            return;
        }
        
        this.ctx = this.canvas.getContext('2d');
        this.options = {
            nodeCount: options.nodeCount || 120,
            connectionCount: options.connectionCount || 200,
            nodeColor: options.nodeColor || '#2980b9',
            activeNodeColor: options.activeNodeColor || '#e74c3c',
            connectionColor: options.connectionColor || 'rgba(41, 128, 185, 0.5)',
            activeConnectionColor: options.activeConnectionColor || 'rgba(231, 76, 60, 0.8)',
            backgroundColor: options.backgroundColor || 'rgba(26, 37, 48, 0.0)',
            nodeSize: options.nodeSize || 3,
            maxDistance: options.maxDistance || 100,
            animate: options.animate !== false,
            veilleMode: options.veilleMode || false,
            pulseFrequency: options.pulseFrequency || 2000,
            activityLevel: options.activityLevel || 0.5,
            particleCount: options.particleCount || 30,
            particleSpeed: options.particleSpeed || 1.5,
            showZones: options.showZones !== false
        };

        this.nodes = [];
        this.connections = [];
        this.particles = [];
        this.zones = [];
        this.cognitiveMetrics = {};
        
        this.init();
    }

    init() {
        this.resizeCanvas();
        this.createNodes();
        this.createConnections();
        this.createParticles();
        this.createZones();
        this.initCognitiveMetrics();
        
        if (this.options.animate) {
            this.animate();
        }
        
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        const rect = this.canvas.parentElement.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
    }

    createNodes() {
        this.nodes = [];
        for (let i = 0; i < this.options.nodeCount; i++) {
            this.nodes.push({
                id: i,
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                active: false,
                activeTime: 0,
                size: this.options.nodeSize + Math.random() * 2,
                zone: Math.floor(Math.random() * 6)
            });
        }
    }

    createConnections() {
        this.connections = [];
        for (let i = 0; i < this.nodes.length; i++) {
            for (let j = i + 1; j < this.nodes.length; j++) {
                const distance = this.getDistance(this.nodes[i], this.nodes[j]);
                if (distance < this.options.maxDistance && Math.random() < 0.1) {
                    this.connections.push({
                        nodeA: this.nodes[i],
                        nodeB: this.nodes[j],
                        active: false,
                        activeTime: 0,
                        strength: Math.random()
                    });
                }
            }
        }
    }

    createParticles() {
        this.particles = [];
        for (let i = 0; i < this.options.particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * this.options.particleSpeed,
                vy: (Math.random() - 0.5) * this.options.particleSpeed,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
    }

    createZones() {
        const zoneNames = ['Mémoire', 'Attention', 'Langage', 'Moteur', 'Visuel', 'Cognition'];
        this.zones = zoneNames.map((name, index) => ({
            id: index,
            name: name,
            x: (index % 3) * (this.canvas.width / 3) + (this.canvas.width / 6),
            y: Math.floor(index / 3) * (this.canvas.height / 2) + (this.canvas.height / 4),
            radius: 50,
            active: false,
            activeTime: 0,
            intensity: 0
        }));
    }

    initCognitiveMetrics() {
        this.cognitiveMetrics = {
            'Attention': { value: 0.7, target: 0.7 },
            'Mémoire': { value: 0.8, target: 0.8 },
            'Langage': { value: 0.6, target: 0.6 },
            'Parole': { value: 0.5, target: 0.5 },
            'Personnalité': { value: 0.9, target: 0.9 }
        };
    }

    getDistance(nodeA, nodeB) {
        return Math.sqrt(Math.pow(nodeA.x - nodeB.x, 2) + Math.pow(nodeA.y - nodeB.y, 2));
    }

    activateNode(nodeId, duration = 1000) {
        if (this.nodes[nodeId]) {
            this.nodes[nodeId].active = true;
            this.nodes[nodeId].activeTime = duration;
        }
    }

    activateZone(zoneId, duration = 2000, intensity = 0.8) {
        if (this.zones[zoneId]) {
            this.zones[zoneId].active = true;
            this.zones[zoneId].activeTime = duration;
            this.zones[zoneId].intensity = intensity;
            
            // Activer les nœuds dans cette zone
            this.nodes.forEach(node => {
                if (node.zone === zoneId) {
                    this.activateNode(node.id, duration);
                }
            });
        }
    }

    activateCognitiveFunction(functionName, duration = 2000, intensity = 0.8) {
        if (this.cognitiveMetrics[functionName]) {
            this.cognitiveMetrics[functionName].target = intensity;
            
            setTimeout(() => {
                this.cognitiveMetrics[functionName].target = this.cognitiveMetrics[functionName].value;
            }, duration);
        }
    }

    update() {
        // Mettre à jour les nœuds
        this.nodes.forEach(node => {
            if (node.active) {
                node.activeTime -= 16;
                if (node.activeTime <= 0) {
                    node.active = false;
                }
            }
            
            // Mouvement des nœuds
            node.x += node.vx;
            node.y += node.vy;
            
            // Rebond sur les bords
            if (node.x <= 0 || node.x >= this.canvas.width) node.vx *= -1;
            if (node.y <= 0 || node.y >= this.canvas.height) node.vy *= -1;
        });

        // Mettre à jour les zones
        this.zones.forEach(zone => {
            if (zone.active) {
                zone.activeTime -= 16;
                if (zone.activeTime <= 0) {
                    zone.active = false;
                    zone.intensity = 0;
                }
            }
        });

        // Mettre à jour les métriques cognitives
        Object.keys(this.cognitiveMetrics).forEach(key => {
            const metric = this.cognitiveMetrics[key];
            const diff = metric.target - metric.value;
            metric.value += diff * 0.02;
        });

        // Mettre à jour les particules
        this.particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            if (particle.x <= 0 || particle.x >= this.canvas.width) particle.vx *= -1;
            if (particle.y <= 0 || particle.y >= this.canvas.height) particle.vy *= -1;
        });
    }

    draw() {
        // Effacer le canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Dessiner les zones si activées
        if (this.options.showZones) {
            this.zones.forEach(zone => {
                if (zone.active) {
                    this.ctx.beginPath();
                    this.ctx.arc(zone.x, zone.y, zone.radius * zone.intensity, 0, Math.PI * 2);
                    this.ctx.fillStyle = `rgba(231, 76, 60, ${zone.intensity * 0.1})`;
                    this.ctx.fill();
                }
            });
        }

        // Dessiner les connexions
        this.connections.forEach(connection => {
            const distance = this.getDistance(connection.nodeA, connection.nodeB);
            if (distance < this.options.maxDistance) {
                this.ctx.beginPath();
                this.ctx.moveTo(connection.nodeA.x, connection.nodeA.y);
                this.ctx.lineTo(connection.nodeB.x, connection.nodeB.y);
                
                const opacity = 1 - (distance / this.options.maxDistance);
                if (connection.nodeA.active || connection.nodeB.active) {
                    this.ctx.strokeStyle = this.options.activeConnectionColor;
                } else {
                    this.ctx.strokeStyle = this.options.connectionColor.replace('0.5', opacity * 0.5);
                }
                this.ctx.stroke();
            }
        });

        // Dessiner les particules
        this.particles.forEach(particle => {
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fillStyle = `rgba(41, 128, 185, ${particle.opacity})`;
            this.ctx.fill();
        });

        // Dessiner les nœuds
        this.nodes.forEach(node => {
            this.ctx.beginPath();
            this.ctx.arc(node.x, node.y, node.size, 0, Math.PI * 2);
            
            if (node.active) {
                this.ctx.fillStyle = this.options.activeNodeColor;
                this.ctx.shadowBlur = 10;
                this.ctx.shadowColor = this.options.activeNodeColor;
            } else {
                this.ctx.fillStyle = this.options.nodeColor;
                this.ctx.shadowBlur = 0;
            }
            
            this.ctx.fill();
        });
    }

    animate() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.animate());
    }
}
