<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Mémoire Thermique Vivante</title>

    <!-- Scripts nécessaires -->
    <script src="/js/neural-animation.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(138, 43, 226, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo i {
            font-size: 2.5em;
            color: #8a2be2;
            animation: pulse 2s infinite;
        }

        .logo h1 {
            font-size: 2em;
            background: linear-gradient(45deg, #8a2be2, #4169e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .status.connected {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
            border: 1px solid #00ff00;
        }

        .status.disconnected {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
            border: 1px solid #ff0000;
        }

        .controls {
            display: flex;
            gap: 10px;
        }

        .control-button {
            background: rgba(65, 105, 225, 0.2);
            border: 1px solid #4169e1;
            color: #4169e1;
            padding: 10px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .control-button:hover {
            background: rgba(65, 105, 225, 0.4);
            transform: translateY(-2px);
        }

        .kyber-status {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(138, 43, 226, 0.1);
            padding: 10px 15px;
            border-radius: 10px;
            border: 1px solid rgba(138, 43, 226, 0.3);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff00;
            animation: pulse 2s infinite;
        }

        .status-indicator.disabled {
            background: #ff0000;
        }

        .kyber-boost-btn {
            background: linear-gradient(45deg, #8a2be2, #4169e1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .kyber-boost-btn:hover {
            transform: scale(1.1);
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chat-section {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(138, 43, 226, 0.3);
            display: flex;
            flex-direction: column;
            height: 600px;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(138, 43, 226, 0.3);
        }

        .chat-title {
            font-size: 1.5em;
            color: #8a2be2;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .chat-btn {
            background: rgba(65, 105, 225, 0.2);
            border: 1px solid #4169e1;
            color: #4169e1;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chat-btn:hover {
            background: rgba(65, 105, 225, 0.4);
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(45deg, #8a2be2, #4169e1);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-time {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-area {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 16px;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .message-input:focus {
            border-color: #8a2be2;
            box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
            outline: none;
        }

        .send-btn {
            background: linear-gradient(45deg, #8a2be2, #4169e1);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .metrics-panel {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(138, 43, 226, 0.3);
        }

        .metrics-title {
            font-size: 1.2em;
            color: #8a2be2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-item:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .metric-value {
            font-weight: bold;
            color: #8a2be2;
        }

        .brain-visual {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(138, 43, 226, 0.3);
            text-align: center;
        }

        .brain-3d {
            width: 150px;
            height: 150px;
            margin: 20px auto;
            background: radial-gradient(circle, #8a2be2, #4169e1);
            border-radius: 50%;
            position: relative;
            animation: brainPulse 3s infinite;
        }

        @keyframes brainPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .brain-3d::before {
            content: '🧠';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3em;
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .temperature-display {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(138, 43, 226, 0.3);
        }

        .temp-gauge {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .temp-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00, #ff0000);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 45%;
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>LOUNA AI</h1>
            </div>
            <div class="kyber-status">
                <div class="status-indicator" id="kyber-indicator"></div>
                <span>Accélérateurs Kyber</span>
                <button class="kyber-boost-btn" onclick="toggleKyberBoost()">
                    <i class="fas fa-rocket"></i> BOOST
                </button>
            </div>
            <div class="status connected" id="connection-status">
                <i class="fas fa-wifi"></i> Connecté
            </div>
            <div class="controls">
                <a href="#" class="control-button" onclick="openSettings()">
                    <i class="fas fa-cog"></i>
                </a>
                <a href="#" class="control-button" onclick="openMemoryManager()">
                    <i class="fas fa-memory"></i>
                </a>
            </div>
        </header>

        <div class="main-grid">
            <div class="chat-section">
                <div class="chat-header">
                    <div class="chat-title">
                        <i class="fas fa-comments"></i>
                        Interface de Chat Cognitif
                    </div>
                    <div class="chat-controls">
                        <button class="chat-btn" onclick="clearChat()">
                            <i class="fas fa-trash"></i> Effacer
                        </button>
                        <button class="chat-btn" onclick="exportChat()">
                            <i class="fas fa-download"></i> Exporter
                        </button>
                    </div>
                </div>

                <div class="messages-container" id="messages">
                    <div class="message ai-message">
                        <strong>LOUNA AI:</strong> Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd'hui ?
                        <div class="message-time">Maintenant</div>
                    </div>
                </div>

                <div class="input-area">
                    <input type="text" class="message-input" id="messageInput" placeholder="Tapez votre message ici..." onkeypress="handleKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> Envoyer
                    </button>
                </div>
            </div>

            <div class="sidebar">
                <div class="metrics-panel">
                    <div class="metrics-title">
                        <i class="fas fa-chart-line"></i>
                        Métriques Système
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Neurones Actifs</span>
                        <span class="metric-value" id="neuronCount">1,247</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Synapses</span>
                        <span class="metric-value" id="synapseCount">3,891</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">QI Agent</span>
                        <span class="metric-value" id="agentIQ">185</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">QI Mémoire</span>
                        <span class="metric-value" id="memoryIQ">142</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">QI Combiné</span>
                        <span class="metric-value" id="combinedIQ">327</span>
                    </div>
                </div>

                <div class="brain-visual">
                    <div class="metrics-title">
                        <i class="fas fa-brain"></i>
                        Cerveau 3D
                    </div>
                    <div class="brain-3d"></div>
                    <div style="font-size: 0.9em; opacity: 0.8;">
                        Activité neuronale en temps réel
                    </div>
                </div>

                <div class="temperature-display">
                    <div class="metrics-title">
                        <i class="fas fa-thermometer-half"></i>
                        Température Système
                    </div>
                    <div style="font-size: 1.5em; font-weight: bold; margin: 10px 0;" id="tempValue">
                        42.3°C
                    </div>
                    <div class="temp-gauge">
                        <div class="temp-fill" id="tempFill"></div>
                    </div>
                    <div style="font-size: 0.8em; opacity: 0.7;">
                        Optimal: 35-50°C
                    </div>
                </div>
            </div>
        </div>

        <!-- Sections supplémentaires intégrées -->
        <div class="additional-sections" style="margin-top: 20px;">
            <!-- Code Section -->
            <div id="code" class="content-section" style="display: none;">
                <div class="card">
                    <h3><i class="fas fa-code"></i> Générateur de Code</h3>
                    <div style="margin-bottom: 20px;">
                        <input type="text" id="code-prompt" placeholder="Décrivez le code à générer..." style="width: 70%; padding: 10px; border-radius: 5px; border: 1px solid rgba(138, 43, 226, 0.5); background: rgba(26, 26, 46, 0.8); color: #fff;">
                        <select id="code-language" style="margin-left: 10px; padding: 10px; border-radius: 5px; border: 1px solid rgba(138, 43, 226, 0.5); background: rgba(26, 26, 46, 0.8); color: #fff;">
                            <option value="javascript">JavaScript</option>
                            <option value="python">Python</option>
                            <option value="html">HTML</option>
                        </select>
                        <button onclick="generateCode()" style="margin-left: 10px; padding: 10px 15px; background: linear-gradient(45deg, #8a2be2, #4169e1); border: none; border-radius: 5px; color: #fff; cursor: pointer;">
                            <i class="fas fa-magic"></i> Générer
                        </button>
                    </div>
                    <div id="generated-code" style="background: rgba(0, 0, 0, 0.5); padding: 15px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; min-height: 200px;">
                        // Le code généré apparaîtra ici...
                    </div>
                </div>
            </div>

            <!-- Multimedia Section -->
            <div id="multimedia" class="content-section" style="display: none;">
                <div class="card">
                    <h3><i class="fas fa-images"></i> Génération Multimédia</h3>
                    <p>Fonctionnalités multimédia en développement...</p>
                </div>
            </div>

            <!-- Protection Section -->
            <div id="protection" class="content-section" style="display: none;">
                <div class="card">
                    <h3><i class="fas fa-shield-alt"></i> Protection du Noyau</h3>
                    <p>Système de protection actif. Toutes les fonctions critiques sont sécurisées.</p>
                </div>
            </div>

            <!-- Brain Activity Section -->
            <div id="brain" class="content-section" style="display: none;">
                <div class="card">
                    <h3><i class="fas fa-brain"></i> Activité Cérébrale</h3>
                    <div class="neural-animation" style="height: 300px;">
                        <!-- Animation cérébrale avancée -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let neuronCount = 1247;
        let synapseCount = 3891;
        let agentIQ = 185;
        let memoryIQ = 142;
        let combinedIQ = 327;
        let currentTemp = 42.3;
        let kyberActive = true;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateMetrics();
            updateTemperature();
            updateKyberStatus();
            startNeuralActivity();
        });

        // Gestion du chat
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (message === '') return;

            addMessage(message, 'user');
            input.value = '';

            // Simulation de réponse IA
            setTimeout(() => {
                const response = generateAIResponse(message);
                addMessage(response, 'ai');
                updateNeuralActivity();
            }, 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const time = new Date().toLocaleTimeString();
            const senderName = sender === 'user' ? 'Vous' : 'LOUNA AI';

            messageDiv.innerHTML = `
                <strong>${senderName}:</strong> ${text}
                <div class="message-time">${time}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function generateAIResponse(message) {
            const responses = [
                "Je comprends votre question. Laissez-moi analyser cela avec ma mémoire thermique...",
                "Intéressant ! Mes neurones traitent cette information à " + currentTemp + "°C.",
                "D'après mes calculs avec un QI combiné de " + combinedIQ + ", voici ma réponse...",
                "Ma mémoire thermique contient " + neuronCount + " neurones actifs pour traiter votre demande.",
                "Excellent ! Mes accélérateurs Kyber sont " + (kyberActive ? "actifs" : "inactifs") + " pour optimiser ma réponse."
            ];

            return responses[Math.floor(Math.random() * responses.length)];
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Gestion des métriques
        function updateMetrics() {
            document.getElementById('neuronCount').textContent = neuronCount.toLocaleString();
            document.getElementById('synapseCount').textContent = synapseCount.toLocaleString();
            document.getElementById('agentIQ').textContent = agentIQ;
            document.getElementById('memoryIQ').textContent = memoryIQ;
            document.getElementById('combinedIQ').textContent = combinedIQ;
        }

        function updateNeuralActivity() {
            // Simulation d'activité neuronale
            neuronCount += Math.floor(Math.random() * 10) - 5;
            synapseCount += Math.floor(Math.random() * 20) - 10;

            // Mise à jour des QI
            agentIQ += Math.floor(Math.random() * 3) - 1;
            memoryIQ += Math.floor(Math.random() * 2) - 1;
            combinedIQ = agentIQ + memoryIQ;

            updateMetrics();
        }

        // Gestion de la température
        function updateTemperature() {
            currentTemp += (Math.random() - 0.5) * 2;
            currentTemp = Math.max(30, Math.min(60, currentTemp));

            document.getElementById('tempValue').textContent = currentTemp.toFixed(1) + '°C';

            const tempPercentage = ((currentTemp - 30) / 30) * 100;
            document.getElementById('tempFill').style.width = tempPercentage + '%';
        }

        // Gestion Kyber
        function updateKyberStatus() {
            const indicator = document.getElementById('kyber-indicator');
            if (kyberActive) {
                indicator.classList.remove('disabled');
            } else {
                indicator.classList.add('disabled');
            }
        }

        function toggleKyberBoost() {
            kyberActive = !kyberActive;
            updateKyberStatus();

            if (kyberActive) {
                addMessage("Accélérateurs Kyber activés ! Performance optimisée.", 'ai');
                // Boost des métriques
                neuronCount += 50;
                synapseCount += 100;
                agentIQ += 5;
                updateMetrics();
            } else {
                addMessage("Accélérateurs Kyber désactivés. Mode économie d'énergie.", 'ai');
            }
        }

        // Fonctions utilitaires
        function clearChat() {
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="message ai-message">
                    <strong>LOUNA AI:</strong> Chat effacé. Comment puis-je vous aider ?
                    <div class="message-time">Maintenant</div>
                </div>
            `;
        }

        function exportChat() {
            addMessage("Fonctionnalité d'export en cours de développement...", 'ai');
        }

        function openSettings() {
            addMessage("Ouverture des paramètres système...", 'ai');
        }

        function openMemoryManager() {
            addMessage("Accès au gestionnaire de mémoire thermique...", 'ai');
        }

        // Animation neuronale
        function startNeuralActivity() {
            setInterval(() => {
                updateTemperature();

                // Activité neuronale aléatoire
                if (Math.random() < 0.3) {
                    updateNeuralActivity();
                }
            }, 2000);
        }
    </script>
</body>
</html>






                <h3><i class="fas fa-brain"></i> Activité neuronale avancée</h3>
                <div class="grid-2">
                    <div class="card">
                        <h3><i class="fas fa-network-wired"></i> Réseau neuronal</h3>
                        <div class="neural-visualization"></div>
                        <div class="flex justify-between items-center">
                            <div class="badge primary"><i class="fas fa-circle-nodes"></i> 30 nœuds</div>
                            <div class="badge info"><i class="fas fa-bolt"></i> Activité: Élevée</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-wave-square"></i> Ondes cérébrales</h3>
                        <div class="brain-activity-chart">
                            <div class="brain-wave">
                                <div class="wave-line"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="badge success"><i class="fas fa-check-circle"></i> Synchronisé</div>
                            <div class="badge warning"><i class="fas fa-chart-line"></i> Fréquence: 12.4 Hz</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-project-diagram"></i> Visualisation 3D</h3>
                    <div class="neural-container">
                        <div class="neural-background"></div>
                        <div class="neural-smoke"></div>
                        <div class="neural-fluid"></div>
                        <div class="neural-activity neural-activity-working" id="neural-activity"></div>

                        <!-- Effets électriques -->
                        <div class="neural-spark" style="top: 30%; left: 20%;"></div>
                        <div class="neural-spark" style="top: 60%; left: 70%;"></div>
                        <div class="neural-spark" style="top: 40%; left: 50%;"></div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="badge info"><i class="fas fa-sync-alt"></i> Mise à jour en temps réel</div>
                        <div class="badge accent"><i class="fas fa-tachometer-alt"></i> Performance: Optimale</div>
                    </div>
                </div>
            </section>

            <!-- Section Statistiques -->
            <section id="stats">
                <h2><i class="fas fa-chart-line"></i> Statistiques et Analyse</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Distribution des niveaux</h3>
                        <div class="chart-container">
                            <canvas id="levels-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>Température par niveau</h3>
                        <div class="chart-container">
                            <canvas id="temperature-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card card">
                        <h3><i class="fas fa-bolt"></i> Accélérateur Kyber</h3>
                        <div class="kyber-stats-container">
                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                                <div class="kyber-stat-title">Facteur d'accélération</div>
                                <div class="kyber-stat-value accent" id="kyber-acceleration">-</div>
                                <div class="kyber-stat-subtitle">Multiplicateur de performance</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="kyber-stat-title">Opérations/seconde</div>
                                <div class="kyber-stat-value info" id="kyber-ops">-</div>
                                <div class="kyber-stat-subtitle">Traitement en temps réel</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-temperature-high"></i>
                                </div>
                                <div class="kyber-stat-title">Température</div>
                                <div class="kyber-stat-value success" id="kyber-temperature">-</div>
                                <div class="kyber-stat-subtitle">Stabilité thermique</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="kyber-stat-title">Utilisation</div>
                                <div class="kyber-stat-value warning" id="kyber-utilization">-</div>
                                <div class="kyber-stat-subtitle">Capacité utilisée</div>
                            </div>
                        </div>

                        <div class="gauge-container">
                            <div class="gauge" data-type="acceleration" data-value="50">
                                <div class="gauge-background"></div>
                                <div class="gauge-fill"></div>
                                <div class="gauge-center">
                                    <div class="gauge-value">1.5</div>
                                    <div class="gauge-label">Facteur</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques Kyber -->
                <div class="stats-container">
                    <h3>Statistiques de performance</h3>
                    <div class="stats-content">
                        <div class="stats-metric">
                            <span class="metric-label">Facteur d'accélération</span>
                            <span class="metric-value" id="kyber-factor">1.0</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Température</span>
                            <span class="metric-value" id="kyber-temp">25.0°C</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Score de performance</span>
                            <span class="metric-value" id="kyber-score">80/100</span>
                        </div>
                    </div>

                    <!-- Graphiques de température et distribution -->
                    <h3>Température par niveau</h3>
                    <div class="temperature-chart-container">
                        <div class="chart-particles"></div>
                        <canvas id="temperature-level-chart"></canvas>
                        <div class="temperature-gradient"></div>
                    </div>

                    <h3>Distribution d'activité</h3>
                    <div class="chart-container">
                        <canvas id="activity-distribution-chart"></canvas>
                    </div>

                    <h3>Évolution de la mémoire</h3>
                    <div class="chart-container">
                        <canvas id="memory-evolution-chart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Section Multimédia -->
            <section id="multimedia">
                <h2><i class="fas fa-images"></i> Génération Multimédia</h2>

                <div class="multimedia-container">
                    <div class="multimedia-controls">
                        <div class="form-group">
                            <label for="multimedia-type">Type de contenu</label>
                            <select id="multimedia-type">
                                <option value="chart">Graphique</option>
                                <option value="image">Image avec texte</option>
                                <option value="art">Art abstrait</option>
                            </select>
                        </div>

                        <div id="chart-options" class="media-options">
                            <div class="form-group">
                                <label for="chart-type">Type de graphique</label>
                                <select id="chart-type">
                                    <option value="bar">Barres</option>
                                    <option value="line">Lignes</option>
                                    <option value="pie">Camembert</option>
                                    <option value="radar">Radar</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="chart-data">Données (séparées par des virgules)</label>
                                <input type="text" id="chart-data" placeholder="10, 20, 30, 40, 50">
                            </div>

                            <div class="form-group">
                                <label for="chart-labels">Étiquettes (séparées par des virgules)</label>
                                <input type="text" id="chart-labels" placeholder="A, B, C, D, E">
                            </div>
                        </div>

                        <div id="image-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="image-text">Texte à afficher</label>
                                <input type="text" id="image-text" placeholder="Bonjour, Monde !">
                            </div>

                            <div class="form-group">
                                <label for="image-bg-color">Couleur d'arrière-plan</label>
                                <input type="color" id="image-bg-color" value="#f5f5f5">
                            </div>

                            <div class="form-group">
                                <label for="image-text-color">Couleur du texte</label>
                                <input type="color" id="image-text-color" value="#333333">
                            </div>
                        </div>

                        <div id="art-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="art-complexity">Complexité</label>
                                <input type="range" id="art-complexity" min="1" max="10" value="5">
                            </div>

                            <div class="form-group">
                                <label for="art-colors">Palette de couleurs</label>
                                <select id="art-colors">
                                    <option value="vibrant">Vibrante</option>
                                    <option value="pastel">Pastel</option>
                                    <option value="monochrome">Monochrome</option>
                                    <option value="autumn">Automne</option>
                                    <option value="ocean">Océan</option>
                                </select>
                            </div>
                        </div>

                        <button id="generate-media-btn" class="btn primary"><i class="fas fa-magic"></i> Générer</button>
                    </div>

                    <div class="multimedia-preview">
                        <h3>Aperçu</h3>
                        <div id="media-output"></div>
                    </div>
                </div>
            </section>

            <!-- Section Code -->
            <section id="code">
                <h2><i class="fas fa-code"></i> Assistant de Programmation</h2>

                <div class="code-container">
                    <div class="code-editor">
                        <div class="form-group">
                            <label for="code-language">Langage</label>
                            <select id="code-language">
                                <option value="python">Python</option>
                                <option value="javascript">JavaScript</option>
                                <option value="java">Java</option>
                                <option value="cpp">C++</option>
                                <option value="csharp">C#</option>
                                <option value="php">PHP</option>
                                <option value="ruby">Ruby</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                                <option value="swift">Swift</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="code-input">Code ou description</label>
                            <textarea id="code-input" class="code-textarea" placeholder="Entrez votre code à analyser ou décrivez ce que vous voulez générer..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="code-action">Action</label>
                            <select id="code-action">
                                <option value="explain">Expliquer</option>
                                <option value="generate">Générer</option>
                                <option value="optimize">Optimiser</option>
                                <option value="debug">Déboguer</option>
                                <option value="convert">Convertir</option>
                            </select>
                        </div>

                        <button id="process-code-btn" class="btn primary"><i class="fas fa-terminal"></i> Traiter</button>
                    </div>

                    <div class="code-output">
                        <h3>Résultat</h3>
                        <pre id="code-result" class="code-block">// Le résultat apparaîtra ici</pre>
                    </div>
                </div>
            </section>

            <!-- Section Systèmes Externes -->
            <section id="external">
                <h2><i class="fas fa-sitemap"></i> Systèmes Externes</h2>

                <div class="external-systems-container">
                    <div class="systems-info">
                        <h3>Systèmes connectés</h3>
                        <div id="connected-systems-list" class="systems-list">
                            <!-- Liste des systèmes connectés (remplie dynamiquement) -->
                        </div>

                        <div class="system-details" id="system-details">
                            <h3>Détails du système</h3>
                            <div id="system-details-content">
                                <p>Sélectionnez un système pour voir les détails.</p>
                            </div>
                        </div>
                    </div>

                    <div class="add-system-form">
                        <h3>Connecter un nouveau système</h3>
                        <div class="form-group">
                            <label for="system-type">Type de système</label>
                            <select id="system-type">
                                <option value="api">API / Service Web</option>
                                <option value="file">Système de fichiers</option>
                                <option value="database">Base de données</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="system-id">Identifiant unique</label>
                            <input type="text" id="system-id" placeholder="ex: github-repos">
                        </div>

                        <div class="form-group">
                            <label for="system-config">Configuration (JSON)</label>
                            <textarea id="system-config" placeholder='{"endpoint": "https://api.example.com"}'></textarea>
                        </div>

                        <button id="connect-system-btn" class="btn primary"><i class="fas fa-plug"></i> Connecter</button>
                    </div>

                    <div class="learning-status">
                        <h3>Statistiques d'apprentissage</h3>
                        <div class="learning-metrics">
                            <div class="metric">
                                <span class="metric-label">Systèmes connectés</span>
                                <span class="metric-value" id="connected-systems-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Données traitées</span>
                                <span class="metric-value" id="processed-data-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Taux d'apprentissage</span>
                                <span class="metric-value" id="learning-rate">0%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Dernier apprentissage</span>
                                <span class="metric-value" id="last-learning-time">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Activité Cérébrale -->
            <section id="brain-activity">
                <h2><i class="fas fa-brain"></i> Activité Cérébrale</h2>

                <div class="brain-container">
                    <div class="brain-visualization" id="brain-visualization">
                        <!-- Conteneur pour l'animation neuronale avancée -->
                        <div id="neural-container-large" class="neural-container-large"></div>
                    </div>

                    <div class="brain-controls">
                        <h3>Contrôle de l'activité</h3>

                        <div class="form-group">
                            <label for="activity-level">Niveau d'activité</label>
                            <input type="range" id="activity-level" min="0.1" max="1" step="0.1" value="0.5">
                            <span id="activity-level-value">50%</span>
                        </div>

                        <div class="form-group">
                            <label for="mode-selector">Mode de fonctionnement</label>
                            <select id="mode-selector">
                                <option value="normal">Normal</option>
                                <option value="veille">Veille</option>
                                <option value="apprentissage">Apprentissage intensif</option>
                                <option value="economie">Économie d'énergie</option>
                            </select>
                        </div>

                        <div class="brain-metrics">
                            <h3>Métriques en temps réel</h3>
                            <div class="metrics-container">
                                <div class="metric">
                                    <span class="metric-label">Utilisation mémoire</span>
                                    <span class="metric-value" id="memory-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Charge CPU</span>
                                    <span class="metric-value" id="cpu-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Connexions synaptiques</span>
                                    <span class="metric-value" id="synaptic-connections">0</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">État émotionnel</span>
                                    <span class="metric-value" id="emotional-state">Neutre</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>Agent à Mémoire Thermique &copy; 2025 | <i class="fas fa-thermometer-half"></i> Système de mémoire thermique avec accélérateur Kyber</p>
            <div class="system-stats">
                <span id="kyber-temp">Kyber: <span id="kyber-temperature">45.2°C</span></span> |
                <span id="memory-stat">Mémoire: <span id="memory-utilization">42%</span></span> |
                <span id="acceleration-factor">Accélération: <span id="kyber-acceleration">1.8x</span></span>
            </div>
        </footer>
    </div>

    <!-- Animation neuronale en arrière-plan pour tout le site -->
    <div id="neural-container" class="neural-background"></div>

    <!-- Chart.js pour les graphiques statistiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Contrôleur d'animations (chargé en premier pour désactiver les animations bloquantes) -->
    <script src="/js/animation-controller.js"></script>

    <!-- Gestionnaire de fallback API (chargé avant les scripts principaux pour éviter le mode limité) -->
    <script src="/js/api-fallback-manager.js"></script>

    <!-- Scripts principaux -->
    <script src="/js/simple-app.js"></script>
    <script src="/js/memory-manager.js"></script>
    <script src="/js/kyber-module.js"></script>
    <script src="/js/voice-interface.js"></script>
    <script src="/js/human-language-trainer.js"></script>
    <script src="/js/keyboard-shortcuts.js"></script>
    <script src="/js/code-explorer.js"></script>
    <script src="/js/file-loader.js"></script>
    <script src="/js/vscode-integration.js"></script>
    <script src="/js/vscode-integration-file-operations.js"></script>
    <script src="/js/auto-paradigm-initializer.js"></script>
    <!-- Scripts d'animation (chargés après le contrôleur pour respecter les paramètres) -->
    <script src="/js/neural-animation.js"></script>
    <script src="/js/neural-animation-enhanced.js"></script>
    <script src="/js/particle-animation.js"></script>
    <script src="/js/animated-stats-charts.js"></script>
    <script src="/js/sensory-memory-manager.js"></script>
    <script src="/js/sidebar-manager.js"></script>
    <script src="/js/enhanced-visualizations.js"></script>
    <script src="/js/neural-activity-fix.js"></script>
    <script src="/js/brain-activity-injector.js"></script>
    <script src="/js/simple-app.js"></script>
            </div>
        </div>
    </div>
</body>
</html>
