<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thermal Agent - Sécurité</title>
    <link rel="stylesheet" href="css/secure-cognition.css">
    <style>
        .lock-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: radial-gradient(circle, #1a1a2e 0%, #16213e 100%);
            color: #e6e6e6;
            padding: 20px;
            text-align: center;
        }

        .lock-container {
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0, 180, 250, 0.3);
            max-width: 500px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 180, 250, 0.2);
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo img {
            width: 120px;
            height: auto;
            filter: drop-shadow(0 0 10px rgba(0, 180, 250, 0.7));
        }

        h1 {
            margin-bottom: 30px;
            color: #00b4fa;
            font-size: 28px;
        }

        .code-input {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .code-input input {
            width: 50px;
            height: 60px;
            margin: 0 5px;
            font-size: 24px;
            text-align: center;
            border: 2px solid #3a506b;
            border-radius: 8px;
            background-color: rgba(28, 30, 44, 0.8);
            color: #00b4fa;
            transition: all 0.3s ease;
        }

        .code-input input:focus {
            border-color: #00b4fa;
            box-shadow: 0 0 15px rgba(0, 180, 250, 0.5);
            outline: none;
        }

        .buttons {
            margin-top: 30px;
        }

        .unlock-btn {
            background: linear-gradient(135deg, #0582ca 0%, #006494 100%);
            color: white;
            border: none;
            padding: 12px 36px;
            font-size: 18px;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 100, 148, 0.4);
        }

        .unlock-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0, 100, 148, 0.6);
        }

        .unlock-btn:active {
            transform: translateY(1px);
        }

        .error-message {
            color: #ff6b6b;
            margin-top: 20px;
            font-size: 16px;
            min-height: 20px;
        }

        .security-info {
            margin-top: 30px;
            font-size: 14px;
            color: #8d99ae;
            max-width: 400px;
            line-height: 1.6;
        }

        .neuron-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.2;
            pointer-events: none;
        }

        .security-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            font-size: 14px;
            color: #8d99ae;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #00b4fa;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 180, 250, 0.7);
                transform: scale(0.95);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 180, 250, 0);
                transform: scale(1);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 180, 250, 0);
                transform: scale(0.95);
            }
        }

        .biometric-auth {
            display: flex;
            align-items: center;
            margin-top: 20px;
            color: #8d99ae;
            cursor: pointer;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .biometric-auth:hover {
            color: #00b4fa;
        }

        .biometric-auth svg {
            margin-right: 8px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="lock-screen">
        <canvas class="neuron-background" id="neuronCanvas"></canvas>
        <div class="lock-container">
            <div class="logo">
                <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="45" stroke="#00b4fa" stroke-width="2" />
                    <circle cx="50" cy="50" r="30" stroke="#00b4fa" stroke-width="2" stroke-dasharray="5 3" />
                    <circle cx="50" cy="50" r="15" fill="rgba(0, 180, 250, 0.2)" stroke="#00b4fa" stroke-width="2" />
                    <path d="M50 5 L50 20" stroke="#00b4fa" stroke-width="2" />
                    <path d="M50 80 L50 95" stroke="#00b4fa" stroke-width="2" />
                    <path d="M5 50 L20 50" stroke="#00b4fa" stroke-width="2" />
                    <path d="M80 50 L95 50" stroke="#00b4fa" stroke-width="2" />
                </svg>
            </div>
            <h1>Sécurité Cognitive Thermique</h1>
            <div class="code-input">
                <input type="password" maxlength="1" class="code-digit" data-index="0" autofocus>
                <input type="password" maxlength="1" class="code-digit" data-index="1">
                <input type="password" maxlength="1" class="code-digit" data-index="2">
                <input type="password" maxlength="1" class="code-digit" data-index="3">
                <input type="password" maxlength="1" class="code-digit" data-index="4">
                <input type="password" maxlength="1" class="code-digit" data-index="5">
            </div>
            <div class="error-message" id="errorMessage"></div>
            <div class="buttons">
                <button class="unlock-btn" id="unlockBtn">Déverrouiller</button>
            </div>
            <div class="security-status">
                <div class="status-dot"></div>
                <span>Mode strictement local activé</span>
            </div>
            <div class="biometric-auth" id="biometricAuth">
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M6.5 2C4.01 2 2 4.01 2 6.5S4.01 11 6.5 11 11 8.99 11 6.5 8.99 2 6.5 2zm0 2C7.88 4 9 5.12 9 6.5S7.88 9 6.5 9 4 7.88 4 6.5 5.12 4 6.5 4zM17.5 2C15.01 2 13 4.01 13 6.5s2.01 4.5 4.5 4.5S22 8.99 22 6.5 19.99 2 17.5 2zm0 2C18.88 4 20 5.12 20 6.5S18.88 9 17.5 9 15 7.88 15 6.5 16.12 4 17.5 4zM12 11c-4.96 0-9 4.04-9 9v2h18v-2c0-4.96-4.04-9-9-9zm0 2c3.87 0 7 3.13 7 7H5c0-3.87 3.13-7 7-7z"/>
                </svg>
                <span>Authentification biométrique</span>
            </div>
            <p class="security-info">
                Ce système fonctionne en mode strictement local. Toutes les données 
                et processus sont confinés à votre machine. Aucune information n'est 
                partagée avec des serveurs externes.
            </p>
        </div>
    </div>

    <script>
        // Configuration - Le code est maintenant vérifié par le serveur
        let appLocked = true; // Par défaut, on suppose que l'application est verrouillée

        // Référence aux éléments
        const codeInputs = document.querySelectorAll('.code-digit');
        const unlockBtn = document.getElementById('unlockBtn');
        const errorMessage = document.getElementById('errorMessage');
        const biometricAuth = document.getElementById('biometricAuth');

        // Navigation automatique entre les champs
        codeInputs.forEach(input => {
            input.addEventListener('keyup', (e) => {
                const index = parseInt(input.getAttribute('data-index'));
                
                // Si une touche numérique est pressée
                if (/^[0-9]$/.test(e.key)) {
                    // Passer au champ suivant si ce n'est pas le dernier
                    if (index < codeInputs.length - 1) {
                        codeInputs[index + 1].focus();
                    }
                }
                // Si la touche Backspace est pressée
                else if (e.key === 'Backspace') {
                    // Si le champ est vide et ce n'est pas le premier, revenir au précédent
                    if (input.value === '' && index > 0) {
                        codeInputs[index - 1].focus();
                    }
                }

                // Activer automatiquement le bouton si tous les champs sont remplis
                checkAllFieldsFilled();
            });
        });

        // Vérifier si tous les champs sont remplis
        function checkAllFieldsFilled() {
            const allFilled = Array.from(codeInputs).every(input => input.value !== '');
            if (allFilled) {
                unlockBtn.classList.add('ready');
            } else {
                unlockBtn.classList.remove('ready');
            }
        }

        // Gérer l'action de déverrouillage avec l'API serveur
        unlockBtn.addEventListener('click', () => {
            verifyCode();
        });
        
        // Fonction pour vérifier le code avec l'API serveur
        async function verifyCode() {
            const enteredCode = Array.from(codeInputs).map(input => input.value).join('');
            
            try {
                // Appel à l'API de vérification
                const response = await fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code: enteredCode })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Code correct - déverrouiller
                    errorMessage.textContent = '';
                    
                    // Animation de succès
                    document.querySelector('.lock-container').style.borderColor = '#4caf50';
                    document.querySelector('.lock-container').style.boxShadow = '0 0 30px rgba(76, 175, 80, 0.5)';
                    
                    // Redirection vers la page d'accueil
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    handleInvalidCode();
                }
            } catch (error) {
                errorMessage.textContent = 'Erreur de connexion au serveur.';
                console.error('Erreur lors de la vérification du code:', error);
            }
        }
        
        // Fonction pour gérer un code invalide
        function handleInvalidCode() {
            // Code incorrect
            errorMessage.textContent = 'Code de sécurité incorrect. Veuillez réessayer.';
            
            // Animation d'erreur
            document.querySelector('.lock-container').style.borderColor = '#ff6b6b';
            document.querySelector('.lock-container').style.boxShadow = '0 0 30px rgba(255, 107, 107, 0.5)';
            
            // Réinitialiser les champs
            codeInputs.forEach(input => {
                input.value = '';
            });
            codeInputs[0].focus();
            
            // Rétablir l'apparence normale après un court délai
            setTimeout(() => {
                document.querySelector('.lock-container').style.borderColor = 'rgba(0, 180, 250, 0.2)';
                document.querySelector('.lock-container').style.boxShadow = '0 0 30px rgba(0, 180, 250, 0.3)';
            }, 2000);
        }
        });

        // Gérer la touche Entrée pour soumettre le code
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                unlockBtn.click();
            }
        });

        // Authentification biométrique (simulation)
        biometricAuth.addEventListener('click', () => {
            biometricAuth.style.color = '#4caf50';
            biometricAuth.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor"/>
                </svg>
                <span>Authentification réussie</span>
            `;
            
            // Nous utilisons une méthode spéciale pour l'authentification biométrique
            // Dans un système réel, on utiliserait WebAuthn ou une API biométrique
            setTimeout(async () => {
                try {
                    const response = await fetch('/api/auth/verify', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ code: 'biometric_auth' })
                    });
                    
                    if (response.ok) {
                        window.location.href = '/';
                    }
                } catch (error) {
                    console.error('Erreur d\'authentification biométrique:', error);
                    biometricAuth.style.color = '#ff6b6b';
                    biometricAuth.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
                        </svg>
                        <span>Échec de l'authentification</span>
                    `;
                }
            }, 1500);
        });

        // Animation du canvas en arrière-plan
        const canvas = document.getElementById('neuronCanvas');
        const ctx = canvas.getContext('2d');
        
        // Ajuster la taille du canvas
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        
        // Animation des neurones
        class Neuron {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 3 + 1;
                this.speedX = (Math.random() - 0.5) * 0.5;
                this.speedY = (Math.random() - 0.5) * 0.5;
                this.connections = [];
                this.connectionDistance = 150;
            }
            
            update() {
                this.x += this.speedX;
                this.y += this.speedY;
                
                // Rebondissement aux bords
                if (this.x < 0 || this.x > canvas.width) this.speedX *= -1;
                if (this.y < 0 || this.y > canvas.height) this.speedY *= -1;
            }
            
            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(0, 180, 250, 0.5)';
                ctx.fill();
            }
            
            connect(neurons) {
                this.connections = [];
                neurons.forEach(neuron => {
                    if (neuron !== this) {
                        const distance = Math.hypot(this.x - neuron.x, this.y - neuron.y);
                        if (distance < this.connectionDistance) {
                            this.connections.push(neuron);
                        }
                    }
                });
            }
            
            drawConnections() {
                ctx.strokeStyle = 'rgba(0, 180, 250, 0.2)';
                this.connections.forEach(neuron => {
                    ctx.beginPath();
                    ctx.moveTo(this.x, this.y);
                    ctx.lineTo(neuron.x, neuron.y);
                    ctx.stroke();
                });
            }
        }
        
        // Créer les neurones
        const neurons = [];
        const neuronCount = 100;
        
        for (let i = 0; i < neuronCount; i++) {
            neurons.push(new Neuron());
        }
        
        // Animation
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            neurons.forEach(neuron => {
                neuron.update();
                neuron.connect(neurons);
                neuron.drawConnections();
                neuron.draw();
            });
            
            requestAnimationFrame(animate);
        }
        
        animate();

        // Vérifier si l'application est verrouillée et si l'authentification est nécessaire
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Vérifier l'état d'authentification auprès du serveur
                const response = await fetch('/api/auth/status');
                const data = await response.json();
                
                // Si l'application n'est pas verrouillée ou si l'utilisateur est déjà authentifié
                if (!data.locked || data.authenticated) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('Erreur lors de la vérification du statut d\'authentification:', error);
            }
        });
    </script>
</body>
</html>
