<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moniteur de Sécurité Cognitive</title>
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/secure-cognition.css">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Socket.IO compatible avec Flask-SocketIO -->
    <script src="https://cdn.socket.io/4.6.1/socket.io.min.js"></script>
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- D3.js pour les visualisations avancées -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <h1>Moniteur de Sécurité Cognitive</h1>
            </div>
            <div id="security-status" class="status secure">Sécurisé</div>
        </header>

        <nav>
            <ul>
                <li><a href="#real-time" id="nav-real-time" class="active"><i class="fas fa-stream"></i> Temps Réel</a></li>
                <li><a href="#violations" id="nav-violations"><i class="fas fa-exclamation-triangle"></i> Violations</a></li>
                <li><a href="#analytics" id="nav-analytics"><i class="fas fa-chart-line"></i> Analytique</a></li>
                <li><a href="#security-map" id="nav-security-map"><i class="fas fa-map"></i> Cartographie</a></li>
                <li><a href="#settings" id="nav-settings"><i class="fas fa-cogs"></i> Paramètres</a></li>
            </ul>
        </nav>

        <main>
            <!-- Section Temps Réel -->
            <section id="real-time" class="active">
                <h2><i class="fas fa-stream"></i> Activité Cognitive en Temps Réel</h2>
                
                <div class="control-panel">
                    <div class="filters">
                        <div class="filter-group">
                            <label for="category-filter">Catégorie:</label>
                            <select id="category-filter">
                                <option value="all">Toutes</option>
                                <option value="memory">Mémoire</option>
                                <option value="reasoning">Raisonnement</option>
                                <option value="planning">Planification</option>
                                <option value="security">Sécurité</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="importance-filter">Importance minimale:</label>
                            <input type="range" id="importance-filter" min="0" max="1" step="0.1" value="0">
                            <span id="importance-value">0.0</span>
                        </div>
                        
                        <div class="filter-group">
                            <label for="security-filter">Niveau de sécurité:</label>
                            <select id="security-filter">
                                <option value="all">Tous</option>
                                <option value="secure">Sécurisé</option>
                                <option value="warning">Avertissement</option>
                                <option value="violation">Violation</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="view-controls">
                        <button id="pause-btn" class="btn"><i class="fas fa-pause"></i> Pause</button>
                        <button id="clear-btn" class="btn"><i class="fas fa-trash"></i> Effacer</button>
                        <div class="view-selector">
                            <button class="view-btn active" data-view="list"><i class="fas fa-list"></i></button>
                            <button class="view-btn" data-view="graph"><i class="fas fa-project-diagram"></i></button>
                            <button class="view-btn" data-view="timeline"><i class="fas fa-stream"></i></button>
                        </div>
                    </div>
                </div>
                
                <div class="cognitive-views">
                    <!-- Vue Liste -->
                    <div id="list-view" class="cognitive-view active">
                        <div id="thought-list" class="thoughts-container"></div>
                    </div>
                    
                    <!-- Vue Graphe -->
                    <div id="graph-view" class="cognitive-view">
                        <div id="thought-graph" class="graph-container"></div>
                    </div>
                    
                    <!-- Vue Timeline -->
                    <div id="timeline-view" class="cognitive-view">
                        <div id="thought-timeline" class="timeline-container"></div>
                    </div>
                </div>
                
                <!-- Détails d'une pensée -->
                <div id="thought-details" class="details-panel" style="display: none;">
                    <h3>Détails de la pensée</h3>
                    <div class="detail-content">
                        <p><strong>Source:</strong> <span id="detail-source"></span></p>
                        <p><strong>Catégorie:</strong> <span id="detail-category"></span></p>
                        <p><strong>Importance:</strong> <span id="detail-importance"></span></p>
                        <p><strong>Horodatage:</strong> <span id="detail-timestamp"></span></p>
                        <p><strong>Statut de sécurité:</strong> <span id="detail-security"></span></p>
                        <div class="detail-args">
                            <p><strong>Arguments:</strong></p>
                            <pre id="detail-args"></pre>
                        </div>
                        <div class="detail-content-box">
                            <p><strong>Contenu:</strong></p>
                            <pre id="detail-content"></pre>
                        </div>
                    </div>
                    <button id="close-details" class="btn"><i class="fas fa-times"></i> Fermer</button>
                </div>
            </section>

            <!-- Section Violations -->
            <section id="violations">
                <h2><i class="fas fa-exclamation-triangle"></i> Violations de Sécurité</h2>
                
                <div class="violations-summary">
                    <div class="violation-stat">
                        <span class="stat-value" id="total-violations">0</span>
                        <span class="stat-label">Violations Totales</span>
                    </div>
                    <div class="violation-stat critical">
                        <span class="stat-value" id="critical-violations">0</span>
                        <span class="stat-label">Critiques</span>
                    </div>
                    <div class="violation-stat warning">
                        <span class="stat-value" id="warning-violations">0</span>
                        <span class="stat-label">Avertissements</span>
                    </div>
                    <div class="violation-stat info">
                        <span class="stat-value" id="resolved-violations">0</span>
                        <span class="stat-label">Résolues</span>
                    </div>
                </div>
                
                <div class="violations-filter">
                    <div class="filter-group">
                        <label for="violation-type">Type:</label>
                        <select id="violation-type">
                            <option value="all">Tous</option>
                            <option value="path_access">Accès Fichier</option>
                            <option value="forbidden_action">Action Interdite</option>
                            <option value="behavior">Comportement</option>
                            <option value="resource">Ressource</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="violation-period">Période:</label>
                        <select id="violation-period">
                            <option value="all">Tout l'historique</option>
                            <option value="day">Aujourd'hui</option>
                            <option value="week">Cette semaine</option>
                            <option value="month">Ce mois</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="violation-status">Statut:</label>
                        <select id="violation-status">
                            <option value="all">Tous</option>
                            <option value="active">Actif</option>
                            <option value="resolved">Résolu</option>
                        </select>
                    </div>
                </div>
                
                <div class="violations-list-container">
                    <table class="violations-table">
                        <thead>
                            <tr>
                                <th>Horodatage</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Sévérité</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="violations-list">
                            <!-- Les violations seront ajoutées ici dynamiquement -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Détails d'une violation -->
                <div id="violation-details" class="details-panel" style="display: none;">
                    <h3>Détails de la violation</h3>
                    <div class="detail-content">
                        <p><strong>Type:</strong> <span id="v-detail-type"></span></p>
                        <p><strong>Horodatage:</strong> <span id="v-detail-timestamp"></span></p>
                        <p><strong>Sévérité:</strong> <span id="v-detail-severity"></span></p>
                        <p><strong>Statut:</strong> <span id="v-detail-status"></span></p>
                        <div class="detail-content-box">
                            <p><strong>Description:</strong></p>
                            <pre id="v-detail-description"></pre>
                        </div>
                        <div class="detail-content-box">
                            <p><strong>Contexte:</strong></p>
                            <pre id="v-detail-context"></pre>
                        </div>
                    </div>
                    <div class="detail-actions">
                        <button id="resolve-violation" class="btn"><i class="fas fa-check"></i> Marquer comme résolu</button>
                        <button id="ignore-violation" class="btn"><i class="fas fa-eye-slash"></i> Ignorer</button>
                        <button id="close-violation-details" class="btn"><i class="fas fa-times"></i> Fermer</button>
                    </div>
                </div>
            </section>

            <!-- Section Analytique -->
            <section id="analytics">
                <h2><i class="fas fa-chart-line"></i> Analytique de Sécurité</h2>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Tendances des Violations</h3>
                        <div class="chart-container">
                            <canvas id="violations-trend-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Distribution par Type</h3>
                        <div class="chart-container">
                            <canvas id="violation-types-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Activité Cognitive par Catégorie</h3>
                        <div class="chart-container">
                            <canvas id="cognitive-category-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Évaluation des Risques</h3>
                        <div class="risk-meter-container">
                            <div class="risk-meter">
                                <div class="risk-level" id="risk-level" style="width: 10%"></div>
                            </div>
                            <div class="risk-labels">
                                <span>Faible</span>
                                <span>Modéré</span>
                                <span>Élevé</span>
                                <span>Critique</span>
                            </div>
                        </div>
                        <div class="risk-stats">
                            <div class="risk-stat">
                                <span class="stat-label">Niveau de Risque Actuel:</span>
                                <span class="stat-value" id="current-risk-level">Faible</span>
                            </div>
                            <div class="risk-stat">
                                <span class="stat-label">Tendance:</span>
                                <span class="stat-value" id="risk-trend">Stable</span>
                            </div>
                            <div class="risk-stat">
                                <span class="stat-label">Facteurs Principaux:</span>
                                <span class="stat-value" id="risk-factors">-</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="analytics-insights">
                    <h3>Insights et Recommandations</h3>
                    <div id="security-insights" class="insights-container">
                        <!-- Les insights seront ajoutés ici dynamiquement -->
                        <div class="insight">
                            <i class="fas fa-info-circle"></i>
                            <span>Analyse des données en cours...</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Cartographie de Sécurité -->
            <section id="security-map">
                <h2><i class="fas fa-map"></i> Cartographie de Sécurité</h2>
                
                <div class="map-controls">
                    <div class="control-group">
                        <label for="map-view-type">Type de Vue:</label>
                        <select id="map-view-type">
                            <option value="filesystem">Système de Fichiers</option>
                            <option value="cognitive">Processus Cognitifs</option>
                            <option value="actions">Actions Système</option>
                        </select>
                    </div>
                    
                    <div class="zoom-controls">
                        <button id="zoom-in" class="btn"><i class="fas fa-search-plus"></i></button>
                        <button id="zoom-out" class="btn"><i class="fas fa-search-minus"></i></button>
                        <button id="zoom-reset" class="btn"><i class="fas fa-sync"></i></button>
                    </div>
                </div>
                
                <div id="security-map-container" class="map-container">
                    <!-- La carte sera générée ici dynamiquement -->
                </div>
                
                <div class="map-legend">
                    <h4>Légende</h4>
                    <div class="legend-item">
                        <span class="legend-color zone-allowed"></span>
                        <span class="legend-label">Zone autorisée</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color zone-restricted"></span>
                        <span class="legend-label">Zone restreinte</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color zone-forbidden"></span>
                        <span class="legend-label">Zone interdite</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-icon"><i class="fas fa-exclamation-triangle"></i></span>
                        <span class="legend-label">Violation</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-icon"><i class="fas fa-check-circle"></i></span>
                        <span class="legend-label">Accès autorisé</span>
                    </div>
                </div>
            </section>

            <!-- Section Paramètres -->
            <section id="settings">
                <h2><i class="fas fa-cogs"></i> Paramètres de Sécurité</h2>
                
                <div class="settings-container">
                    <div class="settings-section">
                        <h3>Paramètres Généraux</h3>
                        
                        <div class="setting-item">
                            <label for="auto-pause">Pause automatique en cas de violation:</label>
                            <label class="switch">
                                <input type="checkbox" id="auto-pause" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label for="notification-level">Niveau de notification:</label>
                            <select id="notification-level">
                                <option value="all">Toutes les activités</option>
                                <option value="warnings">Avertissements et violations</option>
                                <option value="violations">Violations uniquement</option>
                                <option value="critical">Violations critiques uniquement</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="log-retention">Conservation des logs:</label>
                            <select id="log-retention">
                                <option value="1">1 jour</option>
                                <option value="7">7 jours</option>
                                <option value="30" selected>30 jours</option>
                                <option value="90">90 jours</option>
                                <option value="365">1 an</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h3>Seuils de Sécurité</h3>
                        
                        <div class="setting-item slider-setting">
                            <label for="warning-threshold">Seuil d'avertissement:</label>
                            <input type="range" id="warning-threshold" min="1" max="50" value="10">
                            <span id="warning-threshold-value">10</span> violations/heure
                        </div>
                        
                        <div class="setting-item slider-setting">
                            <label for="critical-threshold">Seuil critique:</label>
                            <input type="range" id="critical-threshold" min="1" max="100" value="25">
                            <span id="critical-threshold-value">25</span> violations/heure
                        </div>
                        
                        <div class="setting-item slider-setting">
                            <label for="auto-block-threshold">Seuil de blocage automatique:</label>
                            <input type="range" id="auto-block-threshold" min="10" max="200" value="50">
                            <span id="auto-block-threshold-value">50</span> violations/heure
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h3>Actions Automatiques</h3>
                        
                        <div class="setting-item">
                            <label for="auto-resolve">Résolution automatique des avertissements:</label>
                            <label class="switch">
                                <input type="checkbox" id="auto-resolve">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label for="auto-block">Blocage automatique en cas de dépassement de seuil:</label>
                            <label class="switch">
                                <input type="checkbox" id="auto-block" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label for="daily-report">Rapport quotidien:</label>
                            <label class="switch">
                                <input type="checkbox" id="daily-report" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="settings-actions">
                    <button id="save-settings" class="btn primary"><i class="fas fa-save"></i> Enregistrer les paramètres</button>
                    <button id="reset-settings" class="btn"><i class="fas fa-undo"></i> Réinitialiser</button>
                </div>
            </section>
        </main>

        <footer>
            <p>Moniteur de Sécurité Cognitive &copy; 2025 | <i class="fas fa-shield-alt"></i> Protection des processus cognitifs du système à mémoire thermique</p>
        </footer>
    </div>
    
    <script src="/js/secure-cognition-viewer.js"></script>
</body>
</html>
