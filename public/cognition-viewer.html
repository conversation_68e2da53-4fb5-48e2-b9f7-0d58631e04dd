<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisation Cognitive - Agent à Mémoire Thermique</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/cognition-viewer.css">
    <!-- Bibliothèque pour la visualisation de graphes -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.css" rel="stylesheet" type="text/css" />
    <!-- Bibliothèque pour l'interface utilisateur -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
</head>
<body class="cognition-viewer-body">
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <h1>Agent à Mémoire Thermique</h1>
                <h2>Visualisation des Processus Cognitifs</h2>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">Accueil</a></li>
                    <li><a href="code-evolution.html">Évolution de Code</a></li>
                    <li><a href="code-analyzer.html">Analyseur de Code</a></li>
                    <li><a href="cognition-viewer.html" class="active">Visualisation Cognitive</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container-fluid py-4">
            <div class="row">
                <!-- Panneau de contrôle -->
                <div class="col-md-3">
                    <div class="card cognitive-control-panel">
                        <div class="card-header">
                            <h3>Contrôles</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="visualization-mode" class="form-label">Mode de visualisation</label>
                                <select id="visualization-mode" class="form-select">
                                    <option value="graph">Graphe de pensées</option>
                                    <option value="timeline">Chronologie</option>
                                    <option value="heatmap">Carte thermique</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="source-filter" class="form-label">Filtrer par source</label>
                                <select id="source-filter" class="form-select">
                                    <option value="">Toutes les sources</option>
                                    <option value="thermal_memory">Mémoire Thermique</option>
                                    <option value="code_analyzer">Analyseur de Code</option>
                                    <option value="code_evolution">Évolution de Code</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category-filter" class="form-label">Filtrer par catégorie</label>
                                <select id="category-filter" class="form-select">
                                    <option value="">Toutes les catégories</option>
                                    <option value="decision">Décisions</option>
                                    <option value="observation">Observations</option>
                                    <option value="question">Questions</option>
                                    <option value="error">Erreurs</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="importance-filter" class="form-label">Importance minimale</label>
                                <input type="range" class="form-range" id="importance-filter" min="0" max="1" step="0.1" value="0">
                                <div class="d-flex justify-content-between">
                                    <small>Faible</small>
                                    <small>Élevée</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="time-window" class="form-label">Fenêtre temporelle</label>
                                <select id="time-window" class="form-select">
                                    <option value="realtime">Temps réel</option>
                                    <option value="5min">5 dernières minutes</option>
                                    <option value="1hour">Dernière heure</option>
                                    <option value="1day">Dernier jour</option>
                                </select>
                            </div>
                            
                            <button id="refresh-btn" class="btn btn-primary w-100">
                                <i class="bi bi-arrow-clockwise"></i> Actualiser
                            </button>
                            
                            <div class="form-check form-switch mt-3">
                                <input class="form-check-input" type="checkbox" id="auto-update" checked>
                                <label class="form-check-label" for="auto-update">Mise à jour automatique</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Statistiques</h3>
                        </div>
                        <div class="card-body" id="stats-panel">
                            <p>Chargement des statistiques...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Zone principale de visualisation -->
                <div class="col-md-9">
                    <div class="card cognitive-visualization">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3>Processus Cognitifs</h3>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary" id="zoom-in">
                                    <i class="bi bi-zoom-in"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="zoom-out">
                                    <i class="bi bi-zoom-out"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="reset-view">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="visualization-container">
                                <!-- La visualisation sera insérée ici par JavaScript -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Détails de la pensée</h3>
                        </div>
                        <div class="card-body" id="thought-details">
                            <p class="text-muted">Cliquez sur une pensée pour voir ses détails</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Threads cognitifs actifs -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>Threads Cognitifs Actifs</h3>
                        </div>
                        <div class="card-body">
                            <div id="active-threads" class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Catégorie</th>
                                            <th>Démarré</th>
                                            <th>Durée</th>
                                            <th>Pensées</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="threads-table-body">
                                        <!-- Les threads seront insérés ici par JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-container">
            <p>&copy; 2025 Agent à Mémoire Thermique - Tous droits réservés</p>
        </div>
    </footer>

    <!-- Modals -->
    <div class="modal fade" id="thread-detail-modal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails du Thread Cognitif</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="thread-detail-content">
                    <!-- Le contenu sera inséré par JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/cognition-viewer.js"></script>
</body>
</html>
