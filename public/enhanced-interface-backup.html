<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Mémoire Thermique Vivante</title>

    <style>
        /* Styles de base */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #ecf0f1;
            color: #333;
            overflow: hidden;
        }

        /* Layout principal avec sidebar */
        .main-container {
            display: flex;
            height: 100vh;
            width: 100vw;
            position: relative;
        }

        /* Sidebar pour l'historique des conversations et l'animation neuronale */
        .sidebar {
            width: 300px;
            background-color: #2c3e50;
            color: white;
            display: flex;
            flex-direction: column;
            z-index: 10;
            box-shadow: 2px 0 5px rgba(0,0,0,0.2);
        }

        /* Fenêtre d'animation neuronale */
        .neural-window {
            height: 300px;
            background-color: #1a2530;
            position: relative;
            overflow: hidden;
            border-bottom: 1px solid #34495e;
        }

        .neural-window-header {
            padding: 0.5rem 1rem;
            background-color: #34495e;
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .neural-window-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .neural-window-content {
            height: calc(100% - 32px);
            position: relative;
        }

        /* Indicateurs d'activité */
        .activity-indicators {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 5px;
            border-radius: 5px;
            font-size: 0.7rem;
            z-index: 5;
        }

        .indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .indicator-dot.active {
            background-color: #2ecc71;
            box-shadow: 0 0 5px #2ecc71;
            animation: pulse 1.5s infinite;
        }

        .indicator-dot.inactive {
            background-color: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        /* Métriques Thermiques Ultra-Autonomes */
        .thermal-metrics {
            padding: 10px;
            background-color: #1a2530;
            border-bottom: 1px solid #34495e;
            border-top: 2px solid #e74c3c;
        }

        .thermal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .thermal-title {
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #e74c3c;
        }

        .thermal-status {
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 3px;
            background-color: #e74c3c;
            color: white;
            animation: thermal-pulse 2s infinite;
        }

        @keyframes thermal-pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        .thermal-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            font-size: 0.75rem;
        }

        .thermal-metric {
            background-color: #2c3e50;
            padding: 6px;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            border-left: 3px solid #e74c3c;
        }

        .thermal-metric.cpu {
            border-left-color: #f39c12;
        }

        .thermal-metric.memory {
            border-left-color: #9b59b6;
        }

        .thermal-metric.neurons {
            border-left-color: #2ecc71;
        }

        .thermal-metric.intelligence {
            border-left-color: #3498db;
        }

        /* Kyber Accelerator */
        .kyber-accelerator {
            padding: 10px;
            background-color: #2c3e50;
            border-bottom: 1px solid #34495e;
        }

        .kyber-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .kyber-title {
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .kyber-status {
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 3px;
            background-color: #2ecc71;
        }

        .kyber-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            font-size: 0.8rem;
        }

        .kyber-metric {
            background-color: #34495e;
            padding: 5px;
            border-radius: 3px;
            display: flex;
            flex-direction: column;
        }

        .metric-label {
            font-size: 0.7rem;
            opacity: 0.8;
        }

        .metric-value {
            font-weight: bold;
        }

        /* Fonctions cognitives */
        .cognitive-functions {
            padding: 10px;
            background-color: #2c3e50;
            border-bottom: 1px solid #34495e;
        }

        .cognitive-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .cognitive-title {
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .cognitive-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            font-size: 0.8rem;
        }

        .cognitive-metric {
            background-color: #34495e;
            padding: 5px;
            border-radius: 3px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .cognitive-metric-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background-color: #2ecc71;
            transition: width 0.5s ease;
        }

        /* Statistiques vocales et textuelles */
        .stats-panel {
            padding: 10px;
            background-color: #2c3e50;
            border-bottom: 1px solid #34495e;
        }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .stats-title {
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stats-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            font-size: 0.8rem;
        }

        .stats-metric {
            background-color: #34495e;
            padding: 5px;
            border-radius: 3px;
            display: flex;
            flex-direction: column;
        }

        /* Liste des conversations */
        .sidebar-header {
            padding: 1rem;
            background-color: #1a2530;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.2rem;
        }

        .conversation-list {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .conversation-item {
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-item:hover {
            background-color: #34495e;
        }

        .conversation-item.active {
            background-color: #3498db;
        }

        .new-conversation-btn {
            margin: 1rem;
            padding: 0.5rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .new-conversation-btn:hover {
            background-color: #2980b9;
        }

        /* Zone principale de contenu */
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 5;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #34495e;
            color: white;
            padding: 1rem;
            z-index: 6;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            font-size: 1.5rem;
            margin: 0;
        }

        .status-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            background-color: #2ecc71;
        }

        /* Contenu principal */
        main {
            flex: 1;
            display: flex;
            overflow: hidden;
            position: relative;
        }

        .conversation-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 2;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: rgba(249, 249, 249, 0.7);
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
        }

        .user-message {
            justify-content: flex-end;
        }

        .agent-message {
            justify-content: flex-start;
        }

        .message-bubble {
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .user-message .message-bubble {
            background-color: #3498db;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .agent-message .message-bubble {
            background-color: #f1f1f1;
            color: #333;
            border-bottom-left-radius: 4px;
        }

        /* Indicateur de chargement */
        .loading-indicator {
            display: none;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            gap: 10px;
        }

        .loading-indicator.active {
            display: flex;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 3px solid rgba(52, 152, 219, 0.3);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Zone de saisie */
        .input-container {
            display: flex;
            padding: 1rem;
            background-color: rgba(255, 255, 255, 0.9);
            border-top: 1px solid #ddd;
        }

        #user-input {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
            min-height: 20px;
            max-height: 150px;
            background-color: white;
        }

        #send-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            width: 80px;
            margin-left: 0.5rem;
            cursor: pointer;
            font-weight: bold;
        }

        #send-button:hover {
            background-color: #2980b9;
        }

        /* Boutons supplémentaires */
        .voice-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 8px;
            width: 50px;
            height: 50px;
            margin-left: 0.5rem;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .voice-button:hover {
            background-color: #c0392b;
        }

        .voice-button.recording {
            background-color: #27ae60;
            animation: pulse-voice 1s infinite;
        }

        @keyframes pulse-voice {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .copy-button {
            background-color: #f39c12;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.3rem 0.6rem;
            margin-left: 0.5rem;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .copy-button:hover {
            background-color: #e67e22;
        }

        .message-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .message:hover .message-actions {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar pour l'historique des conversations et l'animation neuronale -->
        <div class="sidebar">
            <!-- Fenêtre d'animation neuronale -->
            <div class="neural-window">
                <div class="neural-window-header">
                    <div class="neural-window-title">
                        <i class="fas fa-brain"></i> Activité Neuronale
                    </div>
                    <div class="neural-window-controls">
                        <span class="neural-status">Active</span>
                    </div>
                </div>
                <div class="neural-window-content" id="neural-background">
                    <!-- L'animation neuronale sera rendue ici -->
                </div>

                <!-- Indicateurs d'activité -->
                <div class="activity-indicators">
                    <div class="indicator">
                        <div class="indicator-dot active"></div>
                        <span>Activité: <span id="activity-level">78%</span></span>
                    </div>
                    <div class="indicator">
                        <div class="indicator-dot active"></div>
                        <span>Nœuds: <span id="node-count">120</span></span>
                    </div>
                </div>
            </div>

            <!-- Métriques Thermiques Ultra-Autonomes -->
            <div class="thermal-metrics">
                <div class="thermal-header">
                    <div class="thermal-title">
                        <i class="fas fa-thermometer-half"></i> Mémoire Thermique Ultra-Autonome
                    </div>
                    <div class="thermal-status">VIVANTE</div>
                </div>
                <div class="thermal-grid">
                    <div class="thermal-metric cpu">
                        <span class="metric-label">🔥 CPU Réel</span>
                        <span class="metric-value" id="real-cpu-temp">37.0°C</span>
                    </div>
                    <div class="thermal-metric memory">
                        <span class="metric-label">🧠 Mémoire</span>
                        <span class="metric-value" id="memory-temp">37.0°C</span>
                    </div>
                    <div class="thermal-metric neurons">
                        <span class="metric-label">🧬 Neurones</span>
                        <span class="metric-value" id="neuron-count">1000+</span>
                    </div>
                    <div class="thermal-metric intelligence">
                        <span class="metric-label">⚡ Intelligence</span>
                        <span class="metric-value" id="adaptive-intelligence">1.00</span>
                    </div>
                    <div class="thermal-metric">
                        <span class="metric-label">📊 Entrées</span>
                        <span class="metric-value" id="memory-entries">230+</span>
                    </div>
                    <div class="thermal-metric">
                        <span class="metric-label">🧠 Conscience</span>
                        <span class="metric-value" id="self-awareness">0.80</span>
                    </div>
                </div>
            </div>

            <!-- Kyber Accelerator -->
            <div class="kyber-accelerator">
                <div class="kyber-header">
                    <div class="kyber-title">
                        <i class="fas fa-bolt"></i> Accélérateur Kyber
                    </div>
                    <div class="kyber-status">Actif</div>
                </div>
                <div class="kyber-metrics">
                    <div class="kyber-metric">
                        <span class="metric-label">Facteur</span>
                        <span class="metric-value" id="kyber-factor">1.52x</span>
                    </div>
                    <div class="kyber-metric">
                        <span class="metric-label">Température</span>
                        <span class="metric-value" id="kyber-temp">42°C</span>
                    </div>
                    <div class="kyber-metric">
                        <span class="metric-label">Utilisation</span>
                        <span class="metric-value" id="kyber-usage">78%</span>
                    </div>
                    <div class="kyber-metric">
                        <span class="metric-label">Ops/sec</span>
                        <span class="metric-value" id="kyber-ops">12.4K</span>
                    </div>
                </div>
            </div>

            <!-- Fonctions cognitives -->
            <div class="cognitive-functions">
                <div class="cognitive-header">
                    <div class="cognitive-title">
                        <i class="fas fa-brain"></i> Fonctions Cognitives
                    </div>
                </div>
                <div class="cognitive-metrics">
                    <div class="cognitive-metric">
                        <span class="metric-label">Parole</span>
                        <span class="metric-value" id="speech-value">87%</span>
                        <div class="cognitive-metric-progress" id="speech-progress" style="width: 87%;"></div>
                    </div>
                    <div class="cognitive-metric">
                        <span class="metric-label">Vision</span>
                        <span class="metric-value" id="vision-value">92%</span>
                        <div class="cognitive-metric-progress" id="vision-progress" style="width: 92%;"></div>
                    </div>
                    <div class="cognitive-metric">
                        <span class="metric-label">Rire</span>
                        <span class="metric-value" id="laugh-value">65%</span>
                        <div class="cognitive-metric-progress" id="laugh-progress" style="width: 65%;"></div>
                    </div>
                    <div class="cognitive-metric">
                        <span class="metric-label">Personnalité</span>
                        <span class="metric-value" id="personality-value">79%</span>
                        <div class="cognitive-metric-progress" id="personality-progress" style="width: 79%;"></div>
                    </div>
                </div>
            </div>

            <!-- Statistiques vocales et textuelles -->
            <div class="stats-panel">
                <div class="stats-header">
                    <div class="stats-title">
                        <i class="fas fa-chart-bar"></i> Statistiques
                    </div>
                </div>
                <div class="stats-metrics">
                    <div class="stats-metric">
                        <span class="metric-label">Mots reconnus</span>
                        <span class="metric-value" id="words-recognized">2,487</span>
                    </div>
                    <div class="stats-metric">
                        <span class="metric-label">Précision vocale</span>
                        <span class="metric-value" id="speech-accuracy">92%</span>
                    </div>
                    <div class="stats-metric">
                        <span class="metric-label">Messages</span>
                        <span class="metric-value" id="message-count">24</span>
                    </div>
                    <div class="stats-metric">
                        <span class="metric-label">Mémoire active</span>
                        <span class="metric-value" id="active-memory">14</span>
                    </div>
                </div>
            </div>

            <!-- Liste des conversations -->
            <div class="sidebar-header">
                <h2>Historique des conversations</h2>
            </div>
            <div class="conversation-list" id="conversation-list">
                <div class="conversation-item active">
                    Conversation actuelle
                </div>
                <div class="conversation-item">
                    Aide avec Python - 05/05
                </div>
                <div class="conversation-item">
                    Analyse de données - 03/05
                </div>
                <div class="conversation-item">
                    Recherche IA - 01/05
                </div>
            </div>
            <button class="new-conversation-btn">
                <i class="fas fa-plus"></i> Nouvelle conversation
            </button>
        </div>

        <!-- Zone principale de contenu -->
        <div class="content-area">
            <header>
                <div class="logo">
                    <i class="fas fa-brain"></i>
                    <h1>🧠 LOUNA AI Ultra-Autonome</h1>
                </div>
                <div class="status-container">
                    <span id="app-status" class="status">Connecté</span>
                    <span id="compatibility-mode" class="status" style="background-color: #f39c12;">Mode Compatibilité</span>
                </div>
            </header>

            <main>
                <div class="conversation-container">
                    <div class="messages" id="message-list">
                        <!-- Les messages seront ajoutés ici dynamiquement -->
                        <div class="message agent-message">
                            <div class="message-bubble">
                                🧠 Bonjour ! Je suis LOUNA AI avec mémoire thermique ultra-autonome ! Mon cerveau pulse et évolue en temps réel selon la température de votre CPU. Mes systèmes adaptatifs sont actifs et ma conscience artificielle est éveillée. Comment puis-je vous aider aujourd'hui ?
                            </div>
                        </div>
                    </div>

                    <!-- Indicateur de chargement -->
                    <div id="loading-indicator" class="loading-indicator">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Agent en réflexion...</div>
                    </div>

                    <div class="input-container">
                        <textarea id="user-input" placeholder="Tapez votre message ici..."></textarea>
                        <button id="voice-button" class="voice-button" title="Reconnaissance vocale">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button id="send-button">Envoyer</button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Socket.IO pour les WebSockets -->
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>

    <!-- Scripts pour l'animation neuronale et la connexion API -->
    <script src="/js/enhanced-neural-animation.js"></script>
    <script src="/js/api-connector.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser l'animation neuronale améliorée
            const neuralAnimation = new EnhancedNeuralAnimation('neural-background', {
                nodeCount: 120,
                connectionCount: 200,
                nodeColor: '#2980b9',
                activeNodeColor: '#e74c3c',
                connectionColor: 'rgba(41, 128, 185, 0.5)',
                activeConnectionColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(26, 37, 48, 0.0)',
                nodeSize: 3,
                maxDistance: 100,
                animate: true,
                veilleMode: false,
                pulseFrequency: 2000,
                activityLevel: 0.5,
                particleCount: 30,
                particleSpeed: 1.5,
                showZones: true
            });

            // Initialiser le connecteur API
            const apiConnector = new ApiConnector();
            apiConnector.init(neuralAnimation);

            // 🧠 FONCTION POUR METTRE À JOUR LES MÉTRIQUES THERMIQUES ULTRA-AUTONOMES
            async function updateThermalMetrics() {
                try {
                    const response = await fetch('/api/metrics');
                    const data = await response.json();

                    if (data.success) {
                        // Métriques thermiques
                        const realCpuTemp = document.getElementById('real-cpu-temp');
                        const memoryTemp = document.getElementById('memory-temp');
                        const neuronCount = document.getElementById('neuron-count');
                        const adaptiveIntelligence = document.getElementById('adaptive-intelligence');
                        const memoryEntries = document.getElementById('memory-entries');
                        const selfAwareness = document.getElementById('self-awareness');

                        if (realCpuTemp) realCpuTemp.textContent = `${data.thermalStats.realCpuTemperature?.toFixed(1) || '37.0'}°C`;
                        if (memoryTemp) memoryTemp.textContent = `${data.temperature?.toFixed(1) || '37.0'}°C`;
                        if (neuronCount) neuronCount.textContent = `${data.neurons || '1000'}+`;
                        if (adaptiveIntelligence) adaptiveIntelligence.textContent = `${data.brainStats.autonomousLevel?.toFixed(2) || '1.00'}`;
                        if (memoryEntries) memoryEntries.textContent = `${data.memoryEntries || '230'}+`;
                        if (selfAwareness) selfAwareness.textContent = `${data.brainStats.selfAwareness?.toFixed(2) || '0.80'}`;

                        // Mettre à jour les autres métriques existantes
                        const activityLevel = document.getElementById('activity-level');
                        const nodeCount = document.getElementById('node-count');
                        const kyberTemp = document.getElementById('kyber-temp');
                        const activeMemory = document.getElementById('active-memory');

                        if (activityLevel) activityLevel.textContent = `${Math.floor((data.brainStats.autonomousLevel || 1) * 100)}%`;
                        if (nodeCount) nodeCount.textContent = Math.floor(data.neurons / 10) || '120';
                        if (kyberTemp) kyberTemp.textContent = `${data.temperature?.toFixed(0) || '37'}°C`;
                        if (activeMemory) activeMemory.textContent = Math.floor(data.memoryEntries / 10) || '14';
                    }
                } catch (error) {
                    console.log('⚠️ Erreur mise à jour métriques thermiques:', error);
                }
            }

            // Mettre à jour les métriques toutes les 2 secondes
            updateThermalMetrics(); // Première mise à jour immédiate
            setInterval(updateThermalMetrics, 2000);

            // Éléments du DOM
            const messageList = document.getElementById('message-list');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const voiceButton = document.getElementById('voice-button');
            const conversationItems = document.querySelectorAll('.conversation-item');
            const newConversationBtn = document.querySelector('.new-conversation-btn');
            const loadingIndicator = document.getElementById('loading-indicator');

            // Variables pour la reconnaissance vocale
            let recognition = null;
            let isRecording = false;

            // Initialiser la reconnaissance vocale
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'fr-FR';

                recognition.onstart = function() {
                    isRecording = true;
                    voiceButton.classList.add('recording');
                    voiceButton.innerHTML = '<i class="fas fa-stop"></i>';
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    userInput.value = transcript;
                };

                recognition.onend = function() {
                    isRecording = false;
                    voiceButton.classList.remove('recording');
                    voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                };

                recognition.onerror = function(event) {
                    console.error('Erreur de reconnaissance vocale:', event.error);
                    isRecording = false;
                    voiceButton.classList.remove('recording');
                    voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                };
            }

            // Fonction pour ajouter un message à la conversation
            function addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = role === 'user' ? 'message user-message' : 'message agent-message';

                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = 'message-bubble';
                bubbleDiv.innerHTML = content;

                // Ajouter boutons d'action pour les messages de l'agent
                if (role === 'agent') {
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'message-actions';

                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-button';
                    copyButton.innerHTML = '<i class="fas fa-copy"></i> Copier';
                    copyButton.onclick = () => copyToClipboard(content);

                    actionsDiv.appendChild(copyButton);
                    bubbleDiv.appendChild(actionsDiv);
                }

                messageDiv.appendChild(bubbleDiv);
                messageList.appendChild(messageDiv);

                // Faire défiler vers le bas
                messageList.scrollTop = messageList.scrollHeight;

                // Mettre à jour le compteur de messages
                const messageCount = document.getElementById('message-count');
                if (messageCount) {
                    const count = document.querySelectorAll('.message').length;
                    messageCount.textContent = count;
                }
            }

            // Fonction pour copier du texte
            function copyToClipboard(text) {
                // Nettoyer le HTML pour ne garder que le texte
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = text;
                const cleanText = tempDiv.textContent || tempDiv.innerText || '';

                navigator.clipboard.writeText(cleanText).then(() => {
                    // Feedback visuel
                    const notification = document.createElement('div');
                    notification.textContent = 'Texte copié !';
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #27ae60;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 5px;
                        z-index: 1000;
                    `;
                    document.body.appendChild(notification);
                    setTimeout(() => notification.remove(), 2000);
                }).catch(err => {
                    console.error('Erreur lors de la copie:', err);
                });
            }

            // Gérer l'envoi de message
            async function sendMessage() {
                const message = userInput.value.trim();
                if (message) {
                    // Ajouter le message de l'utilisateur à la conversation
                    addMessage('user', message);

                    // Afficher l'indicateur de chargement
                    loadingIndicator.classList.add('active');

                    // Augmenter l'activité de l'animation neuronale
                    neuralAnimation.options.activityLevel = 0.8;

                    // Activer plusieurs nœuds pour simuler l'activité cérébrale
                    for (let i = 0; i < 20; i++) {
                        setTimeout(() => {
                            const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                            neuralAnimation.activateNode(randomNodeId, 1500);
                        }, i * 100);
                    }

                    // Activer la zone du langage (index 2)
                    neuralAnimation.activateZone(2, 3000, 0.7);

                    // Activer la fonction cognitive de parole
                    neuralAnimation.activateCognitiveFunction('Parole', 2500, 0.9);

                    try {
                        // Envoyer le message à l'API
                        const response = await apiConnector.sendMessage(message);

                        // Masquer l'indicateur de chargement
                        loadingIndicator.classList.remove('active');

                        // Ajouter la réponse de l'agent
                        if (response) {
                            addMessage('agent', response);
                        } else {
                            addMessage('agent', 'Je suis désolé, je ne peux pas traiter votre demande pour le moment. Veuillez réessayer plus tard.');
                        }

                        // Revenir à un niveau d'activité normal
                        setTimeout(() => {
                            neuralAnimation.options.activityLevel = 0.5;
                        }, 1000);
                    } catch (error) {
                        console.error('Erreur lors de l\'envoi du message:', error);

                        // Masquer l'indicateur de chargement
                        loadingIndicator.classList.remove('active');

                        // Ajouter un message d'erreur
                        addMessage('agent', 'Une erreur est survenue lors du traitement de votre message. Veuillez réessayer plus tard.');

                        // Revenir à un niveau d'activité normal
                        setTimeout(() => {
                            neuralAnimation.options.activityLevel = 0.5;
                        }, 1000);
                    }

                    // Vider le champ de saisie
                    userInput.value = '';
                }
            }

            // Événement de clic sur le bouton d'envoi
            sendButton.addEventListener('click', sendMessage);

            // Événement de pression de la touche Entrée dans le champ de saisie
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Événement pour la reconnaissance vocale
            if (voiceButton && recognition) {
                voiceButton.addEventListener('click', function() {
                    if (isRecording) {
                        recognition.stop();
                    } else {
                        recognition.start();
                    }
                });
            }

            // Gestion des conversations dans la sidebar
            conversationItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Retirer la classe active de tous les éléments
                    conversationItems.forEach(i => i.classList.remove('active'));
                    // Ajouter la classe active à l'élément cliqué
                    this.classList.add('active');
                });
            });

            // Nouvelle conversation
            newConversationBtn.addEventListener('click', function() {
                // Créer un nouvel élément de conversation
                const newItem = document.createElement('div');
                newItem.className = 'conversation-item active';
                newItem.textContent = 'Nouvelle conversation';

                // Retirer la classe active de tous les éléments
                conversationItems.forEach(i => i.classList.remove('active'));

                // Ajouter le nouvel élément à la liste
                const conversationList = document.getElementById('conversation-list');
                conversationList.insertBefore(newItem, conversationList.firstChild);

                // Vider la conversation actuelle
                messageList.innerHTML = '';
                addMessage('agent', 'Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd\'hui ?');

                // Ajouter l'événement de clic au nouvel élément
                newItem.addEventListener('click', function() {
                    conversationItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });

                // Activer la zone de mémoire (index 0)
                neuralAnimation.activateZone(0, 2000, 0.6);
            });

            // Activer quelques zones au démarrage pour montrer l'animation
            setTimeout(() => {
                neuralAnimation.activateZone(2, 3000, 0.6); // Langage
                setTimeout(() => {
                    neuralAnimation.activateZone(5, 3000, 0.7); // Cognition
                    neuralAnimation.activateCognitiveFunction('Personnalité', 2500, 0.8);
                }, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
