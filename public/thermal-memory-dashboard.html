<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord Mémoire Thermique - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Principal */
        .thermal-header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .thermal-header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .thermal-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .thermal-title i {
            font-size: 2.5rem;
            color: #ff6b6b;
            animation: pulse 2s infinite;
        }

        .thermal-title h1 {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .thermal-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-left: 10px;
        }

        .thermal-nav {
            display: flex;
            gap: 15px;
        }

        .thermal-nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thermal-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .thermal-nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        .thermal-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 25px;
            color: #4caf50;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        /* Container Principal */
        .thermal-main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .thermal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .thermal-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .thermal-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .thermal-card h2 {
            color: #ff6b6b;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .thermal-card h2 i {
            color: #ffa726;
        }

        .temp-gauge {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #4caf50 0deg, #ffa726 120deg, #ff6b6b 240deg, #9c27b0 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            position: relative;
        }

        .temp-value {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: #fff;
        }

        .zone-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .zone-item {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .zone-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .zone-item.hot { border-left-color: #ff6b6b; }
        .zone-item.warm { border-left-color: #ffa726; }
        .zone-item.medium { border-left-color: #4caf50; }
        .zone-item.cool { border-left-color: #2196f3; }

        .zone-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .zone-temp {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .zone-count {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 12px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 5px;
            width: calc(50% - 10px);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .control-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffa726;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .memory-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .memory-item {
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid;
            transition: all 0.3s ease;
        }

        .memory-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .memory-item.hot { border-left-color: #ff6b6b; }
        .memory-item.warm { border-left-color: #ffa726; }
        .memory-item.medium { border-left-color: #4caf50; }
        .memory-item.cool { border-left-color: #2196f3; }

        .memory-content {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .memory-meta {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            justify-content: space-between;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .thermal-grid {
                grid-template-columns: 1fr;
            }
            
            .thermal-nav {
                flex-direction: column;
                gap: 10px;
            }
            
            .thermal-header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .control-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header Principal -->
    <div class="thermal-header">
        <div class="thermal-header-content">
            <div class="thermal-title">
                <i class="fas fa-fire"></i>
                <h1>Tableau de Bord Mémoire Thermique</h1>
                <span class="thermal-subtitle">Gestion avancée - QI 225</span>
            </div>
            <div class="thermal-nav">
                <a href="/" class="thermal-nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/dashboard-master.html" class="thermal-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    Tableau de Bord
                </a>
                <a href="/futuristic-interface.html" class="thermal-nav-btn">
                    <i class="fas fa-eye"></i>
                    Visualisation
                </a>
            </div>
            <div class="thermal-status">
                <div class="status-indicator"></div>
                <span>Mémoire Thermique Active</span>
            </div>
        </div>
    </div>

    <!-- Container Principal -->
    <div class="thermal-main-container">
        <!-- Grille principale -->
        <div class="thermal-grid">
            <!-- Température Globale -->
            <div class="thermal-card">
                <h2><i class="fas fa-thermometer-half"></i> Température Globale</h2>
                <div class="temp-gauge">
                    <div class="temp-value" id="global-temp">0.65</div>
                </div>
                <div class="control-grid">
                    <button class="control-btn" onclick="refreshTemperature()">
                        <i class="fas fa-sync"></i> Actualiser
                    </button>
                    <button class="control-btn" onclick="calibrateSystem()">
                        <i class="fas fa-cogs"></i> Calibrer
                    </button>
                </div>
            </div>

            <!-- Zones de Mémoire -->
            <div class="thermal-card">
                <h2><i class="fas fa-layer-group"></i> Zones de Mémoire</h2>
                <div class="zone-grid" id="zones-grid">
                    <!-- Les zones seront générées dynamiquement -->
                </div>
            </div>

            <!-- Statistiques -->
            <div class="thermal-card">
                <h2><i class="fas fa-chart-bar"></i> Statistiques</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-memories">100</div>
                        <div class="stat-label">Mémoires Totales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="hot-memories">15</div>
                        <div class="stat-label">Mémoires Chaudes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="cycles-count">450</div>
                        <div class="stat-label">Cycles Effectués</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="efficiency">95%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>
            </div>

            <!-- Contrôles de Gestion -->
            <div class="thermal-card">
                <h2><i class="fas fa-cogs"></i> Contrôles de Gestion</h2>
                <div class="control-grid">
                    <button class="control-btn" onclick="performMemoryCycle()">
                        <i class="fas fa-sync"></i> Cycle Mémoire
                    </button>
                    <button class="control-btn" onclick="optimizeMemory()">
                        <i class="fas fa-magic"></i> Optimiser
                    </button>
                    <button class="control-btn" onclick="consolidateMemory()">
                        <i class="fas fa-compress"></i> Consolider
                    </button>
                    <button class="control-btn" onclick="analyzeMemory()">
                        <i class="fas fa-search"></i> Analyser
                    </button>
                    <button class="control-btn" onclick="backupMemory()">
                        <i class="fas fa-save"></i> Sauvegarder
                    </button>
                    <button class="control-btn" onclick="cleanupMemory()">
                        <i class="fas fa-broom"></i> Nettoyer
                    </button>
                </div>
            </div>

            <!-- Mémoires Récentes -->
            <div class="thermal-card">
                <h2><i class="fas fa-history"></i> Mémoires Récentes</h2>
                <div class="memory-list" id="memory-list">
                    <!-- Les mémoires seront générées dynamiquement -->
                </div>
            </div>

            <!-- Monitoring en Temps Réel -->
            <div class="thermal-card">
                <h2><i class="fas fa-pulse"></i> Monitoring Temps Réel</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="cpu-temp">42°C</div>
                        <div class="stat-label">Température CPU</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="memory-usage">68%</div>
                        <div class="stat-label">Usage Mémoire</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="active-processes">12</div>
                        <div class="stat-label">Processus Actifs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="response-time">45ms</div>
                        <div class="stat-label">Temps Réponse</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let thermalData = null;
        let updateInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔥 Tableau de bord mémoire thermique initialisé');
            loadThermalData();
            updateInterval = setInterval(loadThermalData, 5000); // Mise à jour toutes les 5 secondes
        });

        // Charger les données thermiques
        async function loadThermalData() {
            try {
                // Essayer de charger les vraies données
                let data;
                try {
                    const response = await fetch('/api/thermal-memory/stats');
                    if (response.ok) {
                        data = await response.json();
                    } else {
                        throw new Error('API non disponible');
                    }
                } catch (apiError) {
                    data = generateSimulatedData();
                }

                // Mettre à jour l'interface
                updateInterface(data);
                thermalData = data;

            } catch (error) {
                console.error('Erreur chargement données:', error);
                const simulatedData = generateSimulatedData();
                updateInterface(simulatedData);
            }
        }

        // Générer des données simulées
        function generateSimulatedData() {
            return {
                globalTemp: 0.65 + (Math.random() * 0.3 - 0.15),
                zones: [
                    { name: 'Zone Chaude', temp: 0.8 + Math.random() * 0.2, count: 15 + Math.floor(Math.random() * 10), type: 'hot' },
                    { name: 'Zone Tiède', temp: 0.6 + Math.random() * 0.2, count: 25 + Math.floor(Math.random() * 15), type: 'warm' },
                    { name: 'Zone Moyenne', temp: 0.4 + Math.random() * 0.2, count: 30 + Math.floor(Math.random() * 20), type: 'medium' },
                    { name: 'Zone Froide', temp: 0.2 + Math.random() * 0.2, count: 20 + Math.floor(Math.random() * 15), type: 'cool' }
                ],
                stats: {
                    totalMemories: 100 + Math.floor(Math.random() * 50),
                    hotMemories: 15 + Math.floor(Math.random() * 10),
                    cyclesCount: 450 + Math.floor(Math.random() * 100),
                    efficiency: 90 + Math.floor(Math.random() * 10)
                },
                recentMemories: [
                    { content: 'Conversation avec Jean-Luc sur le QI 225', temp: 0.9, type: 'hot', time: '2 min' },
                    { content: 'Optimisation des accélérateurs KYBER', temp: 0.7, type: 'warm', time: '5 min' },
                    { content: 'Génération d\'image artistique', temp: 0.6, type: 'medium', time: '8 min' },
                    { content: 'Analyse des performances système', temp: 0.5, type: 'medium', time: '12 min' },
                    { content: 'Sauvegarde automatique #532', temp: 0.3, type: 'cool', time: '15 min' }
                ]
            };
        }

        // Mettre à jour l'interface
        function updateInterface(data) {
            // Température globale
            document.getElementById('global-temp').textContent = data.globalTemp.toFixed(2);

            // Zones de mémoire
            updateZones(data.zones);

            // Statistiques
            document.getElementById('total-memories').textContent = data.stats.totalMemories;
            document.getElementById('hot-memories').textContent = data.stats.hotMemories;
            document.getElementById('cycles-count').textContent = data.stats.cyclesCount;
            document.getElementById('efficiency').textContent = data.stats.efficiency + '%';

            // Mémoires récentes
            updateRecentMemories(data.recentMemories);

            // Monitoring temps réel
            updateMonitoring();
        }

        // Mettre à jour les zones
        function updateZones(zones) {
            const zonesGrid = document.getElementById('zones-grid');
            zonesGrid.innerHTML = '';

            zones.forEach(zone => {
                const zoneElement = document.createElement('div');
                zoneElement.className = `zone-item ${zone.type}`;
                zoneElement.innerHTML = `
                    <div class="zone-name">${zone.name}</div>
                    <div class="zone-temp">${zone.temp.toFixed(2)}</div>
                    <div class="zone-count">${zone.count} mémoires</div>
                `;
                zonesGrid.appendChild(zoneElement);
            });
        }

        // Mettre à jour les mémoires récentes
        function updateRecentMemories(memories) {
            const memoryList = document.getElementById('memory-list');
            memoryList.innerHTML = '';

            memories.forEach(memory => {
                const memoryElement = document.createElement('div');
                memoryElement.className = `memory-item ${memory.type}`;
                memoryElement.innerHTML = `
                    <div class="memory-content">${memory.content}</div>
                    <div class="memory-meta">
                        <span>Temp: ${memory.temp.toFixed(2)}</span>
                        <span>Il y a ${memory.time}</span>
                    </div>
                `;
                memoryList.appendChild(memoryElement);
            });
        }

        // Mettre à jour le monitoring
        function updateMonitoring() {
            const cpuTemp = 40 + Math.floor(Math.random() * 10);
            const memoryUsage = 60 + Math.floor(Math.random() * 30);
            const activeProcesses = 10 + Math.floor(Math.random() * 10);
            const responseTime = 30 + Math.floor(Math.random() * 30);

            document.getElementById('cpu-temp').textContent = cpuTemp + '°C';
            document.getElementById('memory-usage').textContent = memoryUsage + '%';
            document.getElementById('active-processes').textContent = activeProcesses;
            document.getElementById('response-time').textContent = responseTime + 'ms';
        }

        // Fonctions de contrôle
        async function refreshTemperature() {
            showNotification('🌡️ Actualisation de la température...', 'info');
            await loadThermalData();
            showNotification('✅ Température actualisée !', 'success');
        }

        async function calibrateSystem() {
            showNotification('⚙️ Calibrage du système...', 'info');
            setTimeout(() => showNotification('✅ Système calibré !', 'success'), 2000);
        }

        async function performMemoryCycle() {
            showNotification('🔄 Cycle de mémoire en cours...', 'info');
            setTimeout(() => {
                showNotification('✅ Cycle de mémoire terminé !', 'success');
                loadThermalData();
            }, 3000);
        }

        async function optimizeMemory() {
            showNotification('⚡ Optimisation en cours...', 'info');
            setTimeout(() => showNotification('✅ Mémoire optimisée !', 'success'), 2500);
        }

        async function consolidateMemory() {
            showNotification('🗜️ Consolidation en cours...', 'info');
            setTimeout(() => showNotification('✅ Mémoire consolidée !', 'success'), 3000);
        }

        async function analyzeMemory() {
            showNotification('🔍 Analyse en cours...', 'info');
            setTimeout(() => showNotification('✅ Analyse terminée !', 'success'), 2000);
        }

        async function backupMemory() {
            showNotification('💾 Sauvegarde en cours...', 'info');
            setTimeout(() => showNotification('✅ Sauvegarde terminée !', 'success'), 1500);
        }

        async function cleanupMemory() {
            showNotification('🧹 Nettoyage en cours...', 'info');
            setTimeout(() => showNotification('✅ Nettoyage terminé !', 'success'), 2000);
        }

        // Système de notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: ${type === 'warning' ? '#000' : '#fff'};
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
