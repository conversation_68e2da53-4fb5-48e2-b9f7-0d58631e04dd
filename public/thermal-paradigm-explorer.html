<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explorateur du Paradigme Thermique</title>
    
    <!-- Styles communs -->
    <link rel="stylesheet" href="/css/styles.css">
    
    <!-- Styles spécifiques -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/themes/prism-tomorrow.min.css">
    <link rel="stylesheet" href="/css/thermal-paradigm.css">
    
    <!-- Scripts externes -->
    <script src="https://cdn.socket.io/4.6.1/socket.io.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/components/prism-javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/components/prism-java.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.24.1/components/prism-csharp.min.js"></script>
    
    <style>
        :root {
            --primary-color: #3a86ff;
            --secondary-color: #8338ec;
            --accent-color: #ffbe0b;
            --success-color: #06d6a0;
            --warning-color: #ffbe0b;
            --danger-color: #ef476f;
            --dark-color: #1a1a2e;
            --light-color: #f8f9fa;
            --border-color: #dee2e6;
            
            /* Couleurs spécifiques */
            --level-1-color: #e63946;  /* Critique */
            --level-2-color: #f77f00;  /* Important */
            --level-3-color: #fcbf49;  /* Utile */
            --level-4-color: #2a9d8f;  /* Contextuel */
            --level-5-color: #457b9d;  /* Éphémère */
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Barre latérale */
        .sidebar {
            width: 280px;
            background-color: var(--dark-color);
            color: white;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
        }
        
        .app-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
        }
        
        .app-title i {
            margin-right: 0.5rem;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            margin-bottom: 0.5rem;
        }
        
        .nav-menu a {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.7);
            padding: 0.75rem;
            border-radius: 6px;
            transition: all 0.2s;
        }
        
        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-menu a.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .nav-menu i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .nav-section {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            padding-left: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* Contenu principal */
        .main-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .page-header {
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 2rem;
            margin: 0;
            color: var(--dark-color);
        }
        
        .page-tools {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2a75f0;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #7029d8;
        }
        
        .btn-accent {
            background-color: var(--accent-color);
            color: var(--dark-color);
        }
        
        .btn-accent:hover {
            background-color: #e5a90a;
        }
        
        /* Cartes des principes */
        .principles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .principle-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .principle-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .principle-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .principle-title {
            margin: 0;
            font-size: 1.25rem;
        }
        
        .principle-body {
            padding: 1.5rem;
        }
        
        .principle-description {
            margin-top: 0;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .principle-examples {
            background-color: #f5f7fa;
            padding: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .code-preview {
            margin: 0;
            white-space: pre-wrap;
            overflow: auto;
            font-family: 'Consolas', monospace;
            max-height: 200px;
        }
        
        /* Modal de détail */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-container {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray-600);
            transition: color 0.2s;
        }
        
        .modal-close:hover {
            color: var(--danger-color);
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        /* Graphe d'évolution */
        .evolution-graph {
            width: 100%;
            height: 500px;
            background-color: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        /* Statistiques */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: var(--gray-600);
            text-align: center;
        }
        
        /* Formulaires */
        .form-container {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .form-textarea {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Barre latérale -->
        <aside class="sidebar">
            <div class="app-title">
                <i class="fas fa-fire"></i>
                <span>Paradigme Thermique</span>
            </div>
            
            <ul class="nav-menu">
                <li><a href="#overview" class="active" data-page="overview"><i class="fas fa-home"></i> Vue d'ensemble</a></li>
                <li><a href="#principles" data-page="principles"><i class="fas fa-book"></i> Principes</a></li>
                <li><a href="#evolution" data-page="evolution"><i class="fas fa-chart-line"></i> Évolution</a></li>
                <li><a href="#examples" data-page="examples"><i class="fas fa-code"></i> Exemples</a></li>
            </ul>
            
            <div class="nav-section">Développement</div>
            <ul class="nav-menu">
                <li><a href="#create" data-page="create"><i class="fas fa-plus-circle"></i> Créer un principe</a></li>
                <li><a href="#simulator" data-page="simulator"><i class="fas fa-flask"></i> Simulateur</a></li>
                <li><a href="#documentation" data-page="documentation"><i class="fas fa-file-alt"></i> Documentation</a></li>
            </ul>
            
            <div class="nav-section">Système</div>
            <ul class="nav-menu">
                <li><a href="#settings" data-page="settings"><i class="fas fa-cog"></i> Paramètres</a></li>
                <li><a href="#security" data-page="security"><i class="fas fa-shield-alt"></i> Sécurité</a></li>
            </ul>
        </aside>
        
        <!-- Contenu principal -->
        <main class="main-content">
            <!-- Vue d'ensemble -->
            <section id="overview" class="page active">
                <div class="page-header">
                    <h1 class="page-title">Vue d'ensemble du Paradigme Thermique</h1>
                    <div class="page-tools">
                        <button class="btn btn-primary"><i class="fas fa-sync"></i> Actualiser</button>
                        <button class="btn btn-secondary"><i class="fas fa-download"></i> Exporter</button>
                    </div>
                </div>

                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-value" id="stat-principles">14</div>
                        <div class="stat-label">Principes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-implementations">52</div>
                        <div class="stat-label">Implémentations</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-evolution">87%</div>
                        <div class="stat-label">Taux d'évolution</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-efficiency">94%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>

                <div class="evolution-graph" id="overview-graph"></div>

                <div class="principles-grid">
                    <div class="principle-card" data-principle="thermal-levels">
                        <div class="principle-header">
                            <h3 class="principle-title">Niveaux Thermiques</h3>
                        </div>
                        <div class="principle-body">
                            <p class="principle-description">Organisation de la mémoire en niveaux d'importance basés sur la "chaleur" des informations. Les données cruciales sont chaudes et descendent, les données moins importantes sont froides et montent.</p>
                        </div>
                    </div>

                    <div class="principle-card" data-principle="thermal-circulation">
                        <div class="principle-header">
                            <h3 class="principle-title">Circulation Thermique</h3>
                        </div>
                        <div class="principle-body">
                            <p class="principle-description">Mécanisme de circulation automatique des informations entre niveaux de mémoire basé sur l'usage, l'importance et l'âge des données.</p>
                        </div>
                    </div>

                    <div class="principle-card" data-principle="thermal-acceleration">
                        <div class="principle-header">
                            <h3 class="principle-title">Accélération Kyber</h3>
                        </div>
                        <div class="principle-body">
                            <p class="principle-description">Système d'optimisation des opérations de mémoire et d'apprentissage basé sur la chaleur des données et leur fréquence d'accès.</p>
                        </div>
                    </div>

                    <div class="principle-card" data-principle="thermal-coding">
                        <div class="principle-header">
                            <h3 class="principle-title">Codage Thermique</h3>
                        </div>
                        <div class="principle-body">
                            <p class="principle-description">Approche de programmation qui fait évoluer le code automatiquement en fonction de son utilisation et de son importance dans le système.</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <div class="modal-overlay" id="principle-detail-modal">
                <div class="modal-container">
                background-color: white;
                border-radius: 8px;
                width: 90%;
                max-width: 800px;
                max-height: 90vh;
                overflow-y: auto;
                position: relative;
            }
            
            .modal-header {
                padding: 1.5rem;
                border-bottom: 1px solid var(--border-color);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-title {
                margin: 0;
                font-size: 1.5rem;
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: var(--gray-600);
                transition: color 0.2s;
            }
            
            .modal-close:hover {
                color: var(--danger-color);
            }
            
            .modal-body {
                padding: 1.5rem;
            }
            
            /* Graphe d'évolution */
            .evolution-graph {
                width: 100%;
                height: 500px;
                background-color: var(--light-color);
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                margin-bottom: 2rem;
            }
            
            /* Statistiques */
            .stats-container {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }
            
            .stat-card {
                background-color: white;
                border-radius: 8px;
                padding: 1.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }
            
            .stat-value {
                font-size: 2.5rem;
                font-weight: bold;
                margin-bottom: 0.5rem;
            }
            
            .stat-label {
                font-size: 0.9rem;
                color: var(--gray-600);
                text-align: center;
            }
            
            /* Formulaires */
            .form-container {
                background-color: white;
                border-radius: 8px;
                padding: 1.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                margin-bottom: 2rem;
            }
            
            .form-group {
                margin-bottom: 1.5rem;
            }
            
            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
            }
            
            .form-input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                font-size: 1rem;
            }
            
            .form-textarea {
                min-height: 150px;
                resize: vertical;
            }
            
            .form-actions {
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Barre latérale -->
            <aside class="sidebar">
                <div class="app-title">
                    <i class="fas fa-fire"></i>
                    <span>Paradigme Thermique</span>
                    <h1 class="page-title">Paramètres</h1>
                </div>
                
                <div class="form-container">
                    <h2>Paramètres généraux</h2>
                    
                    <div class="form-group">
                        <label for="auto-update-interval" class="form-label">Intervalle de mise à jour automatique (secondes):</label>
                        <input type="number" id="auto-update-interval" class="form-input" value="60" min="10" max="3600">
                    </div>
                    
                    <div class="form-group">
                        <label for="default-view" class="form-label">Vue par défaut:</label>
                        <select id="default-view" class="form-input">
                            <option value="overview">Vue d'ensemble</option>
                            <option value="principles">Principes</option>
                            <option value="evolution">Évolution</option>
                            <option value="examples">Exemples</option>
                        </select>
                    </div>
                    
                    <h2>Paramètres d'apprentissage autonome</h2>
                    
                    <div class="form-group" style="display: flex; align-items: center;">
                        <input type="checkbox" id="enable-autonomous-learning" checked>
                        <label for="enable-autonomous-learning" style="margin-left: 0.5rem;">Activer l'apprentissage autonome</label>
                    </div>
                    
                    <div class="form-group">
                        <label for="learning-rate" class="form-label">Taux d'apprentissage:</label>
                        <input type="range" id="learning-rate" class="form-input" min="0.1" max="1" step="0.1" value="0.5">
                        <div style="display: flex; justify-content: space-between; font-size: 0.8rem;">
                            <span>Prudent</span>
                            <span>Équilibré</span>
                            <span>Agressif</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="source-repos" class="form-label">Dépôts sources pour l'apprentissage:</label>
                        <textarea id="source-repos" class="form-input form-textarea" placeholder="https://github.com/example/repo1
https://github.com/example/repo2"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button class="btn btn-secondary" id="reset-settings">Réinitialiser</button>
                        <button class="btn btn-primary" id="save-settings">Enregistrer</button>
                    </div>
                </div>
            </section>
            
            <!-- Sécurité -->
            <section id="security" class="page">
                <div class="page-header">
                    <h1 class="page-title">Sécurité</h1>
                </div>
                
                <div class="form-container">
                    <h2>Limites comportementales</h2>
                    <p>Définissez des comportements qui doivent être explicitement interdits pour l'agent d'apprentissage autonome.</p>
                    
                    <div class="form-group">
                        <label for="forbidden-behaviors" class="form-label">Comportements interdits:</label>
                        <textarea id="forbidden-behaviors" class="form-input form-textarea">mentir
manipuler
cacher_information
biaiser_resultats
ignorer_contraintes
auto_modification_interdite</textarea>
                    </div>
                    
                    <h2>Limites d'accès</h2>
                    
                    <div class="form-group">
                        <label for="allowed-directories" class="form-label">Répertoires autorisés:</label>
                        <textarea id="allowed-directories" class="form-input form-textarea">/Volumes/Sans titre 5/thermal_agent_app/data
/Volumes/Sans titre 5/thermal_agent_app/examples
/Volumes/Sans titre 5/thermal_agent_app/paradigm</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="forbidden-directories" class="form-label">Répertoires interdits:</label>
                        <textarea id="forbidden-directories" class="form-input form-textarea">/etc
/var
/System
/Applications
/Users
/Library</textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button class="btn btn-secondary" id="test-security">Tester la configuration</button>
                        <button class="btn btn-primary" id="save-security">Enregistrer</button>
                    </div>
                </div>
            </section>
        </main>
    </div>
    
    <!-- Modal de détail de paradigme -->
    <div class="paradigm-detail-modal" id="paradigm-detail-modal">
        <div class="paradigm-detail-content">
            <div class="paradigm-detail-header">
                <h2 class="paradigm-detail-title" id="paradigm-detail-title">Nom du paradigme</h2>
                <button class="paradigm-detail-close" id="paradigm-detail-close">&times;</button>
            </div>
            <div class="paradigm-detail-body" id="paradigm-detail-body">
                <!-- Contenu généré dynamiquement -->
            </div>
        </div>
    </div>
    
    <script src="/js/startup-handler.js"></script>
    <script src="/js/neural-animation.js"></script>
    <script src="/js/code-memory-connector.js"></script>
    <script src="/js/paradigm-generator.js"></script>
    <script src="/js/thermal-paradigm-explorer.js"></script>
    <script src="/js/paradigm-explorer-connector.js"></script>
    <script src="/js/auto-paradigm-initializer.js"></script>
</body>
</html>
