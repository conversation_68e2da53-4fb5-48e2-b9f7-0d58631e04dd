<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contrôle de Confidentialité | Mode Strictement Local</title>
    
    <!-- Styles principaux -->
    <link rel="stylesheet" href="/css/reset.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/secure-cognition.css">
    <link rel="stylesheet" href="/css/privacy-control.css">
    
    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        .privacy-panel {
            background-color: rgba(10, 25, 47, 0.8);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .status-secure {
            background-color: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            border: 1px solid #2ecc71;
        }
        
        .status-warning {
            background-color: rgba(241, 196, 15, 0.2);
            color: #f1c40f;
            border: 1px solid #f1c40f;
        }
        
        .status-danger {
            background-color: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        
        .policy-guarantee {
            background-color: rgba(52, 152, 219, 0.1);
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            font-size: 1.1em;
        }
        
        .connection-item {
            background-color: rgba(255, 255, 255, 0.05);
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2ecc71;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .security-metric {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }
        
        .security-metric-label {
            flex: 1;
            font-weight: 500;
        }
        
        .security-metric-value {
            font-weight: bold;
            min-width: 80px;
            text-align: right;
            margin-right: 15px;
        }
        
        .progress-bar {
            flex: 2;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 5px;
        }
        
        .progress-secure {
            background-color: #2ecc71;
        }
        
        .progress-warning {
            background-color: #f1c40f;
        }
        
        .progress-danger {
            background-color: #e74c3c;
        }
        
        .section-title {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #3498db;
        }
        
        .refresh-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            transition: background-color 0.3s;
        }
        
        .refresh-button i {
            margin-right: 5px;
        }
        
        .refresh-button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Navigation principale -->
        <nav class="main-nav">
            <div class="nav-container">
                <div class="logo-container">
                    <a href="index.html">
                        <img src="/img/logo-brain.svg" alt="Logo" class="logo-img">
                        <span class="logo-text">IAM Thermique</span>
                    </a>
                    <span class="mode-badge">MODE LOCAL</span>
                </div>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> Accueil</a></li>
                    <li><a href="brain-activity.html"><i class="fas fa-brain"></i> Activité Cérébrale</a></li>
                    <li><a href="thermal-paradigm-explorer.html"><i class="fas fa-fire"></i> Paradigme Thermique</a></li>
                    <li><a href="learning-dashboard.html"><i class="fas fa-graduation-cap"></i> Apprentissage</a></li>
                    <li class="active"><a href="privacy-control.html"><i class="fas fa-shield-alt"></i> Confidentialité</a></li>
                </ul>
            </div>
        </nav>
        
        <!-- Contenu principal -->
        <main class="main-content">
            <header class="page-header">
                <h1><i class="fas fa-shield-alt"></i> Contrôle de Confidentialité</h1>
                <div class="header-actions">
                    <button id="refreshStatusBtn" class="refresh-button">
                        <i class="fas fa-sync-alt"></i> Rafraîchir
                    </button>
                </div>
            </header>
            
            <div class="content-wrapper">
                <!-- Panneau d'état de la confidentialité -->
                <section class="privacy-panel">
                    <h2 class="section-title"><i class="fas fa-lock"></i> Statut de Confidentialité</h2>
                    
                    <div id="privacyStatusContainer">
                        <div class="status-badge status-secure">
                            <i class="fas fa-check-circle"></i> STRICTEMENT LOCAL
                        </div>
                        
                        <div class="policy-guarantee">
                            <p><strong>GARANTIE DE CONFIDENTIALITÉ:</strong> Ce système fonctionne exclusivement en mode local. Aucune donnée n'est partagée avec des services externes, aucune connexion n'est établie vers l'extérieur sans votre autorisation explicite. Vous gardez le contrôle total sur toutes vos données et votre code.</p>
                        </div>
                        
                        <div class="security-metrics">
                            <div class="security-metric">
                                <div class="security-metric-label">Mode Strict</div>
                                <div id="strictModeValue" class="security-metric-value">Activé</div>
                                <label class="toggle-switch">
                                    <input id="strictModeToggle" type="checkbox" checked disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">Communication Externe</div>
                                <div id="externalCommsValue" class="security-metric-value">Bloquée</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-secure" style="width: 100%"></div>
                                </div>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">Intégrité du Système</div>
                                <div id="integrityValue" class="security-metric-value">100%</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-secure" style="width: 100%"></div>
                                </div>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">État du Gardien</div>
                                <div id="guardianValue" class="security-metric-value">Actif</div>
                                <label class="toggle-switch">
                                    <input id="guardianToggle" type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Panneau de politique de confidentialité -->
                <section class="privacy-panel">
                    <h2 class="section-title"><i class="fas fa-user-shield"></i> Politique de Confidentialité</h2>
                    
                    <div id="privacyPolicyContainer">
                        <div class="policy-settings">
                            <div class="security-metric">
                                <div class="security-metric-label">Mode Strictement Local</div>
                                <div class="security-metric-value">Activé</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">Partage de Données</div>
                                <div class="security-metric-value">Désactivé</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">APIs Externes</div>
                                <div class="security-metric-value">Bloquées</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">Collecte de Données</div>
                                <div class="security-metric-value">Désactivée</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="security-metric">
                                <div class="security-metric-label">Stockage Local Uniquement</div>
                                <div class="security-metric-value">Activé</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked disabled>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Panneau des connexions -->
                <section class="privacy-panel">
                    <h2 class="section-title"><i class="fas fa-network-wired"></i> Connexions Actives</h2>
                    
                    <div id="connectionsContainer">
                        <p class="connections-info">Aucune connexion sortante n'est active. Toutes les opérations sont effectuées localement.</p>
                        
                        <div id="connectionsList" class="connections-list">
                            <!-- Liste des connexions (vide actuellement) -->
                        </div>
                    </div>
                </section>
                
                <!-- Panneau de journal d'activité -->
                <section class="privacy-panel">
                    <h2 class="section-title"><i class="fas fa-history"></i> Journal de Confidentialité</h2>
                    
                    <div id="activityLogContainer">
                        <div class="activity-log-entry">
                            <div class="activity-timestamp">07/05/2025 11:49:28</div>
                            <div class="activity-message"><i class="fas fa-shield-alt"></i> Gardien de confidentialité activé en mode strict</div>
                        </div>
                        <div class="activity-log-entry">
                            <div class="activity-timestamp">07/05/2025 11:49:28</div>
                            <div class="activity-message"><i class="fas fa-check-circle"></i> Vérification d'intégrité du système réussie</div>
                        </div>
                        <div class="activity-log-entry">
                            <div class="activity-timestamp">07/05/2025 11:49:28</div>
                            <div class="activity-message"><i class="fas fa-lock"></i> Mode strictement local confirmé</div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
        
        <!-- Pied de page -->
        <footer class="main-footer">
            <div class="footer-content">
                <p>© 2025 | Paradigme de Programmation Thermique | <strong>MODE STRICTEMENT LOCAL</strong></p>
            </div>
        </footer>
    </div>
    
    <script>
        // Fonction pour récupérer l'état du gardien de confidentialité
        function fetchPrivacyGuardianStatus() {
            fetch('/api/security/privacy-guardian')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updatePrivacyStatus(data.guardian);
                    } else {
                        console.error('Erreur lors de la récupération du statut du gardien de confidentialité');
                    }
                })
                .catch(error => {
                    console.error('Erreur de connexion:', error);
                });
        }
        
        // Fonction pour mettre à jour l'interface avec les données de statut
        function updatePrivacyStatus(status) {
            // Mettre à jour les valeurs des métriques
            document.getElementById('strictModeValue').textContent = status.strict_mode ? 'Activé' : 'Désactivé';
            document.getElementById('externalCommsValue').textContent = status.strict_mode ? 'Bloquée' : 'Contrôlée';
            document.getElementById('integrityValue').textContent = '100%';
            document.getElementById('guardianValue').textContent = status.enabled ? 'Actif' : 'Inactif';
            
            // Mettre à jour les toggles
            document.getElementById('strictModeToggle').checked = status.strict_mode;
            document.getElementById('guardianToggle').checked = status.enabled;
            
            // Mettre à jour la liste des connexions
            const connectionsContainer = document.getElementById('connectionsContainer');
            const connectionsList = document.getElementById('connectionsList');
            const connections = status.outbound_connections.connections;
            
            if (connections && connections.length > 0) {
                connectionsContainer.querySelector('.connections-info').textContent = 
                    `${connections.length} connexion(s) active(s) détectée(s).`;
                
                connectionsList.innerHTML = '';
                connections.forEach(conn => {
                    const item = document.createElement('div');
                    item.className = 'connection-item';
                    item.innerHTML = `
                        <div class="connection-details">
                            <strong>${conn.local_address}</strong> → ${conn.remote_address}
                        </div>
                        <div class="connection-status">${conn.status}</div>
                    `;
                    connectionsList.appendChild(item);
                });
            } else {
                connectionsContainer.querySelector('.connections-info').textContent = 
                    'Aucune connexion sortante n\'est active. Toutes les opérations sont effectuées localement.';
                connectionsList.innerHTML = '';
            }
            
            // Ajouter un événement au journal d'activité
            const now = new Date();
            const timestamp = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'activity-log-entry';
            logEntry.innerHTML = `
                <div class="activity-timestamp">${timestamp}</div>
                <div class="activity-message">
                    <i class="fas fa-sync-alt"></i> Statut du gardien de confidentialité mis à jour
                </div>
            `;
            
            const logContainer = document.getElementById('activityLogContainer');
            logContainer.insertBefore(logEntry, logContainer.firstChild);
        }
        
        // Événement pour le bouton de rafraîchissement
        document.getElementById('refreshStatusBtn').addEventListener('click', function() {
            fetchPrivacyGuardianStatus();
        });
        
        // Événement pour le toggle du gardien
        document.getElementById('guardianToggle').addEventListener('change', function() {
            alert('ATTENTION: Désactiver le gardien de confidentialité pourrait exposer vos données. Cette fonctionnalité est verrouillée en mode strictement local pour votre protection.');
            this.checked = true; // Forcer l'activation pour le moment
        });
        
        // Récupérer le statut initial
        document.addEventListener('DOMContentLoaded', function() {
            fetchPrivacyGuardianStatus();
        });
    </script>
</body>
</html>
