<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Code Avancé - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 10px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }
        .header h1 { font-size: 20px; font-weight: 600; display: flex; align-items: center; gap: 10px; }
        .nav-buttons { display: flex; gap: 8px; }
        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }
        .nav-btn:hover { background: rgba(255, 255, 255, 0.3); transform: translateY(-1px); }
        .main-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            height: calc(100vh - 60px);
            gap: 0;
        }
        .sidebar {
            background: rgba(0, 0, 0, 0.4);
            border-right: 2px solid rgba(255, 105, 180, 0.3);
            padding: 15px;
            overflow-y: auto;
        }
        .editor-container { display: flex; flex-direction: column; background: rgba(0, 0, 0, 0.2); }
        .properties-panel {
            background: rgba(0, 0, 0, 0.4);
            border-left: 2px solid rgba(255, 105, 180, 0.3);
            padding: 15px;
            overflow-y: auto;
        }
        .CodeMirror { height: 100% !important; font-size: 14px; font-family: 'Fira Code', 'Consolas', monospace; }
        .brain-connection {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid rgba(0, 255, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .ai-assist-btn {
            width: 100%;
            background: linear-gradient(135deg, #00ff00, #00cc00);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .ai-assist-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0, 255, 0, 0.4); }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-code"></i> Éditeur de Code Avancé - Louna AI (QI: 225)</h1>
        <div class="nav-buttons">
            <a href="/voice-system-enhanced.html" class="nav-btn"><i class="fas fa-microphone"></i> Vocal</a>
            <a href="/phone-camera-system.html" class="nav-btn"><i class="fas fa-mobile-alt"></i> Caméra</a>
            <a href="/qi-manager.html" class="nav-btn"><i class="fas fa-brain"></i> QI</a>
            <a href="/thermal-memory-dashboard.html" class="nav-btn"><i class="fas fa-fire"></i> Mémoire</a>
            <a href="/chat.html" class="nav-btn"><i class="fas fa-comments"></i> Chat</a>
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
        </div>
    </div>
    <div class="main-container">
        <div class="sidebar">
            <h3 style="color: #ff69b4; margin-bottom: 15px;"><i class="fas fa-folder-open"></i> Explorateur</h3>
            <div style="padding: 8px; margin: 2px 0; border-radius: 6px; cursor: pointer; background: linear-gradient(135deg, #e91e63, #ad1457); color: white;">
                <i class="fab fa-html5"></i> index.html
            </div>
        </div>
        <div class="editor-container">
            <textarea id="codeEditor"><!DOCTYPE html>
<html>
<head>
    <title>Mon Code</title>
</head>
<body>
    <h1>Éditeur de Code Louna AI</h1>
    <p>Commencez à coder ici...</p>
</body>
</html></textarea>
        </div>
        <div class="properties-panel">
            <div class="brain-connection">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px; font-size: 14px; font-weight: 600;">
                    <div style="width: 12px; height: 12px; border-radius: 50%; background: #00ff00; animation: pulse 2s infinite;"></div>
                    Cerveau Louna Connecté
                </div>
                <div style="font-size: 12px; margin-bottom: 10px;">QI: 225 • Neurones: 145 • Connexions: 717</div>
                <button class="ai-assist-btn"><i class="fas fa-robot"></i> Assistance IA</button>
                <button class="ai-assist-btn"><i class="fas fa-magic"></i> Générer Code IA</button>
                <button class="ai-assist-btn"><i class="fas fa-rocket"></i> Optimiser Code</button>
            </div>
        </div>
    </div>
    <script>
        let editor;
        document.addEventListener('DOMContentLoaded', function() {
            editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
                mode: 'htmlmixed',
                theme: 'monokai',
                lineNumbers: true,
                autoCloseBrackets: true,
                matchBrackets: true
            });
        });
    </script>
    <style>
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
    </style>
</body>
</html>
