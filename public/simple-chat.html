<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 LOUNA AI - Chat Simple</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f0f8ff;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metrics {
            display: flex;
            gap: 15px;
            font-size: 14px;
        }

        .metric {
            background: rgba(102, 126, 234, 0.1);
            padding: 5px 10px;
            border-radius: 15px;
        }

        .chat-container {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e3f2fd;
            color: #333;
            border-left: 4px solid #2196f3;
        }

        .message-info {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
            font-style: italic;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        #messageInput {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            background: rgba(102, 126, 234, 0.1);
            color: #333;
            transition: all 0.3s;
        }

        .control-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .quick-btn:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 LOUNA AI - Assistant Intelligent</h1>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="statusText">Connexion en cours...</span>
            </div>
            <div class="metrics" id="metricsDisplay">
                <span class="metric">🧠 Neurones: --</span>
                <span class="metric">🌡️ Temp: --°C</span>
                <span class="metric">⚡ QI: --</span>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" id="voiceBtn" onclick="toggleVoice()">🎤 Vocal</button>
            <button class="control-btn" id="speakBtn" onclick="toggleSpeak()">🔊 Écouter</button>
            <button class="control-btn" id="codeBtn" onclick="toggleCode()">💻 Code</button>
            <button class="control-btn" id="autoBtn" onclick="toggleAuto()">🤖 Auto</button>
        </div>

        <div class="quick-actions">
            <button class="quick-btn" onclick="quickMessage('Bonjour ! Comment allez-vous ?')">👋 Saluer</button>
            <button class="quick-btn" onclick="quickMessage('Expliquez-moi JavaScript')">📚 Apprendre</button>
            <button class="quick-btn" onclick="quickMessage('Créez une fonction Python')">💻 Coder</button>
            <button class="quick-btn" onclick="quickMessage('Quelles sont vos capacités ?')">❓ Capacités</button>
            <button class="quick-btn" onclick="quickMessage('Montrez-moi vos métriques')">📊 Métriques</button>
        </div>

        <div id="chatContainer" class="chat-container">
            <div class="message ai-message">
                <div>👋 Bonjour ! Je suis LOUNA AI, votre assistant intelligent avec mémoire thermique vivante.</div>
                <div class="message-info">Agent: DeepSeek R1-0528 • Neurones: Génération active • Prêt à vous aider</div>
            </div>
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Posez votre question à LOUNA AI..." 
                   onkeypress="handleKeyPress(event)">
            <button id="sendBtn" class="btn" onclick="sendMessage()">Envoyer</button>
        </div>
    </div>

    <script>
        let isConnected = false;
        let isVoiceMode = false;
        let isSpeakMode = false;
        let isCodeMode = false;
        let isAutoMode = false;

        // Vérifier la connexion
        async function checkConnection() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    isConnected = true;
                    updateStatus('✅ Connecté - LOUNA AI opérationnelle');
                    updateMetrics(data.metrics);
                } else {
                    throw new Error('Serveur non disponible');
                }
            } catch (error) {
                isConnected = false;
                updateStatus('❌ Connexion échouée');
                console.error('Erreur connexion:', error);
            }
        }

        // Mettre à jour le statut
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }

        // Mettre à jour les métriques
        function updateMetrics(metrics) {
            if (!metrics) return;
            
            const display = document.getElementById('metricsDisplay');
            const neurons = metrics.brainStats?.activeNeurons || '--';
            const temp = metrics.memoryStats?.globalTemperature?.toFixed(1) || '--';
            const iq = metrics.iqAnalysis?.combinedIQ || '--';
            
            display.innerHTML = `
                <span class="metric">🧠 Neurones: ${neurons}</span>
                <span class="metric">🌡️ Temp: ${temp}°C</span>
                <span class="metric">⚡ QI: ${iq}</span>
            `;
        }

        // Envoyer un message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();
            
            if (!message) {
                alert('Veuillez saisir un message');
                return;
            }
            
            if (!isConnected) {
                alert('Connexion au serveur perdue');
                await checkConnection();
                return;
            }

            // Désactiver l'interface
            input.disabled = true;
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';

            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';

            // Afficher l'indicateur de chargement
            const loadingId = addMessage('🤔 Réflexion en cours...', 'ai', true);

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        message: message,
                        codeMode: isCodeMode,
                        autoMode: isAutoMode
                    })
                });

                const data = await response.json();
                
                // Supprimer l'indicateur de chargement
                document.getElementById(loadingId).remove();

                if (data.success) {
                    addMessage(data.response, 'ai', false, {
                        agent: data.agent,
                        neurons: data.brainStats?.activeNeurons,
                        temperature: data.memoryStats?.globalTemperature?.toFixed(1),
                        iq: data.iqAnalysis?.combinedIQ
                    });
                    
                    // Mettre à jour les métriques
                    if (data.memoryStats || data.brainStats) {
                        updateMetrics({
                            memoryStats: data.memoryStats,
                            brainStats: data.brainStats,
                            iqAnalysis: data.iqAnalysis
                        });
                    }
                } else {
                    addMessage('❌ Erreur: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('❌ Erreur de communication: ' + error.message, 'ai');
            } finally {
                // Réactiver l'interface
                input.disabled = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Envoyer';
                input.focus();
            }
        }

        // Ajouter un message au chat
        function addMessage(text, sender, isLoading = false, info = null) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            messageDiv.id = messageId;
            messageDiv.className = `message ${sender}-message`;

            let content = `<div>${text}</div>`;

            if (info && !isLoading) {
                const infoText = [
                    info.agent ? `Agent: ${info.agent}` : null,
                    info.neurons ? `Neurones: ${info.neurons}` : null,
                    info.temperature ? `Temp: ${info.temperature}°C` : null,
                    info.iq ? `QI: ${info.iq}` : null
                ].filter(Boolean).join(' • ');

                content += `<div class="message-info">${infoText}</div>`;
            }

            messageDiv.innerHTML = content;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;

            return messageId;
        }

        // Message rapide
        function quickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // Basculer les modes
        function toggleVoice() {
            isVoiceMode = !isVoiceMode;
            document.getElementById('voiceBtn').classList.toggle('active', isVoiceMode);
        }

        function toggleSpeak() {
            isSpeakMode = !isSpeakMode;
            document.getElementById('speakBtn').classList.toggle('active', isSpeakMode);
        }

        function toggleCode() {
            isCodeMode = !isCodeMode;
            document.getElementById('codeBtn').classList.toggle('active', isCodeMode);
        }

        function toggleAuto() {
            isAutoMode = !isAutoMode;
            document.getElementById('autoBtn').classList.toggle('active', isAutoMode);
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', async () => {
            await checkConnection();
            document.getElementById('messageInput').focus();
            
            // Vérifier la connexion toutes les 30 secondes
            setInterval(checkConnection, 30000);
        });
    </script>
</body>
</html>
