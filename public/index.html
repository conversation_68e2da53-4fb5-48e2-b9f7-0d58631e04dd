<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 LOUNA AI - Application Complète</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-container {
            background: rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #764ba2, #667eea);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .interface-frame {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 70vh;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .chat-interface {
            display: grid;
            grid-template-rows: 1fr auto;
            height: 60vh;
            gap: 20px;
        }

        .chat-messages {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-info {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-container {
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 0 0 15px 15px;
        }

        .input-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 20px;
            color: white;
            font-size: 16px;
            min-width: 300px;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .message-input:focus {
            border-color: #c084fc;
            box-shadow: 0 0 10px rgba(192, 132, 252, 0.3);
            outline: none;
        }

        .send-btn {
            background: linear-gradient(45deg, #c084fc, #f472b6);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(192, 132, 252, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2><i class="fas fa-brain"></i> Activité Neuronale</h2>

            <div class="activity-section">
                <h3><i class="fas fa-chart-line"></i> Indicateurs</h3>

                <div class="indicator active">
                    <span><i class="fas fa-microphone"></i> Activité orale</span>
                    <span class="status-badge active">Actif</span>
                </div>

                <div class="indicator active">
                    <span><i class="fas fa-pen"></i> Info écrite</span>
                    <span class="status-badge active">Actif</span>
                </div>

                <div class="indicator active">
                    <span><i class="fas fa-bolt"></i> Accélérateur</span>
                    <span class="status-badge active">Actif</span>
                </div>

                <div class="indicator partial">
                    <span><i class="fas fa-eye"></i> Info visuelle</span>
                    <span class="status-badge partial">Partiel</span>
                </div>
            </div>

            <div class="activity-section">
                <h3><i class="fas fa-history"></i> Historique</h3>
                <div id="conversation-history">
                    <!-- Historique dynamique -->
                </div>
            </div>

            <div class="activity-section">
                <h3><i class="fas fa-brain"></i> Animation Neuronale</h3>
                <div class="neural-animation" id="neural-container">
                    <!-- Animation neuronale -->
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1><i class="fas fa-brain"></i> Agent à Mémoire Thermique</h1>
                <div class="connection-status connected" id="connection-status">
                    <i class="fas fa-wifi"></i> Connecté
                </div>
                <div>Kyber <span id="kyber-indicator" class="status-badge active"><i class="fas fa-bolt"></i></span></div>
            </div>

            <!-- Navigation -->
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showSection('chat')">
                    <i class="fas fa-comment-dots"></i> Chat
                </div>
                <div class="nav-tab" onclick="showSection('memory')">
                    <i class="fas fa-memory"></i> Mémoire
                </div>
                <div class="nav-tab" onclick="showSection('stats')">
                    <i class="fas fa-chart-line"></i> Statistiques
                </div>
                <div class="nav-tab" onclick="showSection('code')">
                    <i class="fas fa-code"></i> Programmation
                </div>
                <div class="nav-tab" onclick="showSection('multimedia')">
                    <i class="fas fa-images"></i> Multimédia
                </div>
                <div class="nav-tab" onclick="showSection('protection')">
                    <i class="fas fa-shield-alt"></i> Protection du noyau
                </div>
                <div class="nav-tab" onclick="showSection('brain')">
                    <i class="fas fa-brain"></i> Activité Cérébrale
                </div>
            </div>

            <!-- Chat Section -->
            <div id="chat" class="content-section active">
                <div class="chat-container">
                    <div class="card">
                        <h3><i class="fas fa-comment-dots"></i> Chat Cognitif</h3>
                        <div class="messages" id="messages">
                            <div class="message ai">
                                <strong>LOUNA AI:</strong> Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider ?
                            </div>
                        </div>
                        <div class="input-container">
                            <input type="text" class="message-input" id="message-input" placeholder="Posez une question ou utilisez une commande (!help pour voir les commandes)">
                            <button class="send-button" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i> Envoyer
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-info-circle"></i> Commandes disponibles</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0;"><code>!help</code> - Affiche toutes les commandes</li>
                            <li style="margin: 8px 0;"><code>!memory</code> - Explorer la mémoire thermique</li>
                            <li style="margin: 8px 0;"><code>!stats</code> - Voir les statistiques</li>
                            <li style="margin: 8px 0;"><code>!code [langage]</code> - Expertise en programmation</li>
                            <li style="margin: 8px 0;"><code>!multimedia</code> - Générer des visuels</li>
                            <li style="margin: 8px 0;"><code>!emotion</code> - État émotionnel de l'agent</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Memory Section -->
            <div id="memory" class="content-section">
                <div class="card">
                    <h3><i class="fas fa-memory"></i> Mémoire Thermique</h3>
                    <div class="memory-search" style="margin-bottom: 20px;">
                        <input type="text" placeholder="Rechercher dans la mémoire..." style="padding: 10px; border-radius: 5px; border: 1px solid rgba(138, 43, 226, 0.5); background: rgba(26, 26, 46, 0.8); color: #fff; width: 70%;">
                        <button style="padding: 10px 15px; margin-left: 10px; background: linear-gradient(45deg, #8a2be2, #4169e1); border: none; border-radius: 5px; color: #fff; cursor: pointer;">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-layer-group"></i> Distribution des niveaux de mémoire</h3>
                    <div class="memory-levels">
                        <div class="memory-level">
                            <h4>Niveau 1 (Primaire)</h4>
                            <div class="memory-count" id="level-1-count">6</div>
                        </div>
                        <div class="memory-level">
                            <h4>Niveau 2 (Secondaire)</h4>
                            <div class="memory-count" id="level-2-count">6</div>
                        </div>
                        <div class="memory-level">
                            <h4>Niveau 3 (Tertiaire)</h4>
                            <div class="memory-count" id="level-3-count">2</div>
                        </div>
                        <div class="memory-level">
                            <h4>Niveau 4 (Quaternaire)</h4>
                            <div class="memory-count" id="level-4-count">0</div>
                        </div>
                        <div class="memory-level">
                            <h4>Niveau 5 (Quinary)</h4>
                            <div class="memory-count" id="level-5-count">0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div id="stats" class="content-section">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Neurones Actifs</div>
                        <div class="metric-value" id="neurons-count">925</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Connexions Synaptiques</div>
                        <div class="metric-value" id="synapses-count">653</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">QI Agent</div>
                        <div class="metric-value" id="qi-value">225</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Température</div>
                        <div class="metric-value" id="temperature-value">37.2°C</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Entrées Mémoire</div>
                        <div class="metric-value" id="memory-entries">1234</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Efficacité Mémoire</div>
                        <div class="metric-value" id="memory-efficiency">83.85%</div>
                    </div>
                </div>
            </div>

            <!-- Code Section -->
            <div id="code" class="content-section">
                <div class="card">
                    <h3><i class="fas fa-code"></i> Générateur de Code</h3>
                    <div style="margin-bottom: 20px;">
                        <input type="text" id="code-prompt" placeholder="Décrivez le code à générer..." style="width: 70%; padding: 10px; border-radius: 5px; border: 1px solid rgba(138, 43, 226, 0.5); background: rgba(26, 26, 46, 0.8); color: #fff;">
                        <select id="code-language" style="margin-left: 10px; padding: 10px; border-radius: 5px; border: 1px solid rgba(138, 43, 226, 0.5); background: rgba(26, 26, 46, 0.8); color: #fff;">
                            <option value="javascript">JavaScript</option>
                            <option value="python">Python</option>
                            <option value="html">HTML</option>
                        </select>
                        <button onclick="generateCode()" style="margin-left: 10px; padding: 10px 15px; background: linear-gradient(45deg, #8a2be2, #4169e1); border: none; border-radius: 5px; color: #fff; cursor: pointer;">
                            <i class="fas fa-magic"></i> Générer
                        </button>
                    </div>
                    <div id="generated-code" style="background: rgba(0, 0, 0, 0.5); padding: 15px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; min-height: 200px;">
                        // Le code généré apparaîtra ici...
                    </div>
                </div>
            </div>

            <!-- Multimedia Section -->
            <div id="multimedia" class="content-section">
                <div class="card">
                    <h3><i class="fas fa-images"></i> Génération Multimédia</h3>
                    <p>Fonctionnalités multimédia en développement...</p>
                </div>
            </div>

            <!-- Protection Section -->
            <div id="protection" class="content-section">
                <div class="card">
                    <h3><i class="fas fa-shield-alt"></i> Protection du Noyau</h3>
                    <p>Système de protection actif. Toutes les fonctions critiques sont sécurisées.</p>
                </div>
            </div>

            <!-- Brain Activity Section -->
            <div id="brain" class="content-section">
                <div class="card">
                    <h3><i class="fas fa-brain"></i> Activité Cérébrale</h3>
                    <div class="neural-animation" style="height: 300px;">
                        <!-- Animation cérébrale avancée -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentSection = 'chat';
        let metrics = {
            neurons: 925,
            synapses: 653,
            qi: 225,
            temperature: 37.2,
            memoryEntries: 1234,
            memoryEfficiency: 83.85
        };

        // Fonction pour changer de section
        function showSection(sectionName) {
            // Masquer toutes les sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Désactiver tous les onglets
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Afficher la section sélectionnée
            document.getElementById(sectionName).classList.add('active');

            // Activer l'onglet correspondant
            event.target.classList.add('active');

            currentSection = sectionName;
        }

        // Fonction pour envoyer un message
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (!message) return;

            // Ajouter le message utilisateur
            addMessage('user', message);
            input.value = '';

            // Simuler une réponse de l'IA
            setTimeout(() => {
                let response = generateAIResponse(message);
                addMessage('ai', response);
            }, 1000);
        }

        // Fonction pour ajouter un message au chat
        function addMessage(sender, text) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>Vous:</strong> ${text}`;
            } else {
                messageDiv.innerHTML = `<strong>LOUNA AI:</strong> ${text}`;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Fonction pour générer une réponse IA
        function generateAIResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('!help')) {
                return `🧠 Commandes disponibles:
                - !memory : Explorer la mémoire thermique
                - !stats : Voir les statistiques
                - !code [langage] : Générer du code
                - !multimedia : Fonctions multimédia
                - !emotion : État émotionnel`;
            }

            if (lowerMessage.includes('!stats')) {
                return `📊 Statistiques actuelles:
                🧠 Neurones: ${metrics.neurons}
                🔗 Synapses: ${metrics.synapses}
                🎯 QI: ${metrics.qi}
                🌡️ Température: ${metrics.temperature}°C
                💾 Mémoire: ${metrics.memoryEntries} entrées (${metrics.memoryEfficiency}% efficacité)`;
            }

            if (lowerMessage.includes('!memory')) {
                return `🧠 Mémoire thermique active avec ${metrics.memoryEntries} entrées réparties sur 6 niveaux. Efficacité: ${metrics.memoryEfficiency}%. Température optimale maintenue à ${metrics.temperature}°C.`;
            }

            if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut')) {
                return `🧠 Bonjour ! Je suis LOUNA AI avec ${metrics.neurons} neurones actifs et un QI de ${metrics.qi}. Comment puis-je vous aider aujourd'hui ?`;
            }

            if (lowerMessage.includes('code') || lowerMessage.includes('programmer')) {
                return `💻 Je peux générer du code dans plusieurs langages ! Utilisez la section Programmation ou tapez !code [langage] pour commencer.`;
            }

            // Réponse par défaut
            return `🧠 J'ai analysé votre message "${message}" avec mes ${metrics.neurons} neurones. Ma température cérébrale est de ${metrics.temperature}°C. Comment puis-je vous aider ?`;
        }

        // Fonction pour générer du code
        function generateCode() {
            const prompt = document.getElementById('code-prompt').value;
            const language = document.getElementById('code-language').value;
            const codeContainer = document.getElementById('generated-code');

            if (!prompt) {
                codeContainer.textContent = '// Veuillez entrer une description du code à générer...';
                return;
            }

            // Simuler la génération de code
            let generatedCode = '';

            switch (language) {
                case 'javascript':
                    generatedCode = `// Code JavaScript généré par LOUNA AI
// Prompt: ${prompt}
// Neurones actifs: ${metrics.neurons}

function ${prompt.replace(/[^a-zA-Z]/g, '')}() {
    console.log("Code généré par LOUNA AI !");
    console.log("Neurones actifs: ${metrics.neurons}");
    console.log("Température: ${metrics.temperature}°C");

    // TODO: Implémenter ${prompt}
    return {
        status: "Fonction créée par LOUNA AI",
        neurons: ${metrics.neurons},
        temperature: ${metrics.temperature}
    };
}

// Utilisation
${prompt.replace(/[^a-zA-Z]/g, '')}();`;
                    break;

                case 'python':
                    generatedCode = `# Code Python généré par LOUNA AI
# Prompt: ${prompt}
# Neurones actifs: ${metrics.neurons}

def ${prompt.replace(/[^a-zA-Z]/g, '_').toLowerCase()}():
    print("Code généré par LOUNA AI !")
    print(f"Neurones actifs: ${metrics.neurons}")
    print(f"Température: ${metrics.temperature}°C")

    # TODO: Implémenter ${prompt}
    return {
        "status": "Fonction créée par LOUNA AI",
        "neurons": ${metrics.neurons},
        "temperature": ${metrics.temperature}
    }

# Utilisation
${prompt.replace(/[^a-zA-Z]/g, '_').toLowerCase()}()`;
                    break;

                case 'html':
                    generatedCode = `<!-- Code HTML généré par LOUNA AI -->
<!-- Prompt: ${prompt} -->
<!-- Neurones actifs: ${metrics.neurons} -->

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Généré par LOUNA AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
        }
    </style>
</head>
<body>
    <h1>Code généré par LOUNA AI</h1>
    <p>Prompt: ${prompt}</p>
    <p>Neurones actifs: ${metrics.neurons}</p>
    <p>Température: ${metrics.temperature}°C</p>

    <!-- TODO: Implémenter ${prompt} -->
</body>
</html>`;
                    break;
            }

            codeContainer.textContent = generatedCode;
        }

        // Fonction pour mettre à jour les métriques
        function updateMetrics() {
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        metrics.neurons = data.neurons || metrics.neurons;
                        metrics.synapses = data.synapses || metrics.synapses;
                        metrics.qi = data.qi?.agentIQ || metrics.qi;
                        metrics.temperature = data.temperature || metrics.temperature;
                        metrics.memoryEntries = data.memoryEntries || metrics.memoryEntries;
                        metrics.memoryEfficiency = data.memoryEfficiency || metrics.memoryEfficiency;

                        // Mettre à jour l'affichage
                        document.getElementById('neurons-count').textContent = metrics.neurons;
                        document.getElementById('synapses-count').textContent = metrics.synapses;
                        document.getElementById('qi-value').textContent = metrics.qi;
                        document.getElementById('temperature-value').textContent = metrics.temperature + '°C';
                        document.getElementById('memory-entries').textContent = metrics.memoryEntries;
                        document.getElementById('memory-efficiency').textContent = metrics.memoryEfficiency + '%';
                    }
                })
                .catch(error => {
                    console.log('Erreur lors de la récupération des métriques:', error);
                });
        }

        // Fonction pour créer l'animation neuronale
        function createNeuralAnimation() {
            const container = document.getElementById('neural-container');

            // Créer des neurones animés
            for (let i = 0; i < 20; i++) {
                const neuron = document.createElement('div');
                neuron.className = 'neuron';
                neuron.style.left = Math.random() * 100 + '%';
                neuron.style.top = Math.random() * 100 + '%';
                neuron.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(neuron);
            }
        }

        // Gestion de l'entrée dans le chat
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            createNeuralAnimation();
            updateMetrics();

            // Mettre à jour les métriques toutes les 5 secondes
            setInterval(updateMetrics, 5000);

            console.log('🧠 LOUNA AI Interface chargée avec succès !');
        });
    </script>
</body>
</html>






                <h3><i class="fas fa-brain"></i> Activité neuronale avancée</h3>
                <div class="grid-2">
                    <div class="card">
                        <h3><i class="fas fa-network-wired"></i> Réseau neuronal</h3>
                        <div class="neural-visualization"></div>
                        <div class="flex justify-between items-center">
                            <div class="badge primary"><i class="fas fa-circle-nodes"></i> 30 nœuds</div>
                            <div class="badge info"><i class="fas fa-bolt"></i> Activité: Élevée</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-wave-square"></i> Ondes cérébrales</h3>
                        <div class="brain-activity-chart">
                            <div class="brain-wave">
                                <div class="wave-line"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="badge success"><i class="fas fa-check-circle"></i> Synchronisé</div>
                            <div class="badge warning"><i class="fas fa-chart-line"></i> Fréquence: 12.4 Hz</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-project-diagram"></i> Visualisation 3D</h3>
                    <div class="neural-container">
                        <div class="neural-background"></div>
                        <div class="neural-smoke"></div>
                        <div class="neural-fluid"></div>
                        <div class="neural-activity neural-activity-working" id="neural-activity"></div>

                        <!-- Effets électriques -->
                        <div class="neural-spark" style="top: 30%; left: 20%;"></div>
                        <div class="neural-spark" style="top: 60%; left: 70%;"></div>
                        <div class="neural-spark" style="top: 40%; left: 50%;"></div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="badge info"><i class="fas fa-sync-alt"></i> Mise à jour en temps réel</div>
                        <div class="badge accent"><i class="fas fa-tachometer-alt"></i> Performance: Optimale</div>
                    </div>
                </div>
            </section>

            <!-- Section Statistiques -->
            <section id="stats">
                <h2><i class="fas fa-chart-line"></i> Statistiques et Analyse</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Distribution des niveaux</h3>
                        <div class="chart-container">
                            <canvas id="levels-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>Température par niveau</h3>
                        <div class="chart-container">
                            <canvas id="temperature-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card card">
                        <h3><i class="fas fa-bolt"></i> Accélérateur Kyber</h3>
                        <div class="kyber-stats-container">
                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                                <div class="kyber-stat-title">Facteur d'accélération</div>
                                <div class="kyber-stat-value accent" id="kyber-acceleration">-</div>
                                <div class="kyber-stat-subtitle">Multiplicateur de performance</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="kyber-stat-title">Opérations/seconde</div>
                                <div class="kyber-stat-value info" id="kyber-ops">-</div>
                                <div class="kyber-stat-subtitle">Traitement en temps réel</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-temperature-high"></i>
                                </div>
                                <div class="kyber-stat-title">Température</div>
                                <div class="kyber-stat-value success" id="kyber-temperature">-</div>
                                <div class="kyber-stat-subtitle">Stabilité thermique</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="kyber-stat-title">Utilisation</div>
                                <div class="kyber-stat-value warning" id="kyber-utilization">-</div>
                                <div class="kyber-stat-subtitle">Capacité utilisée</div>
                            </div>
                        </div>

                        <div class="gauge-container">
                            <div class="gauge" data-type="acceleration" data-value="50">
                                <div class="gauge-background"></div>
                                <div class="gauge-fill"></div>
                                <div class="gauge-center">
                                    <div class="gauge-value">1.5</div>
                                    <div class="gauge-label">Facteur</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques Kyber -->
                <div class="stats-container">
                    <h3>Statistiques de performance</h3>
                    <div class="stats-content">
                        <div class="stats-metric">
                            <span class="metric-label">Facteur d'accélération</span>
                            <span class="metric-value" id="kyber-factor">1.0</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Température</span>
                            <span class="metric-value" id="kyber-temp">25.0°C</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Score de performance</span>
                            <span class="metric-value" id="kyber-score">80/100</span>
                        </div>
                    </div>

                    <!-- Graphiques de température et distribution -->
                    <h3>Température par niveau</h3>
                    <div class="temperature-chart-container">
                        <div class="chart-particles"></div>
                        <canvas id="temperature-level-chart"></canvas>
                        <div class="temperature-gradient"></div>
                    </div>

                    <h3>Distribution d'activité</h3>
                    <div class="chart-container">
                        <canvas id="activity-distribution-chart"></canvas>
                    </div>

                    <h3>Évolution de la mémoire</h3>
                    <div class="chart-container">
                        <canvas id="memory-evolution-chart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Section Multimédia -->
            <section id="multimedia">
                <h2><i class="fas fa-images"></i> Génération Multimédia</h2>

                <div class="multimedia-container">
                    <div class="multimedia-controls">
                        <div class="form-group">
                            <label for="multimedia-type">Type de contenu</label>
                            <select id="multimedia-type">
                                <option value="chart">Graphique</option>
                                <option value="image">Image avec texte</option>
                                <option value="art">Art abstrait</option>
                            </select>
                        </div>

                        <div id="chart-options" class="media-options">
                            <div class="form-group">
                                <label for="chart-type">Type de graphique</label>
                                <select id="chart-type">
                                    <option value="bar">Barres</option>
                                    <option value="line">Lignes</option>
                                    <option value="pie">Camembert</option>
                                    <option value="radar">Radar</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="chart-data">Données (séparées par des virgules)</label>
                                <input type="text" id="chart-data" placeholder="10, 20, 30, 40, 50">
                            </div>

                            <div class="form-group">
                                <label for="chart-labels">Étiquettes (séparées par des virgules)</label>
                                <input type="text" id="chart-labels" placeholder="A, B, C, D, E">
                            </div>
                        </div>

                        <div id="image-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="image-text">Texte à afficher</label>
                                <input type="text" id="image-text" placeholder="Bonjour, Monde !">
                            </div>

                            <div class="form-group">
                                <label for="image-bg-color">Couleur d'arrière-plan</label>
                                <input type="color" id="image-bg-color" value="#f5f5f5">
                            </div>

                            <div class="form-group">
                                <label for="image-text-color">Couleur du texte</label>
                                <input type="color" id="image-text-color" value="#333333">
                            </div>
                        </div>

                        <div id="art-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="art-complexity">Complexité</label>
                                <input type="range" id="art-complexity" min="1" max="10" value="5">
                            </div>

                            <div class="form-group">
                                <label for="art-colors">Palette de couleurs</label>
                                <select id="art-colors">
                                    <option value="vibrant">Vibrante</option>
                                    <option value="pastel">Pastel</option>
                                    <option value="monochrome">Monochrome</option>
                                    <option value="autumn">Automne</option>
                                    <option value="ocean">Océan</option>
                                </select>
                            </div>
                        </div>

                        <button id="generate-media-btn" class="btn primary"><i class="fas fa-magic"></i> Générer</button>
                    </div>

                    <div class="multimedia-preview">
                        <h3>Aperçu</h3>
                        <div id="media-output"></div>
                    </div>
                </div>
            </section>

            <!-- Section Code -->
            <section id="code">
                <h2><i class="fas fa-code"></i> Assistant de Programmation</h2>

                <div class="code-container">
                    <div class="code-editor">
                        <div class="form-group">
                            <label for="code-language">Langage</label>
                            <select id="code-language">
                                <option value="python">Python</option>
                                <option value="javascript">JavaScript</option>
                                <option value="java">Java</option>
                                <option value="cpp">C++</option>
                                <option value="csharp">C#</option>
                                <option value="php">PHP</option>
                                <option value="ruby">Ruby</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                                <option value="swift">Swift</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="code-input">Code ou description</label>
                            <textarea id="code-input" class="code-textarea" placeholder="Entrez votre code à analyser ou décrivez ce que vous voulez générer..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="code-action">Action</label>
                            <select id="code-action">
                                <option value="explain">Expliquer</option>
                                <option value="generate">Générer</option>
                                <option value="optimize">Optimiser</option>
                                <option value="debug">Déboguer</option>
                                <option value="convert">Convertir</option>
                            </select>
                        </div>

                        <button id="process-code-btn" class="btn primary"><i class="fas fa-terminal"></i> Traiter</button>
                    </div>

                    <div class="code-output">
                        <h3>Résultat</h3>
                        <pre id="code-result" class="code-block">// Le résultat apparaîtra ici</pre>
                    </div>
                </div>
            </section>

            <!-- Section Systèmes Externes -->
            <section id="external">
                <h2><i class="fas fa-sitemap"></i> Systèmes Externes</h2>

                <div class="external-systems-container">
                    <div class="systems-info">
                        <h3>Systèmes connectés</h3>
                        <div id="connected-systems-list" class="systems-list">
                            <!-- Liste des systèmes connectés (remplie dynamiquement) -->
                        </div>

                        <div class="system-details" id="system-details">
                            <h3>Détails du système</h3>
                            <div id="system-details-content">
                                <p>Sélectionnez un système pour voir les détails.</p>
                            </div>
                        </div>
                    </div>

                    <div class="add-system-form">
                        <h3>Connecter un nouveau système</h3>
                        <div class="form-group">
                            <label for="system-type">Type de système</label>
                            <select id="system-type">
                                <option value="api">API / Service Web</option>
                                <option value="file">Système de fichiers</option>
                                <option value="database">Base de données</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="system-id">Identifiant unique</label>
                            <input type="text" id="system-id" placeholder="ex: github-repos">
                        </div>

                        <div class="form-group">
                            <label for="system-config">Configuration (JSON)</label>
                            <textarea id="system-config" placeholder='{"endpoint": "https://api.example.com"}'></textarea>
                        </div>

                        <button id="connect-system-btn" class="btn primary"><i class="fas fa-plug"></i> Connecter</button>
                    </div>

                    <div class="learning-status">
                        <h3>Statistiques d'apprentissage</h3>
                        <div class="learning-metrics">
                            <div class="metric">
                                <span class="metric-label">Systèmes connectés</span>
                                <span class="metric-value" id="connected-systems-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Données traitées</span>
                                <span class="metric-value" id="processed-data-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Taux d'apprentissage</span>
                                <span class="metric-value" id="learning-rate">0%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Dernier apprentissage</span>
                                <span class="metric-value" id="last-learning-time">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Activité Cérébrale -->
            <section id="brain-activity">
                <h2><i class="fas fa-brain"></i> Activité Cérébrale</h2>

                <div class="brain-container">
                    <div class="brain-visualization" id="brain-visualization">
                        <!-- Conteneur pour l'animation neuronale avancée -->
                        <div id="neural-container-large" class="neural-container-large"></div>
                    </div>

                    <div class="brain-controls">
                        <h3>Contrôle de l'activité</h3>

                        <div class="form-group">
                            <label for="activity-level">Niveau d'activité</label>
                            <input type="range" id="activity-level" min="0.1" max="1" step="0.1" value="0.5">
                            <span id="activity-level-value">50%</span>
                        </div>

                        <div class="form-group">
                            <label for="mode-selector">Mode de fonctionnement</label>
                            <select id="mode-selector">
                                <option value="normal">Normal</option>
                                <option value="veille">Veille</option>
                                <option value="apprentissage">Apprentissage intensif</option>
                                <option value="economie">Économie d'énergie</option>
                            </select>
                        </div>

                        <div class="brain-metrics">
                            <h3>Métriques en temps réel</h3>
                            <div class="metrics-container">
                                <div class="metric">
                                    <span class="metric-label">Utilisation mémoire</span>
                                    <span class="metric-value" id="memory-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Charge CPU</span>
                                    <span class="metric-value" id="cpu-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Connexions synaptiques</span>
                                    <span class="metric-value" id="synaptic-connections">0</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">État émotionnel</span>
                                    <span class="metric-value" id="emotional-state">Neutre</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>Agent à Mémoire Thermique &copy; 2025 | <i class="fas fa-thermometer-half"></i> Système de mémoire thermique avec accélérateur Kyber</p>
            <div class="system-stats">
                <span id="kyber-temp">Kyber: <span id="kyber-temperature">45.2°C</span></span> |
                <span id="memory-stat">Mémoire: <span id="memory-utilization">42%</span></span> |
                <span id="acceleration-factor">Accélération: <span id="kyber-acceleration">1.8x</span></span>
            </div>
        </footer>
    </div>

    <!-- Animation neuronale en arrière-plan pour tout le site -->
    <div id="neural-container" class="neural-background"></div>

    <!-- Chart.js pour les graphiques statistiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Contrôleur d'animations (chargé en premier pour désactiver les animations bloquantes) -->
    <script src="/js/animation-controller.js"></script>

    <!-- Gestionnaire de fallback API (chargé avant les scripts principaux pour éviter le mode limité) -->
    <script src="/js/api-fallback-manager.js"></script>

    <!-- Scripts principaux -->
    <script src="/js/simple-app.js"></script>
    <script src="/js/memory-manager.js"></script>
    <script src="/js/kyber-module.js"></script>
    <script src="/js/voice-interface.js"></script>
    <script src="/js/human-language-trainer.js"></script>
    <script src="/js/keyboard-shortcuts.js"></script>
    <script src="/js/code-explorer.js"></script>
    <script src="/js/file-loader.js"></script>
    <script src="/js/vscode-integration.js"></script>
    <script src="/js/vscode-integration-file-operations.js"></script>
    <script src="/js/auto-paradigm-initializer.js"></script>
    <!-- Scripts d'animation (chargés après le contrôleur pour respecter les paramètres) -->
    <script src="/js/neural-animation.js"></script>
    <script src="/js/neural-animation-enhanced.js"></script>
    <script src="/js/particle-animation.js"></script>
    <script src="/js/animated-stats-charts.js"></script>
    <script src="/js/sensory-memory-manager.js"></script>
    <script src="/js/sidebar-manager.js"></script>
    <script src="/js/enhanced-visualizations.js"></script>
    <script src="/js/neural-activity-fix.js"></script>
    <script src="/js/brain-activity-injector.js"></script>
    <script src="/js/simple-app.js"></script>
            </div>
        </div>
    </div>
</body>
</html>
