<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent à Mémoire Thermique</title>
    <!-- Styles principaux -->
    <link rel="stylesheet" href="/css/reset.css">
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/chat.css">
    <link rel="stylesheet" href="/css/code-display.css">
    <link rel="stylesheet" href="/css/neural-animations.css">
    <link rel="stylesheet" href="/css/graphs.css">
    <link rel="stylesheet" href="/css/enhanced-neural-animation.css">
    <link rel="stylesheet" href="/css/file-loader.css">
    <link rel="stylesheet" href="/css/chart-animations.css">
    <link rel="stylesheet" href="/css/compatibility-mode.css">
    <link rel="stylesheet" href="/css/user-interface.css">
    <link rel="stylesheet" href="/css/particle-animation.css">

    <!-- Nouveaux styles futuristes -->
    <link rel="stylesheet" href="/css/futuristic-interface.css">
    <link rel="stylesheet" href="/css/modern-components.css">
    <link rel="stylesheet" href="/css/advanced-visualizations.css">

    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Socket.IO compatible avec Flask-SocketIO -->
    <script src="https://cdn.socket.io/4.6.1/socket.io.min.js"></script>
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- Scripts pour l'interface utilisateur -->
    <script src="/js/disable-dev-mode.js"></script>
    <script src="/js/force-ui-display.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <!-- Barre latérale -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-brain"></i> Activité Neuronale</h3>
            </div>

            <!-- Indicateurs d'activité -->
            <div class="sidebar-section">
                <h4><i class="fas fa-chart-line"></i> Indicateurs</h4>
                <div class="activity-indicators">
                    <div class="activity-indicator oral-indicator">
                        <div class="indicator-name"><i class="fas fa-microphone"></i> Activité orale</div>
                        <div class="indicator-value active" id="oral-activity-value">Actif</div>
                    </div>
                    <div class="activity-indicator written-indicator">
                        <div class="indicator-name"><i class="fas fa-pen"></i> Info écrite</div>
                        <div class="indicator-value active" id="written-activity-value">Actif</div>
                    </div>
                    <div class="activity-indicator accelerator-indicator">
                        <div class="indicator-name"><i class="fas fa-bolt"></i> Accélérateur</div>
                        <div class="indicator-value active" id="accelerator-status-value">Actif</div>
                    </div>
                    <div class="activity-indicator visual-indicator">
                        <div class="indicator-name"><i class="fas fa-eye"></i> Info visuelle</div>
                        <div class="indicator-value medium" id="visual-activity-value">Partiel</div>
                    </div>
                </div>
            </div>

            <!-- Historique des conversations -->
            <div class="sidebar-section">
                <h4><i class="fas fa-history"></i> Historique</h4>
                <div class="conversation-history" id="conversation-history">
                    <!-- Rempli dynamiquement -->
                </div>
            </div>

            <!-- Animation neuronale occupant tout l'espace restant -->
            <div class="neural-activity-sidebar" id="neural-container">
                <!-- Animation neuronale améliorée -->
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="app-container">
        <header>
            <div class="logo">
                <i class="fas fa-brain"></i>
                <h1>Agent à Mémoire Thermique</h1>
            </div>
            <div id="app-status" class="status disconnected">Déconnecté</div>
            <div class="controls">
                <a href="/language-training.html" class="control-button" title="Formation Linguistique">
                    <i class="fas fa-language"></i>
                </a>
            </div>
            <div class="kyber-status">
                <span id="kyber-indicator" class="status-indicator disabled"></span>
                <span id="kyber-label">Kyber</span>
                <button id="kyber-boost" class="kyber-boost-btn" title="Activer le boost Kyber"><i class="fas fa-bolt"></i></button>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#chat" id="nav-chat" class="active"><i class="fas fa-comment-dots"></i> Chat</a></li>
                <li><a href="#memory" id="nav-memory"><i class="fas fa-memory"></i> Mémoire</a></li>
                <li><a href="#stats" id="nav-stats"><i class="fas fa-chart-line"></i> Statistiques</a></li>
                <li><a href="#multimedia" id="nav-multimedia"><i class="fas fa-images"></i> Multimédia</a></li>
                <li><a href="#code" id="nav-code"><i class="fas fa-code"></i> Programmation</a></li>
                <li><a href="#external" id="nav-external"><i class="fas fa-sitemap"></i> Systèmes Externes</a></li>
                <li><a href="/core-protection.html" id="nav-protection"><i class="fas fa-shield-alt"></i> Protection du noyau</a></li>
                <li><a href="brain-activity.html" id="nav-brain"><i class="fas fa-brain"></i> Activité Cérébrale</a></li>
            </ul>
        </nav>

        <main>
            <!-- Section Chat -->
            <section id="chat" class="active">
                <div class="conversation-container">
                    <div id="message-list" class="messages"></div>

                    <div id="loading-indicator" class="loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Agent en réflexion...</div>
                    </div>

                    <div class="input-container">
                        <textarea id="message-input" placeholder="Posez une question ou utilisez une commande (!help pour voir les commandes disponibles)"></textarea>
                        <button id="send-button"><i class="fas fa-paper-plane"></i> Envoyer</button>
                    </div>

                    <div class="commands-info">
                        <p><i class="fas fa-info-circle"></i> Commandes disponibles:</p>
                        <ul>
                            <li><code>!help</code> - Affiche toutes les commandes</li>
                            <li><code>!memory</code> - Explorer la mémoire thermique</li>
                            <li><code>!stats</code> - Voir les statistiques</li>
                            <li><code>!code [langage]</code> - Expertise en programmation</li>
                            <li><code>!multimedia</code> - Générer des visuels</li>
                            <li><code>!emotion</code> - État émotionnel de l'agent</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Section Mémoire -->
            <section id="memory">
                <h2><i class="fas fa-memory"></i> Mémoire Thermique</h2>

                <div class="memory-search">
                    <input type="text" id="memory-search-input" placeholder="Rechercher dans la mémoire...">
                    <button id="memory-search-btn"><i class="fas fa-search"></i> Rechercher</button>
                </div>

                <div class="card">
                    <h3><i class="fas fa-layer-group"></i> Distribution des niveaux de mémoire</h3>

                    <div class="memory-level-indicator">
                        <div class="memory-level-name">Niveau 1 (Primaire)</div>
                        <div class="memory-level-bar">
                            <div class="memory-level-fill" data-level="1" style="width: 60%;"></div>
                        </div>
                        <div class="memory-level-value" data-level="1">6</div>
                    </div>

                    <div class="memory-level-indicator">
                        <div class="memory-level-name">Niveau 2 (Secondaire)</div>
                        <div class="memory-level-bar">
                            <div class="memory-level-fill" data-level="2" style="width: 60%;"></div>
                        </div>
                        <div class="memory-level-value" data-level="2">6</div>
                    </div>

                    <div class="memory-level-indicator">
                        <div class="memory-level-name">Niveau 3 (Tertiaire)</div>
                        <div class="memory-level-bar">
                            <div class="memory-level-fill" data-level="3" style="width: 20%;"></div>
                        </div>
                        <div class="memory-level-value" data-level="3">2</div>
                    </div>

                    <div class="memory-level-indicator">
                        <div class="memory-level-name">Niveau 4 (Quaternaire)</div>
                        <div class="memory-level-bar">
                            <div class="memory-level-fill" data-level="4" style="width: 0%;"></div>
                        </div>
                        <div class="memory-level-value" data-level="4">0</div>
                    </div>

                    <div class="memory-level-indicator">
                        <div class="memory-level-name">Niveau 5 (Quinary)</div>
                        <div class="memory-level-bar">
                            <div class="memory-level-fill" data-level="5" style="width: 0%;"></div>
                        </div>
                        <div class="memory-level-value" data-level="5">0</div>
                    </div>
                </div>

                <div class="memory-levels">
                    <div class="memory-level card" id="level-1">
                        <h3><i class="fas fa-brain"></i> Niveau 1 (Primaire)</h3>
                        <ul id="level-1-list" class="memory-list"></ul>
                    </div>

                    <div class="memory-level card" id="level-2">
                        <h3><i class="fas fa-brain"></i> Niveau 2 (Secondaire)</h3>
                        <ul id="level-2-list" class="memory-list"></ul>
                    </div>

                    <div class="memory-level card" id="level-3">
                        <h3><i class="fas fa-brain"></i> Niveau 3 (Tertiaire)</h3>
                        <ul id="level-3-list" class="memory-list"></ul>
                    </div>

                    <div class="memory-level card" id="level-4">
                        <h3><i class="fas fa-brain"></i> Niveau 4 (Quaternaire)</h3>
                        <ul id="level-4-list" class="memory-list"></ul>
                    </div>

                    <div class="memory-level card" id="level-5">
                        <h3><i class="fas fa-brain"></i> Niveau 5 (Quinary)</h3>
                        <ul id="level-5-list" class="memory-list"></ul>
                    </div>

                    <div class="memory-level" id="level-6">
                        <h3>Niveau 6 (Sensoriel & Personnalité)</h3>
                        <div class="sensory-tabs">
                            <button class="sensory-tab active" data-target="speech">Parole</button>
                            <button class="sensory-tab" data-target="laughter">Rire</button>
                            <button class="sensory-tab" data-target="vision">Vision</button>
                            <button class="sensory-tab" data-target="personality">Personnalité</button>
                        </div>
                        <div class="sensory-content">
                            <div id="speech-content" class="sensory-panel active">
                                <h4><i class="fas fa-microphone"></i> Parole</h4>
                                <div class="speech-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Mots reconnus:</span>
                                        <span class="metric-value" id="speech-words">2,487</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Précision:</span>
                                        <span class="metric-value" id="speech-accuracy">92%</span>
                                    </div>
                                </div>
                                <div class="speech-history">
                                    <h5>Historique vocal récent</h5>
                                    <ul id="speech-history-list" class="memory-list"></ul>
                                </div>
                            </div>
                            <div id="laughter-content" class="sensory-panel">
                                <h4><i class="fas fa-laugh"></i> Rire</h4>
                                <div class="laughter-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Rires détectés:</span>
                                        <span class="metric-value" id="laughter-count">37</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Intensité moyenne:</span>
                                        <span class="metric-value" id="laughter-intensity">Modérée</span>
                                    </div>
                                </div>
                                <div class="laughter-chart">
                                    <h5>Répartition des types de rire</h5>
                                    <canvas id="laughter-types-chart"></canvas>
                                </div>
                            </div>
                            <div id="vision-content" class="sensory-panel">
                                <h4><i class="fas fa-eye"></i> Vision</h4>
                                <div class="vision-gallery">
                                    <div class="empty-state">Aucune image analysée récemment</div>
                                </div>
                                <div class="vision-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Objets reconnus:</span>
                                        <span class="metric-value" id="vision-objects">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Précision:</span>
                                        <span class="metric-value" id="vision-accuracy">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div id="personality-content" class="sensory-panel">
                                <h4><i class="fas fa-user"></i> Personnalité</h4>
                                <div class="personality-traits">
                                    <div class="trait-item">
                                        <span class="trait-name">Savant</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:85%"></div>
                                        </div>
                                        <span class="trait-value">85%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Positif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:92%"></div>
                                        </div>
                                        <span class="trait-value">92%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Créatif</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:78%"></div>
                                        </div>
                                        <span class="trait-value">78%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Analytique</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:88%"></div>
                                        </div>
                                        <span class="trait-value">88%</span>
                                    </div>
                                    <div class="trait-item">
                                        <span class="trait-name">Social</span>
                                        <div class="trait-bar">
                                            <div class="trait-level" style="width:75%"></div>
                                        </div>
                                        <span class="trait-value">75%</span>
                                    </div>
                                </div>
                                <div class="personality-evolution">
                                    <h5>Évolution de la personnalité</h5>
                                    <div class="evolution-chart">
                                        <canvas id="personality-evolution-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="memory-details" id="memory-details" style="display: none;">
                    <h3>Détails de la mémoire</h3>
                    <p><strong>Clé:</strong> <span id="memory-details-key"></span></p>
                    <p><strong>Valeur:</strong> <span id="memory-details-value"></span></p>
                    <p><strong>Importance:</strong> <span id="memory-details-importance"></span></p>
                    <p><strong>Niveau:</strong> <span id="memory-details-level"></span></p>
                    <p><strong>Chaleur:</strong> <span id="memory-details-heat"></span></p>
                    <p><strong>Accès:</strong> <span id="memory-details-access-count"></span></p>
                    <p><strong>Créé le:</strong> <span id="memory-details-created"></span></p>
                    <p><strong>Dernier accès:</strong> <span id="memory-details-accessed"></span></p>
                </div>

                <div class="actions">
                    <button id="circulate-btn" class="btn primary"><i class="fas fa-sync"></i> Faire circuler les mémoires</button>
                    <button id="cleanup-btn" class="btn warning"><i class="fas fa-broom"></i> Nettoyer la mémoire</button>

                    <!-- Contrôle du gardien de mémoire -->
                    <div class="memory-guardian-control">
                        <div class="guardian-status">
                            <span>Gardien de mémoire:</span>
                            <span id="guardian-status" class="status-badge active">Actif</span>
                        </div>
                        <button id="toggle-guardian-btn" class="btn danger" onclick="toggleMemoryGuardian()">
                            <i class="fas fa-shield-alt"></i> Désactiver le gardien
                        </button>
                        <div class="guardian-info">
                            <i class="fas fa-info-circle"></i>
                            <span>Le gardien de mémoire protège contre les accès non autorisés. Le désactiver permet à l'agent d'accéder à toutes les zones de mémoire.</span>
                        </div>
                    </div>
                </div>

                <h3><i class="fas fa-brain"></i> Activité neuronale avancée</h3>
                <div class="grid-2">
                    <div class="card">
                        <h3><i class="fas fa-network-wired"></i> Réseau neuronal</h3>
                        <div class="neural-visualization"></div>
                        <div class="flex justify-between items-center">
                            <div class="badge primary"><i class="fas fa-circle-nodes"></i> 30 nœuds</div>
                            <div class="badge info"><i class="fas fa-bolt"></i> Activité: Élevée</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-wave-square"></i> Ondes cérébrales</h3>
                        <div class="brain-activity-chart">
                            <div class="brain-wave">
                                <div class="wave-line"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="badge success"><i class="fas fa-check-circle"></i> Synchronisé</div>
                            <div class="badge warning"><i class="fas fa-chart-line"></i> Fréquence: 12.4 Hz</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-project-diagram"></i> Visualisation 3D</h3>
                    <div class="neural-container">
                        <div class="neural-background"></div>
                        <div class="neural-smoke"></div>
                        <div class="neural-fluid"></div>
                        <div class="neural-activity neural-activity-working" id="neural-activity"></div>

                        <!-- Effets électriques -->
                        <div class="neural-spark" style="top: 30%; left: 20%;"></div>
                        <div class="neural-spark" style="top: 60%; left: 70%;"></div>
                        <div class="neural-spark" style="top: 40%; left: 50%;"></div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="badge info"><i class="fas fa-sync-alt"></i> Mise à jour en temps réel</div>
                        <div class="badge accent"><i class="fas fa-tachometer-alt"></i> Performance: Optimale</div>
                    </div>
                </div>
            </section>

            <!-- Section Statistiques -->
            <section id="stats">
                <h2><i class="fas fa-chart-line"></i> Statistiques et Analyse</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Distribution des niveaux</h3>
                        <div class="chart-container">
                            <canvas id="levels-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>Température par niveau</h3>
                        <div class="chart-container">
                            <canvas id="temperature-chart"></canvas>
                        </div>
                    </div>

                    <div class="stat-card card">
                        <h3><i class="fas fa-bolt"></i> Accélérateur Kyber</h3>
                        <div class="kyber-stats-container">
                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                                <div class="kyber-stat-title">Facteur d'accélération</div>
                                <div class="kyber-stat-value accent" id="kyber-acceleration">-</div>
                                <div class="kyber-stat-subtitle">Multiplicateur de performance</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="kyber-stat-title">Opérations/seconde</div>
                                <div class="kyber-stat-value info" id="kyber-ops">-</div>
                                <div class="kyber-stat-subtitle">Traitement en temps réel</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-temperature-high"></i>
                                </div>
                                <div class="kyber-stat-title">Température</div>
                                <div class="kyber-stat-value success" id="kyber-temperature">-</div>
                                <div class="kyber-stat-subtitle">Stabilité thermique</div>
                            </div>

                            <div class="kyber-stat-card">
                                <div class="kyber-stat-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="kyber-stat-title">Utilisation</div>
                                <div class="kyber-stat-value warning" id="kyber-utilization">-</div>
                                <div class="kyber-stat-subtitle">Capacité utilisée</div>
                            </div>
                        </div>

                        <div class="gauge-container">
                            <div class="gauge" data-type="acceleration" data-value="50">
                                <div class="gauge-background"></div>
                                <div class="gauge-fill"></div>
                                <div class="gauge-center">
                                    <div class="gauge-value">1.5</div>
                                    <div class="gauge-label">Facteur</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques Kyber -->
                <div class="stats-container">
                    <h3>Statistiques de performance</h3>
                    <div class="stats-content">
                        <div class="stats-metric">
                            <span class="metric-label">Facteur d'accélération</span>
                            <span class="metric-value" id="kyber-factor">1.0</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Température</span>
                            <span class="metric-value" id="kyber-temp">25.0°C</span>
                        </div>
                        <div class="stats-metric">
                            <span class="metric-label">Score de performance</span>
                            <span class="metric-value" id="kyber-score">80/100</span>
                        </div>
                    </div>

                    <!-- Graphiques de température et distribution -->
                    <h3>Température par niveau</h3>
                    <div class="temperature-chart-container">
                        <div class="chart-particles"></div>
                        <canvas id="temperature-level-chart"></canvas>
                        <div class="temperature-gradient"></div>
                    </div>

                    <h3>Distribution d'activité</h3>
                    <div class="chart-container">
                        <canvas id="activity-distribution-chart"></canvas>
                    </div>

                    <h3>Évolution de la mémoire</h3>
                    <div class="chart-container">
                        <canvas id="memory-evolution-chart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Section Multimédia -->
            <section id="multimedia">
                <h2><i class="fas fa-images"></i> Génération Multimédia</h2>

                <div class="multimedia-container">
                    <div class="multimedia-controls">
                        <div class="form-group">
                            <label for="multimedia-type">Type de contenu</label>
                            <select id="multimedia-type">
                                <option value="chart">Graphique</option>
                                <option value="image">Image avec texte</option>
                                <option value="art">Art abstrait</option>
                            </select>
                        </div>

                        <div id="chart-options" class="media-options">
                            <div class="form-group">
                                <label for="chart-type">Type de graphique</label>
                                <select id="chart-type">
                                    <option value="bar">Barres</option>
                                    <option value="line">Lignes</option>
                                    <option value="pie">Camembert</option>
                                    <option value="radar">Radar</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="chart-data">Données (séparées par des virgules)</label>
                                <input type="text" id="chart-data" placeholder="10, 20, 30, 40, 50">
                            </div>

                            <div class="form-group">
                                <label for="chart-labels">Étiquettes (séparées par des virgules)</label>
                                <input type="text" id="chart-labels" placeholder="A, B, C, D, E">
                            </div>
                        </div>

                        <div id="image-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="image-text">Texte à afficher</label>
                                <input type="text" id="image-text" placeholder="Bonjour, Monde !">
                            </div>

                            <div class="form-group">
                                <label for="image-bg-color">Couleur d'arrière-plan</label>
                                <input type="color" id="image-bg-color" value="#f5f5f5">
                            </div>

                            <div class="form-group">
                                <label for="image-text-color">Couleur du texte</label>
                                <input type="color" id="image-text-color" value="#333333">
                            </div>
                        </div>

                        <div id="art-options" class="media-options" style="display: none;">
                            <div class="form-group">
                                <label for="art-complexity">Complexité</label>
                                <input type="range" id="art-complexity" min="1" max="10" value="5">
                            </div>

                            <div class="form-group">
                                <label for="art-colors">Palette de couleurs</label>
                                <select id="art-colors">
                                    <option value="vibrant">Vibrante</option>
                                    <option value="pastel">Pastel</option>
                                    <option value="monochrome">Monochrome</option>
                                    <option value="autumn">Automne</option>
                                    <option value="ocean">Océan</option>
                                </select>
                            </div>
                        </div>

                        <button id="generate-media-btn" class="btn primary"><i class="fas fa-magic"></i> Générer</button>
                    </div>

                    <div class="multimedia-preview">
                        <h3>Aperçu</h3>
                        <div id="media-output"></div>
                    </div>
                </div>
            </section>

            <!-- Section Code -->
            <section id="code">
                <h2><i class="fas fa-code"></i> Assistant de Programmation</h2>

                <div class="code-container">
                    <div class="code-editor">
                        <div class="form-group">
                            <label for="code-language">Langage</label>
                            <select id="code-language">
                                <option value="python">Python</option>
                                <option value="javascript">JavaScript</option>
                                <option value="java">Java</option>
                                <option value="cpp">C++</option>
                                <option value="csharp">C#</option>
                                <option value="php">PHP</option>
                                <option value="ruby">Ruby</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                                <option value="swift">Swift</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="code-input">Code ou description</label>
                            <textarea id="code-input" class="code-textarea" placeholder="Entrez votre code à analyser ou décrivez ce que vous voulez générer..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="code-action">Action</label>
                            <select id="code-action">
                                <option value="explain">Expliquer</option>
                                <option value="generate">Générer</option>
                                <option value="optimize">Optimiser</option>
                                <option value="debug">Déboguer</option>
                                <option value="convert">Convertir</option>
                            </select>
                        </div>

                        <button id="process-code-btn" class="btn primary"><i class="fas fa-terminal"></i> Traiter</button>
                    </div>

                    <div class="code-output">
                        <h3>Résultat</h3>
                        <pre id="code-result" class="code-block">// Le résultat apparaîtra ici</pre>
                    </div>
                </div>
            </section>

            <!-- Section Systèmes Externes -->
            <section id="external">
                <h2><i class="fas fa-sitemap"></i> Systèmes Externes</h2>

                <div class="external-systems-container">
                    <div class="systems-info">
                        <h3>Systèmes connectés</h3>
                        <div id="connected-systems-list" class="systems-list">
                            <!-- Liste des systèmes connectés (remplie dynamiquement) -->
                        </div>

                        <div class="system-details" id="system-details">
                            <h3>Détails du système</h3>
                            <div id="system-details-content">
                                <p>Sélectionnez un système pour voir les détails.</p>
                            </div>
                        </div>
                    </div>

                    <div class="add-system-form">
                        <h3>Connecter un nouveau système</h3>
                        <div class="form-group">
                            <label for="system-type">Type de système</label>
                            <select id="system-type">
                                <option value="api">API / Service Web</option>
                                <option value="file">Système de fichiers</option>
                                <option value="database">Base de données</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="system-id">Identifiant unique</label>
                            <input type="text" id="system-id" placeholder="ex: github-repos">
                        </div>

                        <div class="form-group">
                            <label for="system-config">Configuration (JSON)</label>
                            <textarea id="system-config" placeholder='{"endpoint": "https://api.example.com"}'></textarea>
                        </div>

                        <button id="connect-system-btn" class="btn primary"><i class="fas fa-plug"></i> Connecter</button>
                    </div>

                    <div class="learning-status">
                        <h3>Statistiques d'apprentissage</h3>
                        <div class="learning-metrics">
                            <div class="metric">
                                <span class="metric-label">Systèmes connectés</span>
                                <span class="metric-value" id="connected-systems-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Données traitées</span>
                                <span class="metric-value" id="processed-data-count">0</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Taux d'apprentissage</span>
                                <span class="metric-value" id="learning-rate">0%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Dernier apprentissage</span>
                                <span class="metric-value" id="last-learning-time">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Activité Cérébrale -->
            <section id="brain-activity">
                <h2><i class="fas fa-brain"></i> Activité Cérébrale</h2>

                <div class="brain-container">
                    <div class="brain-visualization" id="brain-visualization">
                        <!-- Conteneur pour l'animation neuronale avancée -->
                        <div id="neural-container-large" class="neural-container-large"></div>
                    </div>

                    <div class="brain-controls">
                        <h3>Contrôle de l'activité</h3>

                        <div class="form-group">
                            <label for="activity-level">Niveau d'activité</label>
                            <input type="range" id="activity-level" min="0.1" max="1" step="0.1" value="0.5">
                            <span id="activity-level-value">50%</span>
                        </div>

                        <div class="form-group">
                            <label for="mode-selector">Mode de fonctionnement</label>
                            <select id="mode-selector">
                                <option value="normal">Normal</option>
                                <option value="veille">Veille</option>
                                <option value="apprentissage">Apprentissage intensif</option>
                                <option value="economie">Économie d'énergie</option>
                            </select>
                        </div>

                        <div class="brain-metrics">
                            <h3>Métriques en temps réel</h3>
                            <div class="metrics-container">
                                <div class="metric">
                                    <span class="metric-label">Utilisation mémoire</span>
                                    <span class="metric-value" id="memory-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Charge CPU</span>
                                    <span class="metric-value" id="cpu-usage">0%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Connexions synaptiques</span>
                                    <span class="metric-value" id="synaptic-connections">0</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">État émotionnel</span>
                                    <span class="metric-value" id="emotional-state">Neutre</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>Agent à Mémoire Thermique &copy; 2025 | <i class="fas fa-thermometer-half"></i> Système de mémoire thermique avec accélérateur Kyber</p>
            <div class="system-stats">
                <span id="kyber-temp">Kyber: <span id="kyber-temperature">45.2°C</span></span> |
                <span id="memory-stat">Mémoire: <span id="memory-utilization">42%</span></span> |
                <span id="acceleration-factor">Accélération: <span id="kyber-acceleration">1.8x</span></span>
            </div>
        </footer>
    </div>

    <!-- Animation neuronale en arrière-plan pour tout le site -->
    <div id="neural-container" class="neural-background"></div>

    <!-- Chart.js pour les graphiques statistiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Contrôleur d'animations (chargé en premier pour désactiver les animations bloquantes) -->
    <script src="/js/animation-controller.js"></script>

    <!-- Gestionnaire de fallback API (chargé avant les scripts principaux pour éviter le mode limité) -->
    <script src="/js/api-fallback-manager.js"></script>

    <!-- Scripts principaux -->
    <script src="/js/simple-app.js"></script>
    <script src="/js/memory-manager.js"></script>
    <script src="/js/kyber-module.js"></script>
    <script src="/js/voice-interface.js"></script>
    <script src="/js/human-language-trainer.js"></script>
    <script src="/js/keyboard-shortcuts.js"></script>
    <script src="/js/code-explorer.js"></script>
    <script src="/js/file-loader.js"></script>
    <script src="/js/vscode-integration.js"></script>
    <script src="/js/vscode-integration-file-operations.js"></script>
    <script src="/js/auto-paradigm-initializer.js"></script>
    <!-- Scripts d'animation (chargés après le contrôleur pour respecter les paramètres) -->
    <script src="/js/neural-animation.js"></script>
    <script src="/js/neural-animation-enhanced.js"></script>
    <script src="/js/particle-animation.js"></script>
    <script src="/js/animated-stats-charts.js"></script>
    <script src="/js/sensory-memory-manager.js"></script>
    <script src="/js/sidebar-manager.js"></script>
    <script src="/js/enhanced-visualizations.js"></script>
    <script src="/js/neural-activity-fix.js"></script>
    <script src="/js/brain-activity-injector.js"></script>
    <script src="/js/simple-app.js"></script>
            </div>
        </div>
    </div>
</body>
</html>
