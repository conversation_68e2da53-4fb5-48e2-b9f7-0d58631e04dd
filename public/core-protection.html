<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protection du Noyau | Mémoire Thermique</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/core-protection.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="protection-container">
        <div class="protection-header">
            <h1 class="protection-title">Système de Protection du Noyau</h1>
            <div class="system-status">
                <span class="status-label">État du Système:</span>
                <span id="lockStatus" class="status-badge status-unlocked">Déverrouillé</span>
            </div>
        </div>

        <!-- Section Authentification -->
        <div class="protection-section auth-section">
            <h2 class="section-title">Authentification du Créateur</h2>
            <div class="auth-container">
                <div class="creator-status">
                    <div class="status-indicator">
                        <div id="creatorDefinedIndicator" class="indicator-light"></div>
                        <span id="creatorDefinedStatus">Créateur non défini</span>
                    </div>
                </div>
                
                <div class="auth-form">
                    <div class="form-group">
                        <label for="firstName">Prénom</label>
                        <input type="text" id="firstName" class="form-control" placeholder="Prénom">
                    </div>
                    <div class="form-group">
                        <label for="lastName">Nom de famille</label>
                        <input type="text" id="lastName" class="form-control" placeholder="Nom de famille">
                    </div>
                    <div class="auth-buttons">
                        <button id="setCreatorBtn" class="primary-btn">Définir le Créateur</button>
                        <button id="authenticateBtn" class="action-btn">Authentifier</button>
                    </div>
                </div>
                
                <div class="system-controls">
                    <button id="lockSystemBtn" class="danger-btn">Verrouiller le Système</button>
                    <button id="unlockSystemBtn" class="success-btn">Déverrouiller le Système</button>
                </div>
            </div>
        </div>

        <!-- Section Analyse du Code -->
        <div class="protection-section analysis-section">
            <h2 class="section-title">Analyse et Diagnostic du Code</h2>
            <div class="analysis-controls">
                <button id="analyzeCodeBtn" class="primary-btn">Analyser le Noyau</button>
                <button id="viewReportsBtn" class="action-btn">Voir les Rapports</button>
            </div>
            
            <div class="analysis-summary">
                <div class="summary-card">
                    <h3 class="card-title">État d'Intégrité</h3>
                    <div id="integrityStatus" class="status-display">
                        <div class="status-indicator">
                            <div id="integrityIndicator" class="indicator-light"></div>
                            <span id="integrityLabel">Non vérifié</span>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <h3 class="card-title">Problèmes Détectés</h3>
                    <div class="issues-summary">
                        <div class="issue-count">
                            <span class="count-label">Critiques:</span>
                            <span id="criticalCount" class="count-value">0</span>
                        </div>
                        <div class="issue-count">
                            <span class="count-label">Avertissements:</span>
                            <span id="warningCount" class="count-value">0</span>
                        </div>
                    </div>
                </div>
                
                <div class="summary-card">
                    <h3 class="card-title">Dernière Vérification</h3>
                    <div id="lastCheckTime" class="time-display">Jamais</div>
                </div>
            </div>
            
            <div class="analysis-chart-container">
                <canvas id="integrityChart"></canvas>
            </div>
        </div>

        <!-- Section Problèmes Détectés -->
        <div class="protection-section issues-section">
            <h2 class="section-title">Problèmes Détectés</h2>
            <div class="issues-filter">
                <label>
                    <input type="checkbox" id="showCritical" checked>
                    Critiques
                </label>
                <label>
                    <input type="checkbox" id="showWarnings" checked>
                    Avertissements
                </label>
            </div>
            
            <div class="issues-list-container">
                <table class="issues-table">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Module</th>
                            <th>Message</th>
                            <th>Ligne</th>
                        </tr>
                    </thead>
                    <tbody id="issuesTableBody">
                        <!-- Les problèmes seront ajoutés ici dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Section Auto-Réparation -->
        <div class="protection-section repair-section">
            <h2 class="section-title">Auto-Diagnostic et Réparation</h2>
            <div class="repair-description">
                <p>Le système peut analyser et réparer automatiquement certains problèmes non critiques détectés dans le noyau. 
                Les problèmes critiques nécessitent l'intervention du créateur.</p>
            </div>
            
            <div class="repair-controls">
                <button id="diagnosticBtn" class="primary-btn">Lancer l'Auto-Diagnostic</button>
                <button id="repairBtn" class="action-btn">Tenter la Réparation</button>
            </div>
            
            <div class="diagnostic-results">
                <h3>Résultats du Diagnostic</h3>
                <div id="diagnosticOutput" class="output-container">
                    <p>Aucun diagnostic effectué.</p>
                </div>
            </div>
        </div>

        <!-- Section Journal de Sécurité -->
        <div class="protection-section log-section">
            <h2 class="section-title">Journal de Sécurité</h2>
            <div class="log-container">
                <div id="securityLog" class="log-output">
                    <!-- Les entrées du journal seront ajoutées ici dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="reportsModal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Rapports d'Intégrité</h2>
            <div class="reports-list">
                <table id="reportsTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>État</th>
                            <th>Problèmes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="reportsTableBody">
                        <!-- Les rapports seront ajoutés ici dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div id="reportDetailModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close-btn">&times;</span>
            <h2>Détails du Rapport</h2>
            <div id="reportDetail" class="report-detail">
                <!-- Les détails du rapport seront ajoutés ici dynamiquement -->
            </div>
        </div>
    </div>

    <script src="/js/security-guardian.js"></script>
    <script src="/js/core-protection-utils.js"></script>
    <script src="/js/core-protection-analysis.js"></script>
    <script src="/js/core-protection.js"></script>
</body>
</html>