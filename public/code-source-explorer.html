<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explorateur de Sources | Agent à Mémoire Thermique</title>
    
    <!-- Styles communs -->
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/neural-animation.css">
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Styles spécifiques -->
    <link rel="stylesheet" href="/css/code-explorer.css">
    
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo-container">
                <div class="logo"></div>
                <h1>Explorateur de Sources</h1>
            </div>
            <nav>
                <ul class="main-menu">
                    <li><a href="/"><i class="fas fa-home"></i> Accueil</a></li>
                    <li><a href="/code-analyzer.html"><i class="fas fa-code"></i> Analyseur de Code</a></li>
                    <li class="active"><a href="/code-source-explorer.html"><i class="fas fa-globe"></i> Explorateur de Sources</a></li>
                    <li><a href="/thermal-paradigm-explorer.html"><i class="fas fa-brain"></i> Paradigmes</a></li>
                    <li><a href="/core-protection.html"><i class="fas fa-shield-alt"></i> Protection</a></li>
                </ul>
            </nav>
            <div class="user-controls">
                <button id="toggleScan" class="action-button">
                    <i class="fas fa-play-circle"></i>
                    <span>Scanner</span>
                </button>
                <button id="addSourceBtn" class="primary-button">
                    <i class="fas fa-plus"></i>
                    <span>Nouvelle Source</span>
                </button>
            </div>
        </header>
        
        <main class="explorer-content">
            <div class="dashboard-widgets">
                <div class="widget stats-widget">
                    <div class="widget-header">
                        <h3><i class="fas fa-chart-line"></i> Statistiques d'Analyse</h3>
                    </div>
                    <div class="widget-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="totalSources">0</div>
                                <div class="stat-label">Sources Actives</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalRepos">0</div>
                                <div class="stat-label">Dépôts Analysés</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalFiles">0</div>
                                <div class="stat-label">Fichiers Analysés</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalPatterns">0</div>
                                <div class="stat-label">Patterns Découverts</div>
                            </div>
                        </div>
                        
                        <div class="last-analysis">
                            <span>Dernière analyse:</span>
                            <span id="lastAnalysisTime">Jamais</span>
                        </div>
                        
                        <div class="scan-controls">
                            <div class="auto-scan-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="autoScanToggle">
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-label">Analyse Automatique</span>
                                </label>
                            </div>
                            <div class="scan-interval">
                                <label for="scanIntervalInput">Intervalle (minutes):</label>
                                <input type="number" id="scanIntervalInput" min="5" max="1440" value="60">
                                <button id="setScanInterval" class="small-button">Définir</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="widget patterns-widget">
                    <div class="widget-header">
                        <h3><i class="fas fa-brain"></i> Patterns Récents</h3>
                    </div>
                    <div class="widget-content">
                        <div class="patterns-list" id="recentPatterns">
                            <!-- Rempli dynamiquement -->
                            <div class="no-data-message">
                                Aucun pattern récent découvert. Lancez une analyse pour commencer l'apprentissage.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sources-container">
                <div class="section-header">
                    <h2>Sources de Code</h2>
                    <div class="source-filters">
                        <select id="sourceTypeFilter">
                            <option value="all">Tous les types</option>
                            <option value="github">GitHub</option>
                            <option value="gitlab">GitLab</option>
                            <option value="bitbucket">Bitbucket</option>
                            <option value="custom">Custom API</option>
                        </select>
                        <select id="sourceStatusFilter">
                            <option value="all">Tous les statuts</option>
                            <option value="active">Actifs</option>
                            <option value="never">Jamais analysés</option>
                            <option value="recent">Analysés récemment</option>
                        </select>
                        <div class="search-container">
                            <input type="text" id="sourceSearchInput" placeholder="Rechercher...">
                            <button id="sourceSearchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="source-list" id="sourcesList">
                    <!-- Rempli dynamiquement -->
                    <div class="no-sources-message">
                        Aucune source configurée. Cliquez sur "Nouvelle Source" pour en ajouter.
                    </div>
                </div>
            </div>
            
            <div class="analysis-history">
                <div class="section-header">
                    <h2>Historique d'Analyse</h2>
                    <button id="clearHistoryBtn" class="small-button">
                        <i class="fas fa-trash"></i>
                        Effacer
                    </button>
                </div>
                
                <div class="history-list" id="analysisHistory">
                    <!-- Rempli dynamiquement -->
                    <div class="no-data-message">
                        Aucun historique d'analyse. Lancez une analyse pour commencer.
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Animation neuronale en arrière-plan -->
        <div id="neural-container" class="neural-background"></div>
    </div>
    
    <!-- Modales -->
    <div id="addSourceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Ajouter une Nouvelle Source</h2>
                <button class="close-modal-btn">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addSourceForm">
                    <div class="form-group">
                        <label for="sourceType">Type de Source:</label>
                        <select id="sourceType" required>
                            <option value="github">GitHub</option>
                            <option value="gitlab">GitLab</option>
                            <option value="bitbucket">Bitbucket</option>
                            <option value="custom">API Personnalisée</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sourceUrl">URL du Dépôt:</label>
                        <input type="url" id="sourceUrl" placeholder="https://github.com/username/repo" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="sourceName">Nom (optionnel):</label>
                        <input type="text" id="sourceName" placeholder="Nom descriptif">
                    </div>
                    
                    <div class="form-group">
                        <label for="sourceBranch">Branche (optionnel):</label>
                        <input type="text" id="sourceBranch" placeholder="main">
                    </div>
                    
                    <div class="form-group">
                        <label for="sourcePriority">Priorité:</label>
                        <input type="range" id="sourcePriority" min="1" max="10" value="5">
                        <div class="range-labels">
                            <span>Basse</span>
                            <span>Haute</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Options:</label>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" id="analyzeNow">
                                Analyser immédiatement
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="cancel-button close-modal-btn">Annuler</button>
                        <button type="submit" class="submit-button">Ajouter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Template pour les cartes de source -->
    <template id="sourceCardTemplate">
        <div class="source-card">
            <div class="source-icon">
                <i class="fab fa-github"></i>
            </div>
            <div class="source-info">
                <h3 class="source-name">Nom du Dépôt</h3>
                <div class="source-url">https://github.com/user/repo</div>
                <div class="source-meta">
                    <span class="source-type">GitHub</span>
                    <span class="source-branch">main</span>
                </div>
                <div class="source-stats">
                    <div class="stat">
                        <i class="fas fa-file-code"></i>
                        <span class="files-analyzed">0</span> fichiers
                    </div>
                    <div class="stat">
                        <i class="fas fa-lightbulb"></i>
                        <span class="patterns-discovered">0</span> patterns
                    </div>
                    <div class="stat">
                        <i class="fas fa-calendar"></i>
                        <span class="last-analysis">Jamais</span>
                    </div>
                </div>
            </div>
            <div class="source-actions">
                <button class="analyze-source-btn" title="Analyser maintenant">
                    <i class="fas fa-play"></i>
                </button>
                <button class="edit-source-btn" title="Modifier">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="remove-source-btn" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </template>
    
    <!-- Scripts -->
    <script src="/js/startup-handler.js"></script>
    <script src="/js/neural-animation.js"></script>
    <script src="/js/code-memory-connector.js"></script>
    <script src="/js/code-source-analyzer.js"></script>
    <script src="/js/code-explorer.js"></script>
    
    <script>
        // Initialisation des animations neuronales
        document.addEventListener('DOMContentLoaded', () => {
            if (window.initNeuralAnimation) {
                initNeuralAnimation();
            }
        });
    </script>
</body>
</html>
