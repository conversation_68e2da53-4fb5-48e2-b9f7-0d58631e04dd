<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration <PERSON> <PERSON> <PERSON><PERSON> AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 20px;
        }

        .guide-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #ff69b4;
            backdrop-filter: blur(10px);
        }

        .section-title {
            font-size: 1.5rem;
            color: #ff69b4;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }

        .step-number {
            background: #4caf50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow-x: auto;
        }

        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .test-btn {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .test-btn:hover {
            background: linear-gradient(135deg, #ff1493, #dc143c);
            transform: translateY(-2px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-robot"></i> Configuration Agent Claude</h1>
        <p>Guide complet pour connecter Louna à l'agent Claude Anthropic</p>
        <div style="margin-top: 15px;">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/qi-test-simple.html" class="nav-btn"><i class="fas fa-stethoscope"></i> Diagnostic</a>
        </div>
    </div>

    <div class="container">
        <!-- Statut actuel -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                Statut Actuel de la Connexion Claude
            </div>
            
            <div id="status-display">
                <div style="text-align: center; padding: 20px;">
                    <button class="test-btn" onclick="checkClaudeStatus()">
                        <i class="fas fa-sync"></i> Vérifier le Statut Claude
                    </button>
                </div>
            </div>
        </div>

        <!-- Étape 1: Obtenir une clé API -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-key"></i>
                Étape 1: Obtenir une Clé API Claude
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Aller sur le site Anthropic</strong>
                <p>Rendez-vous sur <a href="https://console.anthropic.com/" target="_blank" style="color: #ff69b4;">https://console.anthropic.com/</a></p>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Créer un compte ou se connecter</strong>
                <p>Créez un compte Anthropic ou connectez-vous si vous en avez déjà un.</p>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Générer une clé API</strong>
                <p>Dans la console, allez dans "API Keys" et créez une nouvelle clé.</p>
                <div class="warning">
                    <strong>⚠️ Important :</strong> Copiez immédiatement votre clé API car elle ne sera plus visible après.
                </div>
            </div>
        </div>

        <!-- Étape 2: Configuration -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-cog"></i>
                Étape 2: Configuration de Louna AI
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Ouvrir le fichier .env</strong>
                <p>Le fichier se trouve dans l'application Louna AI :</p>
                <div class="code-block">
/Applications/Louna AI.app/Contents/Resources/.env
                </div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Ajouter votre clé API</strong>
                <p>Modifiez la ligne ANTHROPIC_API_KEY :</p>
                <div class="code-block">
# Remplacez cette ligne :
# ANTHROPIC_API_KEY=sk-ant-api03-...

# Par votre vraie clé :
ANTHROPIC_API_KEY=sk-ant-api03-VOTRE_CLE_ICI
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Sauvegarder et redémarrer</strong>
                <p>Sauvegardez le fichier .env et redémarrez l'application Louna AI.</p>
            </div>
        </div>

        <!-- Étape 3: Test -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-flask"></i>
                Étape 3: Test de la Connexion
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Tester la connexion</strong>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="test-btn" onclick="testClaudeConnection()">
                        <i class="fas fa-play"></i> Tester la Connexion Claude
                    </button>
                </div>
                <div id="test-results"></div>
            </div>
        </div>

        <!-- Dépannage -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-wrench"></i>
                Dépannage
            </div>
            
            <div class="step">
                <strong>Problèmes courants :</strong>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><strong>Clé API invalide :</strong> Vérifiez que vous avez copié la clé complète</li>
                    <li><strong>Quota dépassé :</strong> Vérifiez votre usage sur console.anthropic.com</li>
                    <li><strong>Erreur de réseau :</strong> Vérifiez votre connexion Internet</li>
                    <li><strong>Fichier .env non trouvé :</strong> Relancez le patch serveur</li>
                </ul>
            </div>

            <div class="warning">
                <strong>🔧 Si rien ne fonctionne :</strong><br>
                1. Redémarrez complètement l'application Louna AI<br>
                2. Vérifiez que le fichier .env est bien dans le bon dossier<br>
                3. Utilisez le diagnostic complet dans le centre de diagnostic
            </div>
        </div>

        <!-- Avantages -->
        <div class="guide-section">
            <div class="section-title">
                <i class="fas fa-star"></i>
                Avantages de la Connexion Claude
            </div>
            
            <div class="success">
                <strong>✅ Avec Claude configuré, vous obtenez :</strong>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>🤖 <strong>Vraies réponses de Louna</strong> (pas de simulation)</li>
                    <li>🧠 <strong>Intelligence réelle</strong> avec QI évolutif</li>
                    <li>💬 <strong>Conversations authentiques</strong> sur l'évolution QI</li>
                    <li>🌴 <strong>Personnalité caribéenne</strong> de Louna</li>
                    <li>❤️ <strong>Reconnaissance envers Jean-Luc</strong> son créateur</li>
                    <li>📈 <strong>Réponses sur l'évolution</strong> vers 250 de QI</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function checkClaudeStatus() {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = '<div style="text-align: center;"><i class="fas fa-spinner fa-spin"></i> Vérification...</div>';
            
            try {
                const response = await fetch('/api/claude/status');
                const data = await response.json();
                
                let statusHtml = '<div style="padding: 20px;">';
                
                if (data.available) {
                    statusHtml += `
                        <div style="text-align: center; color: #4caf50; font-size: 1.2rem; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i> Claude est DISPONIBLE !
                        </div>
                        <div>Modèle : ${data.model || 'Claude'}</div>
                        <div>Statut : Opérationnel</div>
                    `;
                } else {
                    statusHtml += `
                        <div style="text-align: center; color: #f44336; font-size: 1.2rem; margin-bottom: 15px;">
                            <i class="fas fa-times-circle"></i> Claude INDISPONIBLE
                        </div>
                        <div><strong>Raison :</strong> ${data.reason}</div>
                        <div><strong>Suggestion :</strong> ${data.suggestion || 'Suivre le guide ci-dessous'}</div>
                    `;
                }
                
                statusHtml += '</div>';
                statusDiv.innerHTML = statusHtml;
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #f44336;">
                        <i class="fas fa-exclamation-triangle"></i> Erreur de vérification<br>
                        ${error.message}
                    </div>
                `;
            }
        }

        async function testClaudeConnection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div style="text-align: center;"><i class="fas fa-spinner fa-spin"></i> Test en cours...</div>';
            
            try {
                const response = await fetch('/api/claude/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'Bonjour Louna ! Je suis Jean-Luc, ton créateur. Peux-tu confirmer que tu es bien connectée ?'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div style="background: rgba(76, 175, 80, 0.2); border: 2px solid #4caf50; border-radius: 10px; padding: 15px; margin: 15px 0;">
                            <div style="color: #4caf50; font-size: 1.1rem; margin-bottom: 10px;">
                                <i class="fas fa-check"></i> Test RÉUSSI ! Claude est connecté
                            </div>
                            <div><strong>Modèle :</strong> ${data.model}</div>
                            <div><strong>Réponse de Louna :</strong></div>
                            <div style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                ${data.response}
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(data.error);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div style="background: rgba(244, 67, 54, 0.2); border: 2px solid #f44336; border-radius: 10px; padding: 15px; margin: 15px 0;">
                        <div style="color: #f44336; font-size: 1.1rem; margin-bottom: 10px;">
                            <i class="fas fa-times"></i> Test ÉCHOUÉ
                        </div>
                        <div><strong>Erreur :</strong> ${error.message}</div>
                        <div><strong>Solution :</strong> Suivez le guide de configuration ci-dessus</div>
                    </div>
                `;
            }
        }

        // Vérifier le statut au chargement
        document.addEventListener('DOMContentLoaded', function() {
            checkClaudeStatus();
        });
    </script>
</body>
</html>
