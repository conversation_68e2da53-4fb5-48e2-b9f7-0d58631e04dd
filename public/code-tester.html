<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laboratoire de Test de Code - Mémoire Thermique</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/dracula.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <link rel="stylesheet" href="css/code-tester.css">
</head>
<body>
    <header class="app-header">
        <div class="logo-container">
            <img src="img/thermal-logo.png" alt="Mémoire Thermique" class="logo">
            <h1>Laboratoire de Test de Code</h1>
        </div>
        <nav class="main-nav">
            <a href="index.html" class="nav-link"><i class="fas fa-home"></i> Accueil</a>
            <a href="code-evolution.html" class="nav-link"><i class="fas fa-brain"></i> Évolution de Code</a>
            <a href="code-tester.html" class="nav-link active"><i class="fas fa-vial"></i> Laboratoire</a>
            <a href="settings.html" class="nav-link"><i class="fas fa-cog"></i> Paramètres</a>
        </nav>
    </header>

    <main class="app-container">
        <div class="editor-panel">
            <div class="editor-toolbar">
                <div class="toolbar-section">
                    <select id="languageSelect" class="language-select">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="html">HTML</option>
                        <option value="css">CSS</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="rust">Rust</option>
                        <option value="go">Go</option>
                        <option value="ruby">Ruby</option>
                        <option value="php">PHP</option>
                    </select>
                </div>
                <div class="toolbar-section">
                    <button id="newCodeBtn" class="toolbar-btn" title="Nouveau code">
                        <i class="fas fa-file"></i>
                    </button>
                    <button id="loadCodeBtn" class="toolbar-btn" title="Charger un code">
                        <i class="fas fa-folder-open"></i>
                    </button>
                    <button id="saveCodeBtn" class="toolbar-btn" title="Sauvegarder le code">
                        <i class="fas fa-save"></i>
                    </button>
                </div>
                <div class="toolbar-section">
                    <button id="formatCodeBtn" class="toolbar-btn" title="Formater le code">
                        <i class="fas fa-indent"></i>
                    </button>
                    <button id="analyzeCodeBtn" class="toolbar-btn" title="Analyser le code">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="toolbar-section">
                    <button id="runCodeBtn" class="primary-btn" title="Exécuter le code">
                        <i class="fas fa-play"></i> Exécuter
                    </button>
                    <button id="clearOutputBtn" class="toolbar-btn" title="Effacer la sortie">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
            <div id="codeEditor" class="code-editor"></div>
        </div>

        <div class="panel-divider"></div>

        <div class="results-panel">
            <div class="tabs">
                <button class="tab-btn active" data-target="outputTab">
                    <i class="fas fa-terminal"></i> Sortie
                </button>
                <button class="tab-btn" data-target="analysisTab">
                    <i class="fas fa-microscope"></i> Analyse
                </button>
                <button class="tab-btn" data-target="compressionTab">
                    <i class="fas fa-compress-arrows-alt"></i> Compression
                </button>
            </div>

            <div class="tab-content">
                <div id="outputTab" class="tab-pane active">
                    <div class="panel-toolbar">
                        <span class="panel-title">Résultat d'exécution</span>
                    </div>
                    <div id="outputDisplay" class="output-display">
                        <p class="output-placeholder">Les résultats d'exécution s'afficheront ici.</p>
                    </div>
                </div>

                <div id="analysisTab" class="tab-pane">
                    <div class="panel-toolbar">
                        <span class="panel-title">Analyse du Code</span>
                        <div class="toolbar-actions">
                            <button id="improveCodeBtn" class="action-btn">
                                <i class="fas fa-magic"></i> Améliorer
                            </button>
                        </div>
                    </div>
                    <div id="analysisDisplay" class="analysis-display">
                        <p class="analysis-placeholder">L'analyse du code s'affichera ici.</p>
                    </div>
                </div>

                <div id="compressionTab" class="tab-pane">
                    <div class="panel-toolbar">
                        <span class="panel-title">Analyse de Compression</span>
                        <div class="toolbar-actions">
                            <button id="analyzeCompressionBtn" class="action-btn">
                                <i class="fas fa-compress"></i> Analyser
                            </button>
                            <button id="innovateCompressionBtn" class="action-btn">
                                <i class="fas fa-lightbulb"></i> Innover
                            </button>
                        </div>
                    </div>
                    <div id="compressionDisplay" class="compression-display">
                        <p class="compression-placeholder">Les résultats d'analyse de compression s'afficheront ici.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal de chargement de code -->
    <div id="loadCodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Charger un Code</h2>
                <span class="close-btn">&times;</span>
            </div>
            <div class="modal-body">
                <div class="modal-section">
                    <h3>Codes Sauvegardés</h3>
                    <select id="savedCodeSelect" class="full-width-select">
                        <option disabled selected>Sélectionnez un code</option>
                    </select>
                    <button id="loadSavedCodeBtn" class="action-btn full-width-btn">
                        <i class="fas fa-download"></i> Charger le code sélectionné
                    </button>
                </div>
                <div class="modal-section">
                    <h3>Charger depuis un fichier</h3>
                    <input type="file" id="codeFileInput" class="file-input">
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de sauvegarde de code -->
    <div id="saveCodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sauvegarder le Code</h2>
                <span class="close-btn">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="codeNameInput">Nom du code</label>
                    <input type="text" id="codeNameInput" class="text-input" placeholder="Entrez un nom pour ce code">
                </div>
                <div class="form-group">
                    <label for="codeDescInput">Description (optionnelle)</label>
                    <textarea id="codeDescInput" class="textarea-input" placeholder="Entrez une description"></textarea>
                </div>
                <div class="modal-actions">
                    <button id="cancelSaveBtn" class="secondary-btn">Annuler</button>
                    <button id="confirmSaveBtn" class="primary-btn">Sauvegarder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'analyse détaillée de compression -->
    <div id="compressionAnalysisModal" class="modal">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h2>Analyse de Compression Détaillée</h2>
                <span class="close-btn">&times;</span>
            </div>
            <div class="modal-body">
                <div id="compressionDetailDisplay" class="detail-display"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/clike/clike.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="js/code-tester.js"></script>
</body>
</html>