<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Monitoring Complet - LOUNA AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .panel-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            flex: 1;
        }

        .metric-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 0 15px currentColor;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .temperature-display {
            font-size: 4em;
            text-align: center;
            margin: 20px 0;
            color: #ff6b6b;
            text-shadow: 0 0 30px #ff6b6b;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .progress-bar {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #ef4444; }

        .chart-container {
            height: 200px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(to top, rgba(79, 172, 254, 0.3), transparent);
            clip-path: polygon(0% 100%, 10% 80%, 20% 85%, 30% 70%, 40% 75%, 50% 60%, 60% 65%, 70% 50%, 80% 55%, 90% 40%, 100% 45%, 100% 100%);
            animation: chartFlow 3s ease-in-out infinite;
        }

        @keyframes chartFlow {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        .reflection-panel {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
            border: 2px solid #ff6b6b;
        }

        .reflection-boost {
            font-size: 1.8em;
            color: #fbbf24;
            text-shadow: 0 0 15px #fbbf24;
            text-align: center;
            margin: 10px 0;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .log-container {
            height: 250px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #4ade80;
            padding-left: 10px;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .log-timestamp {
            color: #94a3b8;
            margin-right: 10px;
        }

        .neural-visualization {
            position: relative;
            height: 150px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .neural-node {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4ade80;
            border-radius: 50%;
            box-shadow: 0 0 10px #4ade80;
            animation: neuralPulse 2s infinite ease-in-out;
        }

        @keyframes neuralPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.3); }
        }

        .performance-ring {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#4ade80 0deg, #4ade80 270deg, rgba(255, 255, 255, 0.2) 270deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            position: relative;
        }

        .performance-ring::before {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .performance-value {
            font-size: 1.5em;
            font-weight: bold;
            z-index: 1;
        }

        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <div style="text-align: left; margin-bottom: 10px;">
                <button onclick="window.location.href='/'" style="background: linear-gradient(45deg, #667eea, #764ba2); border: none; color: white; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: bold; transition: all 0.3s ease;">🏠 Retour à l'Accueil</button>
            </div>
            <h1>📊 LOUNA AI - Monitoring Complet</h1>
            <p>Surveillance en temps réel du système neuronal artificiel avec mémoire thermique vivante</p>
        </div>

        <!-- Panneau Température -->
        <div class="panel">
            <div class="panel-title">🌡️ Température Thermique</div>
            <div class="temperature-display" id="global-temperature">78.2°C</div>
            <div class="progress-bar">
                <div class="progress-fill" id="temp-progress" style="width: 78%"></div>
            </div>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="efficiency">95%</div>
                    <div class="metric-label">Efficacité</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="memory-entries">2</div>
                    <div class="metric-label">Entrées</div>
                </div>
            </div>
        </div>

        <!-- Panneau Activité Neuronale -->
        <div class="panel">
            <div class="panel-title">🧠 Activité Neuronale</div>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="neuron-count">75</div>
                    <div class="metric-label">Neurones</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="synapse-count">225</div>
                    <div class="metric-label">Synapses</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="plasticity">0.95</div>
                    <div class="metric-label">Plasticité</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="growth-rate">0.5</div>
                    <div class="metric-label">Croissance</div>
                </div>
            </div>
            <div class="neural-visualization" id="neural-viz">
                <!-- Neurones générés dynamiquement -->
            </div>
        </div>

        <!-- Panneau Réflexion -->
        <div class="panel reflection-panel">
            <div class="panel-title">⚡ Système de Réflexion</div>
            <div class="connection-status">
                <div class="status-indicator status-active"></div>
                <span>Connexion Mémoire Thermique Active</span>
            </div>
            <div class="reflection-boost" id="total-boost">4.9x</div>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="cognitive-boost">2.7x</div>
                    <div class="metric-label">Cognitif</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="memory-boost">2.6x</div>
                    <div class="metric-label">Mémoire</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="reasoning-boost">2.3x</div>
                    <div class="metric-label">Raisonnement</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="accelerators">2</div>
                    <div class="metric-label">Accélérateurs</div>
                </div>
            </div>
        </div>

        <!-- Panneau Performance -->
        <div class="panel">
            <div class="panel-title">📈 Performance Globale</div>
            <div class="performance-ring">
                <div class="performance-value" id="global-performance">95%</div>
            </div>
            <div class="chart-container">
                <div class="chart-line"></div>
            </div>
            <div class="controls">
                <button class="btn" onclick="generateNeurons()">🧠 Neurones</button>
                <button class="btn" onclick="boostReflection()">⚡ Boost</button>
            </div>
        </div>

        <!-- Panneau Statut Système -->
        <div class="panel">
            <div class="panel-title">🔧 Statut Système</div>
            <div class="status-grid">
                <div class="status-item">
                    <span>Mémoire Thermique</span>
                    <div class="status-indicator status-active"></div>
                </div>
                <div class="status-item">
                    <span>Accélérateurs Kyber</span>
                    <div class="status-indicator status-active"></div>
                </div>
                <div class="status-item">
                    <span>Cerveau Artificiel</span>
                    <div class="status-indicator status-active"></div>
                </div>
                <div class="status-item">
                    <span>Calculateur QI</span>
                    <div class="status-indicator status-active"></div>
                </div>
                <div class="status-item">
                    <span>Surveillance Anti-Crash</span>
                    <div class="status-indicator status-active"></div>
                </div>
                <div class="status-item">
                    <span>DeepSeek R1-0528-8B</span>
                    <div class="status-indicator status-active"></div>
                </div>
            </div>
            <div class="controls">
                <button class="btn" onclick="refreshAll()">🔄 Actualiser</button>
                <button class="btn" onclick="exportMetrics()">💾 Exporter</button>
            </div>
        </div>

        <!-- Panneau Journal -->
        <div class="panel">
            <div class="panel-title">📋 Journal d'Activité</div>
            <div class="log-container" id="activity-log">
                <div class="log-entry">
                    <span class="log-timestamp">[18:05:30]</span>
                    <span>🌡️ Température stable à 78.2°C</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[18:05:25]</span>
                    <span>🧠 75 neurones actifs, 225 connexions synaptiques</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[18:05:20]</span>
                    <span>⚡ Boost réflexion: 4.9x total</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[18:05:15]</span>
                    <span>🔗 Connexion réflexion-mémoire thermique active</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[18:05:10]</span>
                    <span>📊 Efficacité système: 95%</span>
                </div>
            </div>
        </div>

        <!-- Panneau Chat avec Pensées -->
        <div class="panel" style="grid-column: span 2;">
            <div class="panel-title">💭 Chat avec Pensées Internes</div>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <input type="text" id="chat-input" placeholder="Posez une question à Louna..." style="flex: 1; padding: 10px; border: 1px solid #333; border-radius: 5px; background: #1a1a1a; color: white;">
                <button onclick="sendChatMessage()" style="padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 5px; color: white; cursor: pointer;">💬 Envoyer</button>
            </div>
            <div id="chat-responses" style="max-height: 300px; overflow-y: auto; background: #0a0a0a; border: 1px solid #333; border-radius: 5px; padding: 10px;">
                <div style="color: #888; text-align: center; padding: 20px;">Posez une question pour voir les pensées internes de Louna...</div>
            </div>
        </div>
    </div>

    <script>
        // Initialisation des neurones visuels
        function initNeuralVisualization() {
            const container = document.getElementById('neural-viz');
            const width = container.offsetWidth;
            const height = container.offsetHeight;
            
            for (let i = 0; i < 15; i++) {
                const node = document.createElement('div');
                node.className = 'neural-node';
                node.style.left = Math.random() * (width - 8) + 'px';
                node.style.top = Math.random() * (height - 8) + 'px';
                node.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(node);
            }
        }

        // Fonctions de contrôle
        async function generateNeurons() {
            try {
                const response = await fetch('/api/brain/generate-neurons', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ count: 5 })
                });
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neuron-count').textContent = data.brainStats.activeNeurons;
                    document.getElementById('synapse-count').textContent = data.brainStats.synapticConnections;
                    addLogEntry(`🧠 ${data.neuronsGenerated} nouveaux neurones générés`);
                }
            } catch (error) {
                addLogEntry(`❌ Erreur génération neurones: ${error.message}`);
            }
        }

        async function boostReflection() {
            addLogEntry('⚡ Boost réflexion activé');
            
            // Animation des valeurs de boost
            const boosts = ['cognitive-boost', 'memory-boost', 'reasoning-boost', 'total-boost'];
            boosts.forEach(id => {
                const element = document.getElementById(id);
                element.style.animation = 'pulse 0.5s ease-in-out 3';
            });
        }

        async function refreshAll() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success && data.metrics) {
                    updateAllMetrics(data.metrics);
                    addLogEntry('🔄 Toutes les métriques actualisées');
                }
            } catch (error) {
                addLogEntry(`❌ Erreur actualisation: ${error.message}`);
            }
        }

        function updateAllMetrics(metrics) {
            // Métriques de mémoire thermique
            if (metrics.memoryStats) {
                const temp = metrics.memoryStats.globalTemperature || 78.2;
                document.getElementById('global-temperature').textContent = temp.toFixed(1) + '°C';
                document.getElementById('temp-progress').style.width = Math.min(100, temp * 1.2) + '%';
                document.getElementById('efficiency').textContent = Math.round(metrics.memoryStats.memoryEfficiency || 95) + '%';
                document.getElementById('memory-entries').textContent = metrics.memoryStats.totalMemories || 2;
            }
            
            // Métriques du cerveau
            if (metrics.brainStats) {
                document.getElementById('neuron-count').textContent = metrics.brainStats.activeNeurons || 75;
                document.getElementById('synapse-count').textContent = metrics.brainStats.synapticConnections || 225;
                document.getElementById('plasticity').textContent = (metrics.brainStats.synapticPlasticity || 0.95).toFixed(2);
                document.getElementById('growth-rate').textContent = (metrics.brainStats.neuronGrowthRate || 0.5).toFixed(1);
            }
            
            // Métriques d'accélération
            if (metrics.accelerationStats) {
                const boost = metrics.accelerationStats.performanceBoost || 2.7;
                document.getElementById('cognitive-boost').textContent = boost.toFixed(1) + 'x';
                document.getElementById('total-boost').textContent = (boost * 1.8).toFixed(1) + 'x';
                document.getElementById('accelerators').textContent = metrics.accelerationStats.activeAccelerators || 2;
            }
        }

        function exportMetrics() {
            const metrics = {
                timestamp: new Date().toISOString(),
                temperature: document.getElementById('global-temperature').textContent,
                neurons: document.getElementById('neuron-count').textContent,
                synapses: document.getElementById('synapse-count').textContent,
                efficiency: document.getElementById('efficiency').textContent,
                boost: document.getElementById('total-boost').textContent
            };
            
            const blob = new Blob([JSON.stringify(metrics, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'louna-ai-metrics.json';
            a.click();
            
            addLogEntry('💾 Métriques exportées');
        }

        function addLogEntry(message) {
            const log = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span>
                <span>${message}</span>
            `;
            log.insertBefore(entry, log.firstChild);

            // Garder seulement les 15 dernières entrées
            while (log.children.length > 15) {
                log.removeChild(log.lastChild);
            }
        }

        // Fonction de chat avec pensées
        async function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;

            const chatContainer = document.getElementById('chat-responses');
            input.value = '';
            input.disabled = true;

            // Afficher la question
            chatContainer.innerHTML += `
                <div style="margin-bottom: 15px; padding: 10px; background: #1a1a1a; border-radius: 5px; border-left: 3px solid #667eea;">
                    <strong>🤔 Vous:</strong> ${message}
                </div>
            `;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    // Extraire les pensées de la balise <think>
                    const thinkMatch = data.response.match(/<think>([\s\S]*?)<\/think>/);
                    const thoughts = thinkMatch ? thinkMatch[1].trim() : 'Aucune réflexion interne détectée';

                    // Extraire la réponse finale
                    const finalResponse = data.response.replace(/<think>[\s\S]*?<\/think>/, '').trim();

                    // Afficher les pensées internes
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #2a1a2a; border-radius: 5px; border-left: 3px solid #764ba2;">
                            <strong>🧠 Pensées Internes:</strong>
                            <div style="margin-top: 5px; font-style: italic; color: #ccc; white-space: pre-wrap;">${thoughts}</div>
                        </div>
                    `;

                    // Afficher la réponse finale
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 15px; padding: 10px; background: #1a2a1a; border-radius: 5px; border-left: 3px solid #4ade80;">
                            <strong>🤖 Louna:</strong>
                            <div style="margin-top: 5px; white-space: pre-wrap;">${finalResponse}</div>
                            <div style="margin-top: 10px; font-size: 0.8em; color: #888;">
                                QI: Agent ${data.iqAnalysis?.agentIQ || 'N/A'} | Mémoire ${data.iqAnalysis?.memoryIQ || 'N/A'} | Combiné ${data.iqAnalysis?.combinedIQ || 'N/A'}
                            </div>
                        </div>
                    `;

                    addLogEntry(`💬 Chat: "${message.substring(0, 30)}${message.length > 30 ? '...' : ''}"`);
                } else {
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 15px; padding: 10px; background: #2a1a1a; border-radius: 5px; border-left: 3px solid #ef4444;">
                            <strong>❌ Erreur:</strong> ${data.error || 'Erreur inconnue'}
                        </div>
                    `;
                }
            } catch (error) {
                chatContainer.innerHTML += `
                    <div style="margin-bottom: 15px; padding: 10px; background: #2a1a1a; border-radius: 5px; border-left: 3px solid #ef4444;">
                        <strong>❌ Erreur:</strong> ${error.message}
                    </div>
                `;
            }

            input.disabled = false;
            input.focus();
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Permettre l'envoi avec Entrée
        document.addEventListener('DOMContentLoaded', () => {
            const input = document.getElementById('chat-input');
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendChatMessage();
                    }
                });
            }
        });

        // Initialisation
        window.addEventListener('load', () => {
            initNeuralVisualization();
            refreshAll();
            
            // Actualisation automatique toutes les 10 secondes
            setInterval(refreshAll, 10000);
            
            // Ajouter des entrées de log périodiquement
            setInterval(() => {
                const messages = [
                    '🌡️ Température optimale maintenue',
                    '🧠 Activité neuronale stable',
                    '⚡ Accélérateurs fonctionnels',
                    '🔗 Connexions synaptiques actives',
                    '📊 Performance système excellente'
                ];
                const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                addLogEntry(randomMessage);
            }, 15000);
        });
    </script>
</body>
</html>
