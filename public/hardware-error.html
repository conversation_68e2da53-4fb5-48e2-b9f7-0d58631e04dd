<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sécurité - Erreur de validation matérielle</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #e6e6e6;
        }

        .error-container {
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 0, 0, 0.2);
            text-align: center;
        }

        h1 {
            color: #ff3b30;
            margin-bottom: 20px;
        }

        .icon {
            font-size: 60px;
            color: #ff3b30;
            margin-bottom: 20px;
        }

        p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .details {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }

        .admin-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .admin-form {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        input {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            margin-bottom: 15px;
            width: 250px;
            text-align: center;
        }

        button {
            background: linear-gradient(135deg, #ff3b30 0%, #8a0000 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 59, 48, 0.4);
        }

        .message {
            margin-top: 15px;
            min-height: 20px;
            font-weight: bold;
        }

        .success {
            color: #4cd964;
        }

        .error {
            color: #ff3b30;
        }

        .note {
            font-size: 14px;
            color: #8d99ae;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="icon">⚠️</div>
        <h1>Erreur de validation matérielle</h1>
        <p>
            Ce programme est protégé contre la copie non autorisée et ne peut s'exécuter que sur la machine pour laquelle il a été autorisé.
        </p>
        <p>
            La validation matérielle a échoué, ce qui indique que vous essayez d'exécuter ce programme sur une machine non autorisée.
        </p>

        <div class="details" id="errorDetails">
            <p><strong>Message d'erreur:</strong> <span id="errorMessage">Validation matérielle échouée</span></p>
            <p><strong>ID Machine:</strong> <span id="machineId">Non disponible</span></p>
            <p><strong>Date et heure:</strong> <span id="timestamp">Non disponible</span></p>
        </div>

        <div class="admin-section">
            <h2>Réinitialisation administrateur</h2>
            <p>Si vous êtes administrateur et souhaitez autoriser cette machine, entrez le code administrateur ci-dessous:</p>
            
            <div class="admin-form">
                <input type="password" id="adminCode" placeholder="Code administrateur" />
                <button id="resetBtn">Autoriser cette machine</button>
                <div id="message" class="message"></div>
            </div>
        </div>

        <p class="note">
            Note: Cette protection garantit que vos données restent strictement locales et sécurisées. 
            Pour toute assistance, veuillez contacter l'administrateur système.
        </p>
    </div>

    <script>
        // Récupérer l'erreur depuis les paramètres d'URL
        const urlParams = new URLSearchParams(window.location.search);
        const errorMsg = urlParams.get('error');
        const machineId = urlParams.get('machine') || 'Information non disponible';
        
        document.getElementById('errorMessage').textContent = errorMsg || 'Validation matérielle échouée';
        document.getElementById('machineId').textContent = machineId;
        document.getElementById('timestamp').textContent = new Date().toLocaleString();

        // Gestionnaire pour le bouton de réinitialisation
        document.getElementById('resetBtn').addEventListener('click', async () => {
            const adminCode = document.getElementById('adminCode').value;
            const messageEl = document.getElementById('message');
            
            if (!adminCode) {
                messageEl.textContent = 'Veuillez entrer le code administrateur';
                messageEl.className = 'message error';
                return;
            }
            
            try {
                const response = await fetch('/api/security/reset-hardware-binding', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ admin_code: adminCode })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    messageEl.textContent = 'Réinitialisation réussie! Cette machine est maintenant autorisée.';
                    messageEl.className = 'message success';
                    
                    // Rediriger vers la page d'accueil après un délai
                    setTimeout(() => {
                        window.location.href = '/secure-lock.html';
                    }, 2000);
                } else {
                    messageEl.textContent = data.message || 'Échec de la réinitialisation';
                    messageEl.className = 'message error';
                }
            } catch (error) {
                messageEl.textContent = 'Erreur de communication avec le serveur';
                messageEl.className = 'message error';
                console.error('Erreur:', error);
            }
        });
    </script>
</body>
</html>
