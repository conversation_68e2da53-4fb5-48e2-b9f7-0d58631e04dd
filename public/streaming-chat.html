<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Réflexion en Temps Réel</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .thinking-panel {
            background: #f0f8ff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
        }

        .thinking-title {
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .thinking-content {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .chat-container {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e3f2fd;
            color: #333;
            border-left: 4px solid #2196f3;
        }

        .status-message {
            background: #fff3e0;
            color: #e65100;
            border-left: 4px solid #ff9800;
            font-style: italic;
            text-align: center;
            margin: 0 auto;
            max-width: 60%;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        #messageInput {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        .btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f0f8ff;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        .status-dot.thinking {
            background: #ff9800;
            animation: blink 0.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .typing-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .quick-btn:hover {
            background: #667eea;
            color: white;
        }

        .memory-info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 LOUNA AI - Réflexion en Temps Réel</h1>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Prêt à réfléchir...</span>
            </div>
            <div id="memoryInfo" class="memory-info">
                Mémoire thermique: -- • Neurones: --
            </div>
        </div>

        <div class="thinking-panel">
            <div class="thinking-title">
                <span>💭 Réflexion Interne DeepSeek R1</span>
                <div class="typing-indicator" id="thinkingIndicator" style="display: none;"></div>
            </div>
            <div class="thinking-content" id="thinkingContent">
                En attente d'une question pour commencer la réflexion...
            </div>
        </div>

        <div class="quick-actions">
            <button class="quick-btn" onclick="quickMessage('Expliquez-moi la physique quantique')">🔬 Physique</button>
            <button class="quick-btn" onclick="quickMessage('Créez un algorithme de tri')">💻 Code</button>
            <button class="quick-btn" onclick="quickMessage('Analysez mes émotions')">🧠 Psychologie</button>
            <button class="quick-btn" onclick="quickMessage('Résolvez ce problème mathématique: 2x+5=15')">🔢 Maths</button>
        </div>

        <div id="chatContainer" class="chat-container">
            <div class="message ai-message">
                <div>👋 Bonjour ! Je suis LOUNA AI avec DeepSeek R1. Vous pouvez maintenant voir ma réflexion interne en temps réel !</div>
            </div>
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Posez votre question et voyez ma réflexion en direct..." 
                   onkeypress="handleKeyPress(event)">
            <button id="sendBtn" class="btn" onclick="sendMessage()">Envoyer</button>
        </div>
    </div>

    <script>
        let isProcessing = false;
        let eventSource = null;

        // Mettre à jour le statut
        function updateStatus(message, isThinking = false) {
            document.getElementById('statusText').textContent = message;
            const dot = document.getElementById('statusDot');
            dot.className = isThinking ? 'status-dot thinking' : 'status-dot';
        }

        // Ajouter un message au chat
        function addMessage(text, sender, isStatus = false) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            
            messageDiv.className = `message ${sender}-message`;
            if (isStatus) messageDiv.className += ' status-message';
            
            messageDiv.innerHTML = `<div>${text}</div>`;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
            
            return messageDiv;
        }

        // Mettre à jour la réflexion
        function updateThinking(content, append = false) {
            const thinkingContent = document.getElementById('thinkingContent');
            const indicator = document.getElementById('thinkingIndicator');
            
            if (append) {
                thinkingContent.textContent += content;
            } else {
                thinkingContent.textContent = content;
            }
            
            // Auto-scroll
            thinkingContent.scrollTop = thinkingContent.scrollHeight;
        }

        // Envoyer un message avec streaming
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();
            
            if (!message || isProcessing) {
                return;
            }
            
            isProcessing = true;
            sendBtn.disabled = true;
            sendBtn.textContent = 'Réflexion...';
            
            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            // Réinitialiser la réflexion
            updateThinking('🤔 Préparation de la réflexion...');
            document.getElementById('thinkingIndicator').style.display = 'inline-block';
            updateStatus('Réflexion en cours...', true);
            
            // Démarrer le streaming
            eventSource = new EventSource('/api/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            });
            
            // Alternative avec fetch pour POST
            fetch('/api/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            }).then(response => {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            finishProcessing();
                            return;
                        }
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    handleStreamEvent(data);
                                } catch (e) {
                                    console.log('Parse error:', e);
                                }
                            }
                        }
                        
                        return readStream();
                    });
                }
                
                return readStream();
            }).catch(error => {
                console.error('Erreur streaming:', error);
                addMessage('❌ Erreur de connexion streaming', 'ai', true);
                finishProcessing();
            });
        }
        
        // Gérer les événements du stream
        function handleStreamEvent(data) {
            switch (data.event || 'data') {
                case 'start':
                    updateThinking(data.message);
                    break;
                    
                case 'memory':
                    updateThinking(`\n\n🧠 ${data.message}`, true);
                    document.getElementById('memoryInfo').textContent = 
                        `Mémoire thermique: ${data.temperature?.toFixed(1)}°C • Mémoires: ${data.memories}`;
                    break;
                    
                case 'thinking':
                    updateThinking(`\n\n💭 ${data.message}`, true);
                    break;
                    
                case 'thinking_start':
                    updateThinking(`\n\n💭 === RÉFLEXION INTERNE VISIBLE ===\n`, true);
                    break;
                    
                case 'thinking_content':
                    updateThinking(data.content, true);
                    break;
                    
                case 'thinking_end':
                    updateThinking(`\n=== FIN DE RÉFLEXION ===\n\n✅ ${data.message}`, true);
                    break;
                    
                case 'response':
                    // Ajouter la réponse au chat en temps réel
                    let responseDiv = document.getElementById('currentResponse');
                    if (!responseDiv) {
                        responseDiv = addMessage('', 'ai');
                        responseDiv.id = 'currentResponse';
                    }
                    responseDiv.querySelector('div').textContent += data.content;
                    break;
                    
                case 'complete':
                    updateThinking(`\n\n✅ Réflexion terminée !`, true);
                    // S'assurer que la réponse finale est affichée
                    let finalDiv = document.getElementById('currentResponse');
                    if (finalDiv) {
                        finalDiv.querySelector('div').textContent = data.response;
                        finalDiv.id = '';
                    } else {
                        addMessage(data.response, 'ai');
                    }
                    finishProcessing();
                    break;
                    
                case 'error':
                    updateThinking(`\n\n❌ ${data.message}`, true);
                    addMessage(`❌ ${data.message}`, 'ai', true);
                    finishProcessing();
                    break;
            }
        }
        
        // Terminer le traitement
        function finishProcessing() {
            isProcessing = false;
            document.getElementById('sendBtn').disabled = false;
            document.getElementById('sendBtn').textContent = 'Envoyer';
            document.getElementById('thinkingIndicator').style.display = 'none';
            updateStatus('Prêt à réfléchir...', false);
            document.getElementById('messageInput').focus();
        }

        // Message rapide
        function quickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isProcessing) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
