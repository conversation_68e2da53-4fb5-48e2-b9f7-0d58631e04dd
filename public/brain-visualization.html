<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Visualisation 3D - Cerveau Artificiel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: radial-gradient(circle at center, #0a0a0a 0%, #2a1a2e 50%, #3e1a3e 100%);
            color: #c084fc;
            overflow: hidden;
            height: 100vh;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            border-right: 2px solid #00ff88;
            padding: 20px;
            overflow-y: auto;
        }

        .main-view {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            border: 1px solid #00ff88;
        }

        .header h1 {
            font-size: 1.5em;
            text-shadow: 0 0 15px #00ff88;
            margin-bottom: 5px;
        }

        .brain-canvas {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
            position: relative;
            overflow: hidden;
        }

        .neuron {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            box-shadow: 0 0 15px #00ff88;
            animation: pulse 2s infinite ease-in-out;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .neuron:hover {
            transform: scale(2);
            background: #ffff00;
            box-shadow: 0 0 25px #ffff00;
        }

        .neuron.active {
            background: #ff6b6b;
            box-shadow: 0 0 20px #ff6b6b;
            animation: pulse 0.5s infinite ease-in-out;
        }

        .synapse {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #00ff88, transparent, #00ff88);
            transform-origin: left center;
            opacity: 0.6;
            animation: synapseFlow 3s infinite linear;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        @keyframes synapseFlow {
            0% { opacity: 0.3; }
            50% { opacity: 0.8; }
            100% { opacity: 0.3; }
        }

        .control-panel {
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .control-title {
            font-size: 1.1em;
            margin-bottom: 10px;
            color: #c084fc;
            text-shadow: 0 0 10px #c084fc;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        .stat-label {
            color: #888;
        }

        .stat-value {
            color: #c084fc;
            font-weight: bold;
        }

        .btn {
            width: 100%;
            background: linear-gradient(45deg, #c084fc, #f472b6);
            border: none;
            color: #000;
            padding: 10px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(45deg, #00cc6a, #00ff88);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }

        .temperature-gauge {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .temperature-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #ffff00, #ff6b6b);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .zone-selector {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            margin: 10px 0;
        }

        .zone-btn {
            padding: 8px;
            background: rgba(192, 132, 252, 0.1);
            border: 1px solid #c084fc;
            border-radius: 5px;
            color: #c084fc;
            cursor: pointer;
            text-align: center;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .zone-btn:hover, .zone-btn.active {
            background: rgba(0, 255, 136, 0.3);
            transform: scale(1.05);
        }

        .reflection-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 107, 107, 0.9);
            border: 2px solid #ff6b6b;
            border-radius: 10px;
            padding: 15px;
            color: #fff;
            font-weight: bold;
            text-shadow: 0 0 10px #ff6b6b;
            animation: pulse 2s infinite;
        }

        .neural-pathway {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .pathway-particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #ffff00;
            border-radius: 50%;
            box-shadow: 0 0 8px #ffff00;
            animation: pathwayFlow 2s linear infinite;
        }

        @keyframes pathwayFlow {
            0% { opacity: 0; transform: scale(0); }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
        }

        .info-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 15px;
            max-width: 300px;
            display: none;
        }

        .info-title {
            color: #c084fc;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .info-text {
            color: #ccc;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div style="margin-bottom: 15px;">
                <button onclick="window.location.href='/'" style="background: linear-gradient(45deg, #667eea, #764ba2); border: none; color: white; padding: 8px 16px; border-radius: 15px; cursor: pointer; font-weight: bold; font-size: 0.9em; width: 100%;">🏠 Retour à l'Accueil</button>
            </div>
            <div class="header">
                <h1>🧠 Cerveau IA</h1>
                <p>Visualisation 3D</p>
            </div>

            <div class="control-panel">
                <div class="control-title">📊 Statistiques</div>
                <div class="stat-item">
                    <span class="stat-label">Neurones:</span>
                    <span class="stat-value" id="neuron-count">75</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Synapses:</span>
                    <span class="stat-value" id="synapse-count">225</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Plasticité:</span>
                    <span class="stat-value" id="plasticity">0.95</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Température:</span>
                    <span class="stat-value" id="temperature">78.2°C</span>
                </div>
            </div>

            <div class="control-panel">
                <div class="control-title">🌡️ Température</div>
                <div class="temperature-gauge">
                    <div class="temperature-fill" id="temp-gauge" style="width: 78%"></div>
                </div>
            </div>

            <div class="control-panel">
                <div class="control-title">🎛️ Contrôles</div>
                <button class="btn" onclick="generateNeurons()">🧠 Générer Neurones</button>
                <button class="btn" onclick="activateReflection()">⚡ Activer Réflexion</button>
                <button class="btn" onclick="showPathways()">🔗 Voir Connexions</button>
                <button class="btn" onclick="resetView()">🔄 Réinitialiser</button>
            </div>

            <div class="control-panel">
                <div class="control-title">🧭 Zones Mémoire</div>
                <div class="zone-selector">
                    <div class="zone-btn active" onclick="selectZone('all')">Toutes</div>
                    <div class="zone-btn" onclick="selectZone('instant')">Instantané</div>
                    <div class="zone-btn" onclick="selectZone('short')">Court</div>
                    <div class="zone-btn" onclick="selectZone('working')">Travail</div>
                    <div class="zone-btn" onclick="selectZone('medium')">Moyen</div>
                    <div class="zone-btn" onclick="selectZone('long')">Long</div>
                </div>
            </div>

            <div class="control-panel">
                <div class="control-title">⚡ Boost Réflexion</div>
                <div class="stat-item">
                    <span class="stat-label">Cognitif:</span>
                    <span class="stat-value" id="cognitive-boost">2.7x</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Mémoire:</span>
                    <span class="stat-value" id="memory-boost">2.6x</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Raisonnement:</span>
                    <span class="stat-value" id="reasoning-boost">2.3x</span>
                </div>
            </div>
        </div>

        <div class="main-view">
            <div class="brain-canvas" id="brain-canvas">
                <!-- Neurones et synapses générés dynamiquement -->
            </div>
            
            <div class="reflection-indicator" id="reflection-status">
                🔗 RÉFLEXION CONNECTÉE
                <br>Mémoire Thermique Active
            </div>

            <div class="info-overlay" id="info-overlay">
                <div class="info-title" id="info-title">Neurone #1</div>
                <div class="info-text" id="info-text">
                    Type: Cognitif<br>
                    Activité: 85%<br>
                    Connexions: 12<br>
                    Zone: Travail
                </div>
            </div>
        </div>
    </div>

    <!-- Panneau Chat avec Pensées -->
    <div style="position: fixed; bottom: 20px; right: 20px; width: 400px; background: linear-gradient(135deg, #1a1a1a, #2a2a2a); border-radius: 15px; border: 1px solid #333; z-index: 1000;">
        <div style="padding: 15px; border-bottom: 1px solid #333;">
            <h3 style="color: #fff; margin: 0; font-size: 1em;">💭 Chat avec Pensées</h3>
        </div>
        <div style="padding: 10px;">
            <div style="display: flex; gap: 5px; margin-bottom: 10px;">
                <input type="text" id="chat-input" placeholder="Question..." style="flex: 1; padding: 8px; border: 1px solid #333; border-radius: 5px; background: #1a1a1a; color: white; font-size: 0.9em;">
                <button onclick="sendChatMessage()" style="padding: 8px 12px; background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 5px; color: white; cursor: pointer; font-size: 0.9em;">💬</button>
            </div>
            <div id="chat-responses" style="max-height: 200px; overflow-y: auto; background: #0a0a0a; border: 1px solid #333; border-radius: 5px; padding: 8px; font-size: 0.8em;">
                <div style="color: #888; text-align: center; padding: 10px;">Posez une question...</div>
            </div>
        </div>
    </div>

    <script>
        let neurons = [];
        let synapses = [];
        let currentZone = 'all';
        let isReflectionActive = false;

        // Initialisation du cerveau
        function initBrain() {
            const canvas = document.getElementById('brain-canvas');
            const width = canvas.offsetWidth;
            const height = canvas.offsetHeight;

            // Créer les neurones
            for (let i = 0; i < 75; i++) {
                createNeuron(i, width, height);
            }

            // Créer les synapses
            createSynapses();
            
            // Démarrer l'animation
            startBrainActivity();
        }

        function createNeuron(id, width, height) {
            const neuron = document.createElement('div');
            neuron.className = 'neuron';
            neuron.id = `neuron-${id}`;

            // Position organisée en forme de cerveau plus réaliste
            const zone = Math.floor(Math.random() * 6);
            let x, y;

            // Créer une forme de cerveau plus organique
            const centerX = width / 2;
            const centerY = height / 2;
            const brainWidth = width * 0.8;
            const brainHeight = height * 0.7;

            // Générer position selon la zone cérébrale
            switch(zone) {
                case 0: // Cortex frontal (haut)
                    x = centerX + (Math.random() - 0.5) * brainWidth * 0.8;
                    y = centerY - brainHeight * 0.3 + Math.random() * brainHeight * 0.2;
                    break;
                case 1: // Cortex pariétal (milieu-haut)
                    x = centerX + (Math.random() - 0.5) * brainWidth * 0.9;
                    y = centerY - brainHeight * 0.1 + Math.random() * brainHeight * 0.2;
                    break;
                case 2: // Cortex temporal (côtés)
                    x = centerX + (Math.random() > 0.5 ? 1 : -1) * brainWidth * 0.4 + (Math.random() - 0.5) * brainWidth * 0.2;
                    y = centerY + Math.random() * brainHeight * 0.3;
                    break;
                case 3: // Cortex occipital (arrière)
                    x = centerX + (Math.random() - 0.5) * brainWidth * 0.6;
                    y = centerY + brainHeight * 0.2 + Math.random() * brainHeight * 0.2;
                    break;
                case 4: // Cervelet (bas-arrière)
                    x = centerX + (Math.random() - 0.5) * brainWidth * 0.4;
                    y = centerY + brainHeight * 0.35 + Math.random() * brainHeight * 0.15;
                    break;
                case 5: // Tronc cérébral (centre-bas)
                    x = centerX + (Math.random() - 0.5) * brainWidth * 0.2;
                    y = centerY + brainHeight * 0.25 + Math.random() * brainHeight * 0.2;
                    break;
            }

            // S'assurer que les neurones restent dans les limites
            x = Math.max(25, Math.min(width - 25, x));
            y = Math.max(25, Math.min(height - 25, y));
            
            neuron.style.left = x + 'px';
            neuron.style.top = y + 'px';
            neuron.style.animationDelay = Math.random() * 2 + 's';
            
            // Événements
            neuron.addEventListener('click', () => showNeuronInfo(id, x, y));
            neuron.addEventListener('mouseenter', () => highlightConnections(id));
            neuron.addEventListener('mouseleave', () => clearHighlights());
            
            document.getElementById('brain-canvas').appendChild(neuron);
            
            neurons.push({
                id: id,
                element: neuron,
                x: x,
                y: y,
                zone: zone,
                active: false,
                connections: []
            });
        }

        function createSynapses() {
            const canvas = document.getElementById('brain-canvas');
            
            // Créer des connexions entre neurones proches
            for (let i = 0; i < neurons.length; i++) {
                const neuron1 = neurons[i];
                
                for (let j = i + 1; j < neurons.length; j++) {
                    const neuron2 = neurons[j];
                    const distance = Math.sqrt(
                        Math.pow(neuron1.x - neuron2.x, 2) + 
                        Math.pow(neuron1.y - neuron2.y, 2)
                    );
                    
                    // Créer une synapse si les neurones sont assez proches
                    if (distance < 150 && Math.random() > 0.7) {
                        createSynapse(neuron1, neuron2, canvas);
                        neuron1.connections.push(neuron2.id);
                        neuron2.connections.push(neuron1.id);
                    }
                }
            }
        }

        function createSynapse(neuron1, neuron2, canvas) {
            const synapse = document.createElement('div');
            synapse.className = 'synapse';
            
            const dx = neuron2.x - neuron1.x;
            const dy = neuron2.y - neuron1.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
            
            synapse.style.left = neuron1.x + 'px';
            synapse.style.top = neuron1.y + 'px';
            synapse.style.width = length + 'px';
            synapse.style.transform = `rotate(${angle}deg)`;
            synapse.style.animationDelay = Math.random() * 3 + 's';
            
            canvas.appendChild(synapse);
            synapses.push(synapse);
        }

        function startBrainActivity() {
            setInterval(() => {
                // Activer aléatoirement des neurones
                const randomNeuron = neurons[Math.floor(Math.random() * neurons.length)];
                activateNeuron(randomNeuron);
                
                // Propager l'activation
                setTimeout(() => {
                    propagateActivation(randomNeuron);
                }, 200);
                
            }, 1000 + Math.random() * 2000);
        }

        function activateNeuron(neuron) {
            neuron.element.classList.add('active');
            neuron.active = true;
            
            setTimeout(() => {
                neuron.element.classList.remove('active');
                neuron.active = false;
            }, 1000);
        }

        function propagateActivation(sourceNeuron) {
            sourceNeuron.connections.forEach(connectionId => {
                const targetNeuron = neurons.find(n => n.id === connectionId);
                if (targetNeuron && Math.random() > 0.5) {
                    setTimeout(() => {
                        activateNeuron(targetNeuron);
                    }, Math.random() * 500);
                }
            });
        }

        function generateNeurons() {
            const canvas = document.getElementById('brain-canvas');
            const width = canvas.offsetWidth;
            const height = canvas.offsetHeight;
            
            // Ajouter 5 nouveaux neurones
            for (let i = 0; i < 5; i++) {
                const newId = neurons.length;
                createNeuron(newId, width, height);
            }
            
            // Recréer les synapses
            synapses.forEach(s => s.remove());
            synapses = [];
            createSynapses();
            
            // Mettre à jour les stats
            document.getElementById('neuron-count').textContent = neurons.length;
            document.getElementById('synapse-count').textContent = synapses.length;
        }

        function activateReflection() {
            isReflectionActive = !isReflectionActive;
            const indicator = document.getElementById('reflection-status');
            
            if (isReflectionActive) {
                indicator.style.background = 'rgba(192, 132, 252, 0.9)';
                indicator.style.borderColor = '#c084fc';
                indicator.innerHTML = '⚡ RÉFLEXION BOOST ACTIF<br>Accélération Cognitive';
                
                // Accélérer l'activité neuronale
                neurons.forEach(neuron => {
                    neuron.element.style.animationDuration = '0.5s';
                });
                
                // Créer des particules de pathway
                createPathwayParticles();
                
            } else {
                indicator.style.background = 'rgba(244, 114, 182, 0.9)';
                indicator.style.borderColor = '#f472b6';
                indicator.innerHTML = '🔗 RÉFLEXION CONNECTÉE<br>Mémoire Thermique Active';
                
                // Restaurer la vitesse normale
                neurons.forEach(neuron => {
                    neuron.element.style.animationDuration = '2s';
                });
            }
        }

        function createPathwayParticles() {
            if (!isReflectionActive) return;
            
            const canvas = document.getElementById('brain-canvas');
            
            // Créer des particules le long des synapses
            synapses.forEach(synapse => {
                if (Math.random() > 0.8) {
                    const particle = document.createElement('div');
                    particle.className = 'pathway-particle';
                    
                    const rect = synapse.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();
                    
                    particle.style.left = (rect.left - canvasRect.left) + 'px';
                    particle.style.top = (rect.top - canvasRect.top) + 'px';
                    
                    canvas.appendChild(particle);
                    
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 2000);
                }
            });
            
            // Répéter si la réflexion est active
            if (isReflectionActive) {
                setTimeout(createPathwayParticles, 500);
            }
        }

        function showPathways() {
            // Mettre en évidence toutes les connexions
            synapses.forEach(synapse => {
                synapse.style.opacity = '1';
                synapse.style.background = 'linear-gradient(90deg, #ffff00, #ff6b6b, #ffff00)';
                synapse.style.height = '3px';
            });
            
            setTimeout(() => {
                synapses.forEach(synapse => {
                    synapse.style.opacity = '0.6';
                    synapse.style.background = 'linear-gradient(90deg, #c084fc, transparent, #c084fc)';
                    synapse.style.height = '2px';
                });
            }, 3000);
        }

        function resetView() {
            // Réinitialiser la vue
            currentZone = 'all';
            isReflectionActive = false;
            
            // Réinitialiser les boutons de zone
            document.querySelectorAll('.zone-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.zone-btn').classList.add('active');
            
            // Réinitialiser l'indicateur de réflexion
            const indicator = document.getElementById('reflection-status');
            indicator.style.background = 'rgba(244, 114, 182, 0.9)';
            indicator.style.borderColor = '#f472b6';
            indicator.innerHTML = '🔗 RÉFLEXION CONNECTÉE<br>Mémoire Thermique Active';
            
            // Afficher tous les neurones
            neurons.forEach(neuron => {
                neuron.element.style.display = 'block';
                neuron.element.style.animationDuration = '2s';
            });
        }

        function selectZone(zone) {
            currentZone = zone;
            
            // Mettre à jour les boutons
            document.querySelectorAll('.zone-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filtrer les neurones par zone
            neurons.forEach(neuron => {
                if (zone === 'all' || neuron.zone === getZoneNumber(zone)) {
                    neuron.element.style.display = 'block';
                    neuron.element.style.opacity = '1';
                } else {
                    neuron.element.style.opacity = '0.2';
                }
            });
        }

        function getZoneNumber(zoneName) {
            const zones = {
                'instant': 0,
                'short': 1,
                'working': 2,
                'medium': 3,
                'long': 4,
                'dream': 5
            };
            return zones[zoneName] || 0;
        }

        function showNeuronInfo(id, x, y) {
            const overlay = document.getElementById('info-overlay');
            const neuron = neurons.find(n => n.id === id);
            
            document.getElementById('info-title').textContent = `Neurone #${id}`;
            document.getElementById('info-text').innerHTML = `
                Type: ${getZoneName(neuron.zone)}<br>
                Activité: ${Math.floor(Math.random() * 40 + 60)}%<br>
                Connexions: ${neuron.connections.length}<br>
                Position: (${Math.floor(x)}, ${Math.floor(y)})
            `;
            
            overlay.style.display = 'block';
            
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 3000);
        }

        function getZoneName(zoneNumber) {
            const names = ['Instantané', 'Court Terme', 'Travail', 'Moyen Terme', 'Long Terme', 'Rêves'];
            return names[zoneNumber] || 'Inconnu';
        }

        function highlightConnections(neuronId) {
            const neuron = neurons.find(n => n.id === neuronId);
            
            // Mettre en évidence les connexions
            neuron.connections.forEach(connectionId => {
                const connectedNeuron = neurons.find(n => n.id === connectionId);
                if (connectedNeuron) {
                    connectedNeuron.element.style.background = '#ffff00';
                    connectedNeuron.element.style.boxShadow = '0 0 20px #ffff00';
                }
            });
        }

        function clearHighlights() {
            neurons.forEach(neuron => {
                neuron.element.style.background = '#c084fc';
                neuron.element.style.boxShadow = '0 0 15px #c084fc';
            });
        }

        // Mise à jour automatique des métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success && data.metrics) {
                    if (data.metrics.brainStats) {
                        document.getElementById('neuron-count').textContent = data.metrics.brainStats.activeNeurons || 75;
                        document.getElementById('synapse-count').textContent = data.metrics.brainStats.synapticConnections || 225;
                        document.getElementById('plasticity').textContent = (data.metrics.brainStats.synapticPlasticity || 0.95).toFixed(2);
                    }
                    
                    if (data.metrics.memoryStats) {
                        const temp = data.metrics.memoryStats.globalTemperature || 78.2;
                        document.getElementById('temperature').textContent = temp.toFixed(1) + '°C';
                        document.getElementById('temp-gauge').style.width = Math.min(100, temp * 1.2) + '%';
                    }
                }
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        // Initialisation
        window.addEventListener('load', () => {
            initBrain();
            updateMetrics();
            setInterval(updateMetrics, 5000);
        });

        // Redimensionnement
        window.addEventListener('resize', () => {
            setTimeout(() => {
                // Réinitialiser le cerveau avec les nouvelles dimensions
                document.getElementById('brain-canvas').innerHTML = '';
                neurons = [];
                synapses = [];
                initBrain();
            }, 100);
        });

        // Fonction de chat avec pensées
        async function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;

            const chatContainer = document.getElementById('chat-responses');
            input.value = '';
            input.disabled = true;

            // Afficher la question
            chatContainer.innerHTML += `
                <div style="margin-bottom: 8px; padding: 6px; background: #1a1a1a; border-radius: 3px; border-left: 2px solid #667eea;">
                    <strong>🤔 Vous:</strong> ${message}
                </div>
            `;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    // Extraire les pensées de la balise <think>
                    const thinkMatch = data.response.match(/<think>([\s\S]*?)<\/think>/);
                    const thoughts = thinkMatch ? thinkMatch[1].trim() : 'Aucune réflexion interne détectée';

                    // Extraire la réponse finale
                    const finalResponse = data.response.replace(/<think>[\s\S]*?<\/think>/, '').trim();

                    // Afficher les pensées internes (version compacte)
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 6px; padding: 6px; background: #2a1a2a; border-radius: 3px; border-left: 2px solid #764ba2;">
                            <strong>🧠 Pensées:</strong>
                            <div style="margin-top: 3px; font-style: italic; color: #ccc; font-size: 0.85em; max-height: 60px; overflow-y: auto;">${thoughts.substring(0, 200)}${thoughts.length > 200 ? '...' : ''}</div>
                        </div>
                    `;

                    // Afficher la réponse finale
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 8px; padding: 6px; background: #1a2a1a; border-radius: 3px; border-left: 2px solid #4ade80;">
                            <strong>🤖 Louna:</strong>
                            <div style="margin-top: 3px;">${finalResponse.substring(0, 150)}${finalResponse.length > 150 ? '...' : ''}</div>
                            <div style="margin-top: 5px; font-size: 0.7em; color: #888;">
                                QI: ${data.iqAnalysis?.agentIQ || 'N/A'} | ${data.iqAnalysis?.memoryIQ || 'N/A'} | ${data.iqAnalysis?.combinedIQ || 'N/A'}
                            </div>
                        </div>
                    `;
                } else {
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 8px; padding: 6px; background: #2a1a1a; border-radius: 3px; border-left: 2px solid #ef4444;">
                            <strong>❌ Erreur:</strong> ${data.error || 'Erreur inconnue'}
                        </div>
                    `;
                }
            } catch (error) {
                chatContainer.innerHTML += `
                    <div style="margin-bottom: 8px; padding: 6px; background: #2a1a1a; border-radius: 3px; border-left: 2px solid #ef4444;">
                        <strong>❌ Erreur:</strong> ${error.message}
                    </div>
                `;
            }

            input.disabled = false;
            input.focus();
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Permettre l'envoi avec Entrée
        document.addEventListener('DOMContentLoaded', () => {
            const input = document.getElementById('chat-input');
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendChatMessage();
                    }
                });
            }
        });
    </script>
</body>
</html>
