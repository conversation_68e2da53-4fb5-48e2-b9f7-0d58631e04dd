<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reconnaissance Faciale - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .camera-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .camera-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 4/3;
            margin-bottom: 20px;
        }

        .camera-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .face-box {
            position: absolute;
            border: 2px solid #ff69b4;
            border-radius: 5px;
            background: rgba(255, 105, 180, 0.1);
        }

        .face-label {
            position: absolute;
            top: -25px;
            left: 0;
            background: #ff69b4;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
        }

        .camera-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #888;
            background: linear-gradient(45deg, #333, #555);
        }

        .camera-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .camera-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .control-btn {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .control-btn.secondary {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .control-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .control-btn.danger:hover {
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }

        .status-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .status-text {
            font-size: 14px;
            color: #ccc;
        }

        .recognition-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .faces-list {
            flex: 1;
            overflow-y: auto;
            max-height: 400px;
        }

        .face-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .face-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .face-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
        }

        .face-info {
            flex: 1;
        }

        .face-name {
            font-size: 16px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 5px;
        }

        .face-details {
            font-size: 12px;
            color: #ccc;
        }

        .face-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 10px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .settings-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .setting-label {
            font-size: 14px;
            color: #ccc;
        }

        .setting-toggle {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .setting-toggle.active {
            background: #ff69b4;
        }

        .setting-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .setting-toggle.active::after {
            left: 27px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        /* Section Chat */
        .chat-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            max-height: calc(100vh - 140px);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            max-height: 400px;
        }

        .chat-message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .chat-message.user {
            flex-direction: row-reverse;
        }

        .chat-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .chat-avatar.user {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
        }

        .chat-avatar.agent {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            color: white;
        }

        .chat-bubble {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .chat-message.user .chat-bubble {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .chat-message.agent .chat-bubble {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #ff69b4;
            box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border: none;
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .chat-send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .chat-send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .object-recognition {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .object-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .object-item:last-child {
            border-bottom: none;
        }

        .object-name {
            font-weight: 600;
            color: #ff69b4;
        }

        .object-confidence {
            font-size: 12px;
            color: #4caf50;
            background: rgba(76, 175, 80, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 5px;
            padding: 10px 15px;
            margin-bottom: 10px;
        }

        .typing-indicator.show {
            display: flex;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff69b4;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .chat-section {
                grid-column: 1 / -1;
                max-height: 300px;
            }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-user-check"></i>
            Reconnaissance Faciale
        </h1>
        <div class="nav-buttons">
            <a href="/chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="/image-generator-simple.html" class="nav-btn">
                <i class="fas fa-image"></i>
                Images
            </a>
            <a href="/web-search.html" class="nav-btn">
                <i class="fas fa-search"></i>
                Recherche
            </a>
            <a href="/brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="/" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Section caméra -->
        <div class="camera-section">
            <div class="section-title">
                <i class="fas fa-camera"></i>
                Caméra et Détection
            </div>

            <div class="camera-container">
                <video class="camera-video" id="cameraVideo" autoplay muted style="display: none;"></video>
                <canvas class="camera-overlay" id="cameraOverlay"></canvas>
                <div class="camera-placeholder" id="cameraPlaceholder">
                    <i class="fas fa-camera"></i>
                    <p>Caméra non activée</p>
                    <p>Cliquez sur "Démarrer" pour commencer</p>
                </div>
            </div>

            <div class="camera-controls">
                <button class="control-btn" id="startBtn" onclick="startCamera()">
                    <i class="fas fa-play"></i>
                    Démarrer
                </button>
                <button class="control-btn secondary" id="captureBtn" onclick="capturePhoto()" disabled>
                    <i class="fas fa-camera"></i>
                    Capturer
                </button>
                <button class="control-btn danger" id="stopBtn" onclick="stopCamera()" disabled>
                    <i class="fas fa-stop"></i>
                    Arrêter
                </button>
            </div>

            <div class="status-display" id="statusDisplay">
                <div class="status-icon">
                    <i class="fas fa-info-circle" style="color: #2196f3;"></i>
                </div>
                <div class="status-text">Système prêt - Cliquez sur Démarrer</div>
            </div>

            <div class="settings-section">
                <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                    <i class="fas fa-cog"></i>
                    Paramètres
                </div>
                <div class="setting-item">
                    <span class="setting-label">Détection automatique</span>
                    <div class="setting-toggle active" onclick="toggleSetting(this)" data-setting="autoDetect"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Sauvegarde automatique</span>
                    <div class="setting-toggle" onclick="toggleSetting(this)" data-setting="autoSave"></div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Notifications sonores</span>
                    <div class="setting-toggle" onclick="toggleSetting(this)" data-setting="soundNotif"></div>
                </div>
            </div>
        </div>

        <!-- Section reconnaissance -->
        <div class="recognition-section">
            <div class="section-title">
                <i class="fas fa-users"></i>
                Visages Reconnus
            </div>

            <div class="faces-list" id="facesList">
                <div class="face-item">
                    <div class="face-avatar">JL</div>
                    <div class="face-info">
                        <div class="face-name">Jean-Luc Passave</div>
                        <div class="face-details">Créateur • Dernière vue: Maintenant • Confiance: 98%</div>
                    </div>
                    <div class="face-actions">
                        <button class="action-btn" onclick="editFace('jean-luc')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn" onclick="deleteFace('jean-luc')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="face-item">
                    <div class="face-avatar">?</div>
                    <div class="face-info">
                        <div class="face-name">Visage Inconnu #1</div>
                        <div class="face-details">Détecté: Il y a 5 min • Confiance: 85%</div>
                    </div>
                    <div class="face-actions">
                        <button class="action-btn" onclick="identifyFace('unknown-1')">
                            <i class="fas fa-user-plus"></i>
                        </button>
                        <button class="action-btn" onclick="ignoreFace('unknown-1')">
                            <i class="fas fa-eye-slash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="camera-controls" style="margin-top: 20px;">
                <button class="control-btn secondary" onclick="addNewFace()">
                    <i class="fas fa-user-plus"></i>
                    Ajouter Visage
                </button>
                <button class="control-btn" onclick="trainModel()">
                    <i class="fas fa-brain"></i>
                    Entraîner Modèle
                </button>
            </div>
        </div>

        <!-- Section Chat avec Reconnaissance d'Objets -->
        <div class="chat-section">
            <div class="section-title">
                <i class="fas fa-comments"></i>
                Chat avec Reconnaissance d'Objets
            </div>

            <!-- Reconnaissance d'objets -->
            <div class="object-recognition">
                <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                    <i class="fas fa-eye"></i>
                    Objets Détectés
                </div>
                <div id="objectsList">
                    <div class="object-item">
                        <span class="object-name">Aucun objet détecté</span>
                        <span class="object-confidence">--</span>
                    </div>
                </div>
            </div>

            <!-- Messages de chat -->
            <div class="chat-messages" id="chatMessages">
                <div class="chat-message agent">
                    <div class="chat-avatar agent">L</div>
                    <div class="chat-bubble">
                        Bonjour Jean-Luc ! Je suis Louna, votre assistant IA. Montrez-moi des objets devant la caméra et je les reconnaîtrai pour vous ! Vous pouvez aussi me poser des questions sur ce que je vois.
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <div class="chat-avatar agent">L</div>
                    <div class="chat-bubble">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <!-- Zone de saisie -->
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="Demandez-moi ce que je vois ou posez une question..." autocomplete="off">
                <button class="chat-send-btn" id="chatSendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let cameraStream = null;
        let isDetecting = false;
        let detectionInterval = null;
        let recognizedFaces = [];
        let detectedObjects = [];
        let chatMessages = [];
        let isTyping = false;
        let settings = {
            autoDetect: true,
            autoSave: false,
            soundNotif: false
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('👁️ Système de reconnaissance faciale initialisé');
            loadSettings();
            loadRecognizedFaces();
            initializeChat();
            startObjectDetection();
        });

        async function startCamera() {
            try {
                updateStatus('Démarrage de la caméra...', 'info');

                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                };

                cameraStream = await navigator.mediaDevices.getUserMedia(constraints);

                const video = document.getElementById('cameraVideo');
                const placeholder = document.getElementById('cameraPlaceholder');

                video.srcObject = cameraStream;
                video.style.display = 'block';
                placeholder.style.display = 'none';

                // Mettre à jour les boutons
                document.getElementById('startBtn').disabled = true;
                document.getElementById('captureBtn').disabled = false;
                document.getElementById('stopBtn').disabled = false;

                updateStatus('Caméra active - Détection en cours', 'success');

                // Démarrer la détection si activée
                if (settings.autoDetect) {
                    startDetection();
                }

                showNotification('Caméra démarrée avec succès', 'success');

            } catch (error) {
                console.error('Erreur accès caméra:', error);
                updateStatus('Erreur: Impossible d\'accéder à la caméra', 'error');
                showNotification('Erreur d\'accès à la caméra', 'error');
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }

            stopDetection();

            const video = document.getElementById('cameraVideo');
            const placeholder = document.getElementById('cameraPlaceholder');

            video.style.display = 'none';
            placeholder.style.display = 'flex';

            // Mettre à jour les boutons
            document.getElementById('startBtn').disabled = false;
            document.getElementById('captureBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;

            updateStatus('Caméra arrêtée', 'info');
            showNotification('Caméra arrêtée', 'warning');
        }

        function startDetection() {
            if (isDetecting) return;

            isDetecting = true;
            detectionInterval = setInterval(() => {
                detectFaces();
            }, 1000); // Détection chaque seconde

            console.log('🔍 Détection faciale démarrée');
        }

        function stopDetection() {
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
            isDetecting = false;

            console.log('⏹️ Détection faciale arrêtée');
        }

        function detectFaces() {
            // Simulation de détection faciale
            const video = document.getElementById('cameraVideo');
            if (!video.srcObject) return;

            // Simuler la détection de Jean-Luc Passave
            if (Math.random() > 0.7) {
                const faceData = {
                    name: 'Jean-Luc Passave',
                    confidence: 95 + Math.random() * 5,
                    timestamp: new Date().toISOString(),
                    location: 'Sainte-Anne, Guadeloupe'
                };

                onFaceDetected(faceData);
            }
        }

        function onFaceDetected(faceData) {
            console.log('👤 Visage détecté:', faceData);

            // Mettre à jour le statut
            updateStatus(`Visage reconnu: ${faceData.name} (${Math.round(faceData.confidence)}%)`, 'success');

            // Ajouter à la mémoire thermique si disponible
            addToThermalMemory(faceData);

            // Notification sonore si activée
            if (settings.soundNotif) {
                playNotificationSound();
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraVideo');
            if (!video.srcObject) return;

            // Créer un canvas pour capturer l'image
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convertir en blob et sauvegarder
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `capture_${Date.now()}.jpg`;
                a.click();

                showNotification('Photo capturée et téléchargée', 'success');
            }, 'image/jpeg', 0.9);
        }

        function updateStatus(message, type) {
            const statusDisplay = document.getElementById('statusDisplay');
            const icon = statusDisplay.querySelector('.status-icon i');
            const text = statusDisplay.querySelector('.status-text');

            text.textContent = message;

            // Mettre à jour l'icône selon le type
            icon.className = `fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}`;
            icon.style.color = type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3';
        }

        function toggleSetting(toggle) {
            const setting = toggle.dataset.setting;
            const isActive = toggle.classList.contains('active');

            if (isActive) {
                toggle.classList.remove('active');
                settings[setting] = false;
            } else {
                toggle.classList.add('active');
                settings[setting] = true;
            }

            saveSettings();

            // Actions spécifiques selon le paramètre
            if (setting === 'autoDetect') {
                if (settings[setting] && cameraStream) {
                    startDetection();
                } else {
                    stopDetection();
                }
            }
        }

        function addNewFace() {
            const name = prompt('Nom de la personne:');
            if (name) {
                const newFace = {
                    id: Date.now(),
                    name: name,
                    confidence: 100,
                    timestamp: new Date().toISOString(),
                    isNew: true
                };

                recognizedFaces.unshift(newFace);
                updateFacesList();
                saveRecognizedFaces();

                showNotification(`Visage "${name}" ajouté`, 'success');
            }
        }

        function trainModel() {
            showNotification('Entraînement du modèle en cours...', 'warning');

            // Simuler l'entraînement
            setTimeout(() => {
                showNotification('Modèle entraîné avec succès!', 'success');
            }, 3000);
        }

        function editFace(faceId) {
            const newName = prompt('Nouveau nom pour ce visage:');
            if (newName && newName.trim()) {
                // Mettre à jour le nom dans la liste
                const faceElement = document.querySelector(`[onclick="editFace('${faceId}')"]`).closest('.face-item');
                const nameElement = faceElement.querySelector('.face-name');
                const oldName = nameElement.textContent;
                nameElement.textContent = newName.trim();

                // Mettre à jour les détails
                const detailsElement = faceElement.querySelector('.face-details');
                detailsElement.textContent = detailsElement.textContent.replace(oldName, newName.trim());

                // Sauvegarder dans localStorage
                let savedFaces = JSON.parse(localStorage.getItem('lounaRecognizedFaces') || '[]');
                const faceIndex = savedFaces.findIndex(face => face.id === faceId);
                if (faceIndex !== -1) {
                    savedFaces[faceIndex].name = newName.trim();
                    savedFaces[faceIndex].lastModified = new Date().toISOString();
                    localStorage.setItem('lounaRecognizedFaces', JSON.stringify(savedFaces));
                }

                showNotification(`Visage renommé: "${oldName}" → "${newName.trim()}"`, 'success');

                // Ajouter à la mémoire thermique
                addToThermalMemory({
                    action: 'face_renamed',
                    oldName: oldName,
                    newName: newName.trim(),
                    faceId: faceId,
                    timestamp: new Date().toISOString()
                });
            }
        }

        function deleteFace(faceId) {
            const faceElement = document.querySelector(`[onclick="deleteFace('${faceId}')"]`).closest('.face-item');
            const faceName = faceElement.querySelector('.face-name').textContent;

            if (confirm(`Supprimer "${faceName}" de la base de données?\n\nCette action est irréversible.`)) {
                // Supprimer de l'interface
                faceElement.style.transition = 'all 0.3s ease';
                faceElement.style.transform = 'translateX(-100%)';
                faceElement.style.opacity = '0';

                setTimeout(() => {
                    faceElement.remove();
                }, 300);

                // Supprimer du localStorage
                let savedFaces = JSON.parse(localStorage.getItem('lounaRecognizedFaces') || '[]');
                savedFaces = savedFaces.filter(face => face.id !== faceId);
                localStorage.setItem('lounaRecognizedFaces', JSON.stringify(savedFaces));

                // Supprimer de la liste globale
                recognizedFaces = recognizedFaces.filter(face => face.id !== faceId);

                showNotification(`Visage "${faceName}" supprimé de la base de données`, 'warning');

                // Ajouter à la mémoire thermique
                addToThermalMemory({
                    action: 'face_deleted',
                    faceName: faceName,
                    faceId: faceId,
                    timestamp: new Date().toISOString()
                });

                // Mettre à jour les statistiques
                updateFacesList();
            }
        }

        function identifyFace(faceId) {
            const name = prompt('Identifier ce visage:');
            if (name && name.trim()) {
                const faceElement = document.querySelector(`[onclick="identifyFace('${faceId}')"]`).closest('.face-item');
                const nameElement = faceElement.querySelector('.face-name');
                const detailsElement = faceElement.querySelector('.face-details');
                const avatarElement = faceElement.querySelector('.face-avatar');

                // Mettre à jour l'interface
                nameElement.textContent = name.trim();
                detailsElement.textContent = `Identifié • Confiance: 95% • ${new Date().toLocaleTimeString()}`;
                avatarElement.textContent = name.trim().substring(0, 2).toUpperCase();
                avatarElement.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';

                // Ajouter à la base de données
                const newFace = {
                    id: faceId,
                    name: name.trim(),
                    confidence: 95,
                    timestamp: new Date().toISOString(),
                    status: 'identified',
                    avatar: name.trim().substring(0, 2).toUpperCase()
                };

                recognizedFaces.push(newFace);
                saveRecognizedFaces();

                // Changer les boutons d'action
                const actionsDiv = faceElement.querySelector('.face-actions');
                actionsDiv.innerHTML = `
                    <button class="action-btn" onclick="editFace('${faceId}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn" onclick="deleteFace('${faceId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;

                showNotification(`Visage identifié comme "${name.trim()}"`, 'success');

                // Ajouter à la mémoire thermique
                addToThermalMemory({
                    action: 'face_identified',
                    faceName: name.trim(),
                    faceId: faceId,
                    confidence: 95,
                    timestamp: new Date().toISOString()
                });
            }
        }

        function ignoreFace(faceId) {
            const faceElement = document.querySelector(`[onclick="ignoreFace('${faceId}')"]`).closest('.face-item');
            const faceName = faceElement.querySelector('.face-name').textContent;

            if (confirm(`Ignorer "${faceName}"?\n\nCe visage ne sera plus détecté automatiquement.`)) {
                // Ajouter à la liste des visages ignorés
                let ignoredFaces = JSON.parse(localStorage.getItem('lounaIgnoredFaces') || '[]');
                ignoredFaces.push({
                    id: faceId,
                    name: faceName,
                    timestamp: new Date().toISOString(),
                    reason: 'user_ignored'
                });
                localStorage.setItem('lounaIgnoredFaces', JSON.stringify(ignoredFaces));

                // Supprimer de l'interface avec animation
                faceElement.style.transition = 'all 0.3s ease';
                faceElement.style.transform = 'scale(0.8)';
                faceElement.style.opacity = '0.3';

                setTimeout(() => {
                    faceElement.remove();
                }, 300);

                showNotification(`Visage "${faceName}" ignoré`, 'warning');

                // Ajouter à la mémoire thermique
                addToThermalMemory({
                    action: 'face_ignored',
                    faceName: faceName,
                    faceId: faceId,
                    timestamp: new Date().toISOString()
                });
            }
        }

        function updateFacesList() {
            // Mise à jour de la liste des visages (simulation)
            console.log('📝 Mise à jour liste des visages');
        }

        function addToThermalMemory(faceData) {
            try {
                // Ajouter à la mémoire thermique locale
                let thermalMemory = JSON.parse(localStorage.getItem('lounaThermalMemory') || '[]');

                const memoryEntry = {
                    id: `face_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    type: 'facial_recognition',
                    content: faceData,
                    temperature: 0.8, // Température élevée pour les événements de reconnaissance
                    timestamp: new Date().toISOString(),
                    source: 'face_recognition_module',
                    importance: faceData.action === 'face_identified' ? 0.9 : 0.6
                };

                thermalMemory.unshift(memoryEntry); // Ajouter au début

                // Limiter à 1000 entrées maximum
                if (thermalMemory.length > 1000) {
                    thermalMemory = thermalMemory.slice(0, 1000);
                }

                localStorage.setItem('lounaThermalMemory', JSON.stringify(thermalMemory));

                // Essayer d'envoyer à l'API si disponible
                fetch('/api/thermal-memory/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: JSON.stringify(faceData),
                        zone: 'working_memory',
                        source: 'face_recognition'
                    })
                }).then(response => {
                    if (response.ok) {
                        console.log('✅ Ajouté à la mémoire thermique serveur');
                    }
                }).catch(error => {
                    console.log('⚠️ Mémoire thermique locale uniquement');
                });

                console.log('💾 Ajout à la mémoire thermique:', memoryEntry);

            } catch (error) {
                console.error('❌ Erreur ajout mémoire thermique:', error);
            }
        }

        function playNotificationSound() {
            // Simulation de notification sonore
            console.log('🔊 Notification sonore');
        }

        function saveSettings() {
            localStorage.setItem('lounaFaceRecognitionSettings', JSON.stringify(settings));
        }

        function loadSettings() {
            const saved = localStorage.getItem('lounaFaceRecognitionSettings');
            if (saved) {
                settings = { ...settings, ...JSON.parse(saved) };

                // Appliquer les paramètres à l'interface
                Object.keys(settings).forEach(key => {
                    const toggle = document.querySelector(`[data-setting="${key}"]`);
                    if (toggle) {
                        if (settings[key]) {
                            toggle.classList.add('active');
                        } else {
                            toggle.classList.remove('active');
                        }
                    }
                });
            }
        }

        function saveRecognizedFaces() {
            localStorage.setItem('lounaRecognizedFaces', JSON.stringify(recognizedFaces));
        }

        function loadRecognizedFaces() {
            try {
                // Charger depuis localStorage
                const saved = localStorage.getItem('lounaRecognizedFaces');
                if (saved) {
                    recognizedFaces = JSON.parse(saved);
                    console.log('📂 Chargé', recognizedFaces.length, 'visages depuis localStorage');
                }

                // Essayer de charger depuis le serveur
                fetch('/api/face-recognition/load')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.faces) {
                            // Fusionner avec les données locales
                            const serverFaces = data.faces;
                            const mergedFaces = [...recognizedFaces];

                            serverFaces.forEach(serverFace => {
                                if (!mergedFaces.find(face => face.id === serverFace.id)) {
                                    mergedFaces.push(serverFace);
                                }
                            });

                            recognizedFaces = mergedFaces;
                            console.log('🌐 Données serveur fusionnées:', recognizedFaces.length, 'visages total');
                        }

                        updateFacesList();
                    })
                    .catch(error => {
                        console.log('⚠️ Utilisation des données locales uniquement');
                        updateFacesList();
                    });

                // Ajouter quelques visages de démonstration si aucun n'existe
                if (recognizedFaces.length === 0) {
                    recognizedFaces = [
                        {
                            id: 'demo_jean_luc',
                            name: 'Jean-Luc Passave',
                            confidence: 98,
                            timestamp: new Date().toISOString(),
                            status: 'identified',
                            avatar: 'JL',
                            details: 'Créateur de Louna • QI 225 • Sainte-Anne, Guadeloupe'
                        }
                    ];
                    saveRecognizedFaces();
                }

            } catch (error) {
                console.error('❌ Erreur chargement:', error);
                recognizedFaces = [];
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // === FONCTIONS DE CHAT ET RECONNAISSANCE D'OBJETS ===

        function initializeChat() {
            const chatInput = document.getElementById('chatInput');
            const chatSendBtn = document.getElementById('chatSendBtn');

            // Événements de chat
            chatSendBtn.addEventListener('click', sendChatMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });

            console.log('💬 Chat initialisé avec reconnaissance d\'objets');
        }

        function sendChatMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message || isTyping) return;

            // Ajouter le message de l'utilisateur
            addChatMessage(message, 'user');
            chatInput.value = '';

            // Simuler la réponse de l'agent
            showTypingIndicator();

            setTimeout(() => {
                hideTypingIndicator();
                generateAgentResponse(message);
            }, 1500 + Math.random() * 1000);
        }

        function addChatMessage(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = `chat-message ${sender}`;

            const avatar = sender === 'user' ? 'JL' : 'L';
            const avatarClass = sender === 'user' ? 'user' : 'agent';

            messageElement.innerHTML = `
                <div class="chat-avatar ${avatarClass}">${avatar}</div>
                <div class="chat-bubble">${message}</div>
            `;

            // Insérer avant l'indicateur de frappe
            const typingIndicator = document.getElementById('typingIndicator');
            chatMessages.insertBefore(messageElement, typingIndicator);

            // Faire défiler vers le bas
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Ajouter à l'historique
            chatMessages.push({ message, sender, timestamp: new Date().toISOString() });
        }

        function generateAgentResponse(userMessage) {
            let response = '';

            // Analyser le message pour générer une réponse contextuelle
            const lowerMessage = userMessage.toLowerCase();

            if (lowerMessage.includes('objet') || lowerMessage.includes('voir') || lowerMessage.includes('reconnaître')) {
                if (detectedObjects.length > 0) {
                    const objectNames = detectedObjects.map(obj => obj.name).join(', ');
                    response = `Je vois actuellement ces objets : ${objectNames}. ${generateObjectDetails()}`;
                } else {
                    response = 'Je ne détecte aucun objet spécifique en ce moment. Montrez-moi quelque chose devant la caméra et je l\'analyserai pour vous !';
                }
            } else if (lowerMessage.includes('qui') || lowerMessage.includes('visage') || lowerMessage.includes('personne')) {
                response = 'Je peux reconnaître Jean-Luc Passave, le créateur de Louna, avec une confiance de 98%. C\'est un plaisir de vous voir !';
            } else if (lowerMessage.includes('comment') || lowerMessage.includes('ça va')) {
                response = 'Je vais très bien, merci ! Mon système de reconnaissance fonctionne parfaitement. Mon QI est de 225 et tous mes systèmes sont opérationnels. Comment puis-je vous aider aujourd\'hui ?';
            } else if (lowerMessage.includes('couleur')) {
                response = 'Je peux analyser les couleurs des objets que vous me montrez. Présentez un objet devant la caméra et je vous dirai sa couleur dominante !';
            } else if (lowerMessage.includes('taille') || lowerMessage.includes('dimension')) {
                response = 'Je peux estimer la taille relative des objets par rapport au cadre de la caméra. Plus l\'objet est proche, plus il paraît grand !';
            } else {
                // Réponse générale intelligente
                const responses = [
                    'C\'est une question intéressante ! En tant qu\'IA de reconnaissance, je peux analyser tout ce que vous me montrez.',
                    'Je suis là pour vous aider avec la reconnaissance d\'objets et de visages. Que souhaitez-vous que j\'analyse ?',
                    'Mon système de vision artificielle est prêt ! Montrez-moi quelque chose et je vous dirai ce que c\'est.',
                    'Grâce à mes capacités de reconnaissance, je peux identifier de nombreux objets et personnes. Essayez-moi !',
                    'Je combine reconnaissance faciale et détection d\'objets pour vous offrir une expérience complète. Que puis-je analyser pour vous ?'
                ];
                response = responses[Math.floor(Math.random() * responses.length)];
            }

            addChatMessage(response, 'agent');
        }

        function generateObjectDetails() {
            if (detectedObjects.length === 0) return '';

            const details = detectedObjects.map(obj => {
                return `${obj.name} (confiance: ${obj.confidence}%)`;
            }).join(', ');

            return `Détails: ${details}.`;
        }

        function showTypingIndicator() {
            isTyping = true;
            const typingIndicator = document.getElementById('typingIndicator');
            typingIndicator.classList.add('show');

            // Faire défiler vers le bas
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            isTyping = false;
            const typingIndicator = document.getElementById('typingIndicator');
            typingIndicator.classList.remove('show');
        }

        function startObjectDetection() {
            // Simuler la détection d'objets en continu
            setInterval(() => {
                if (cameraStream) {
                    detectObjects();
                }
            }, 3000); // Détection toutes les 3 secondes

            console.log('👁️ Détection d\'objets démarrée');
        }

        function detectObjects() {
            // Simulation de détection d'objets
            const possibleObjects = [
                { name: 'Ordinateur portable', confidence: 92 },
                { name: 'Téléphone', confidence: 88 },
                { name: 'Livre', confidence: 85 },
                { name: 'Tasse', confidence: 90 },
                { name: 'Clavier', confidence: 94 },
                { name: 'Souris', confidence: 89 },
                { name: 'Stylo', confidence: 82 },
                { name: 'Lunettes', confidence: 87 },
                { name: 'Montre', confidence: 91 },
                { name: 'Plante', confidence: 86 }
            ];

            // Simuler la détection aléatoire
            if (Math.random() > 0.6) {
                const randomObject = possibleObjects[Math.floor(Math.random() * possibleObjects.length)];

                // Vérifier si l'objet n'est pas déjà détecté
                if (!detectedObjects.find(obj => obj.name === randomObject.name)) {
                    detectedObjects.push({
                        ...randomObject,
                        timestamp: new Date().toISOString(),
                        id: Date.now()
                    });

                    // Limiter à 3 objets maximum
                    if (detectedObjects.length > 3) {
                        detectedObjects.shift();
                    }

                    updateObjectsList();

                    // Notification dans le chat
                    if (detectedObjects.length === 1) {
                        setTimeout(() => {
                            addChatMessage(`Je détecte un nouvel objet : ${randomObject.name} avec ${randomObject.confidence}% de confiance !`, 'agent');
                        }, 500);
                    }
                }
            } else {
                // Parfois, retirer un objet (simulation de sortie du champ)
                if (detectedObjects.length > 0 && Math.random() > 0.8) {
                    const removedObject = detectedObjects.pop();
                    updateObjectsList();

                    setTimeout(() => {
                        addChatMessage(`L'objet "${removedObject.name}" n'est plus visible dans le champ de la caméra.`, 'agent');
                    }, 500);
                }
            }
        }

        function updateObjectsList() {
            const objectsList = document.getElementById('objectsList');

            if (detectedObjects.length === 0) {
                objectsList.innerHTML = `
                    <div class="object-item">
                        <span class="object-name">Aucun objet détecté</span>
                        <span class="object-confidence">--</span>
                    </div>
                `;
                return;
            }

            objectsList.innerHTML = detectedObjects.map(obj => `
                <div class="object-item">
                    <span class="object-name">${obj.name}</span>
                    <span class="object-confidence">${obj.confidence}%</span>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
