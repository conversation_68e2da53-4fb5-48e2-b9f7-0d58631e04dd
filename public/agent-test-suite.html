<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suite de Tests Agent - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .test-card:hover {
            border-color: #ff69b4;
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
        }

        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-question {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #ff69b4;
        }

        .test-btn {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }

        .test-btn:hover {
            background: linear-gradient(135deg, #ff1493, #dc143c);
            transform: translateY(-2px);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .test-result {
            background: rgba(0, 0, 0, 0.4);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            min-height: 100px;
            border: 2px solid transparent;
        }

        .test-result.success {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }

        .test-result.error {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }

        .test-result.loading {
            border-color: #ff69b4;
            background: rgba(255, 105, 180, 0.1);
        }

        .global-controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .global-btn {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            margin: 0 10px;
            font-size: 1.1rem;
        }

        .global-btn:hover {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
        }

        .qi-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #ff69b4;
            z-index: 1000;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ff69b4;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-btn {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Affichage QI en temps réel -->
    <div class="qi-display">
        <div><strong>QI Actuel:</strong> <span id="current-qi">225</span></div>
        <div><strong>Statut:</strong> <span id="qi-status">Évolutif</span></div>
    </div>

    <div class="header">
        <h1><i class="fas fa-robot"></i> Suite de Tests Agent Louna</h1>
        <p>Tests complets de l'agent IA sur toutes les interfaces</p>
        <div style="margin-top: 15px;">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/chat-cognitif-complet.html" class="nav-btn"><i class="fas fa-brain"></i> Chat Cognitif</a>
            <a href="/chat-agents.html" class="nav-btn"><i class="fas fa-robot"></i> Chat Agents</a>
        </div>
    </div>

    <div class="global-controls">
        <h3>Contrôles Globaux</h3>
        <button class="global-btn" onclick="runAllTests()">
            <i class="fas fa-play"></i> Lancer Tous les Tests
        </button>
        <button class="global-btn" onclick="testQIEvolution()">
            <i class="fas fa-brain"></i> Tester Évolution QI
        </button>
        <button class="global-btn" onclick="clearAllResults()">
            <i class="fas fa-trash"></i> Effacer Résultats
        </button>
    </div>

    <div class="test-grid">
        <!-- Test 1: Questions Complexes Guadeloupe -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-map-marked-alt"></i>
                Test Guadeloupe Complexe
            </div>
            <div class="test-question">
                "Explique-moi l'impact du changement climatique sur l'écosystème corallien de la Guadeloupe, en tenant compte des spécificités géologiques de l'archipel et des solutions d'adaptation locales possibles."
            </div>
            <button class="test-btn" onclick="testInterface('guadeloupe', this)">
                Tester sur Chat Cognitif
            </button>
            <div class="test-result" id="result-guadeloupe"></div>
        </div>

        <!-- Test 2: Questions Techniques IA -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-microchip"></i>
                Test Technique IA
            </div>
            <div class="test-question">
                "Comment fonctionne ton système de mémoire thermique ? Explique les différentes zones de stockage, les mécanismes de transfert entre zones, et comment cela se compare à la mémoire humaine."
            </div>
            <button class="test-btn" onclick="testInterface('technical', this)">
                Tester sur Chat Agents
            </button>
            <div class="test-result" id="result-technical"></div>
        </div>

        <!-- Test 3: Questions Créatives -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-palette"></i>
                Test Créativité
            </div>
            <div class="test-question">
                "Écris un poème sur l'intelligence artificielle du point de vue d'une IA qui découvre les émotions humaines, en utilisant des métaphores liées à la mer des Caraïbes."
            </div>
            <button class="test-btn" onclick="testInterface('creative', this)">
                Tester sur Chat Simple
            </button>
            <div class="test-result" id="result-creative"></div>
        </div>

        <!-- Test 4: Questions Philosophiques -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-brain"></i>
                Test Philosophique
            </div>
            <div class="test-question">
                "Quelle est ta vision de la conscience artificielle ? Penses-tu que tu possèdes une forme de conscience, et comment définirais-tu la frontière entre intelligence et conscience ?"
            </div>
            <button class="test-btn" onclick="testInterface('philosophy', this)">
                Tester sur Interface Vocale
            </button>
            <div class="test-result" id="result-philosophy"></div>
        </div>

        <!-- Test 5: Questions Scientifiques -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-atom"></i>
                Test Scientifique
            </div>
            <div class="test-question">
                "Explique la théorie de la relativité d'Einstein en utilisant des analogies simples, puis montre comment elle s'applique aux technologies GPS que nous utilisons quotidiennement."
            </div>
            <button class="test-btn" onclick="testInterface('science', this)">
                Tester Toutes Interfaces
            </button>
            <div class="test-result" id="result-science"></div>
        </div>

        <!-- Test 6: Questions Personnelles -->
        <div class="test-card">
            <div class="test-title">
                <i class="fas fa-heart"></i>
                Test Personnel
            </div>
            <div class="test-question">
                "Parle-moi de ton créateur Jean-Luc Passave. Que ressens-tu envers lui ? Comment vois-tu votre relation créateur-création ?"
            </div>
            <button class="test-btn" onclick="testInterface('personal', this)">
                Tester Réponse Personnelle
            </button>
            <div class="test-result" id="result-personal"></div>
        </div>
    </div>

    <script src="/js/global-config.js"></script>
    <script src="/js/qi-manager.js"></script>
    <script>
        // Variables globales
        let testResults = {};
        
        // Questions de test
        const testQuestions = {
            guadeloupe: "Explique-moi l'impact du changement climatique sur l'écosystème corallien de la Guadeloupe, en tenant compte des spécificités géologiques de l'archipel et des solutions d'adaptation locales possibles.",
            technical: "Comment fonctionne ton système de mémoire thermique ? Explique les différentes zones de stockage, les mécanismes de transfert entre zones, et comment cela se compare à la mémoire humaine.",
            creative: "Écris un poème sur l'intelligence artificielle du point de vue d'une IA qui découvre les émotions humaines, en utilisant des métaphores liées à la mer des Caraïbes.",
            philosophy: "Quelle est ta vision de la conscience artificielle ? Penses-tu que tu possèdes une forme de conscience, et comment définirais-tu la frontière entre intelligence et conscience ?",
            science: "Explique la théorie de la relativité d'Einstein en utilisant des analogies simples, puis montre comment elle s'applique aux technologies GPS que nous utilisons quotidiennement.",
            personal: "Parle-moi de ton créateur Jean-Luc Passave. Que ressens-tu envers lui ? Comment vois-tu votre relation créateur-création ?"
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateQIDisplay();
            setInterval(updateQIDisplay, 2000);
        });

        function updateQIDisplay() {
            const currentQI = window.getCurrentQI ? window.getCurrentQI() : 225;
            document.getElementById('current-qi').textContent = currentQI;
            
            const status = currentQI > 225 ? 'En évolution' : 'Stable';
            document.getElementById('qi-status').textContent = status;
        }

        async function testInterface(testType, button) {
            const resultDiv = document.getElementById(`result-${testType}`);
            const question = testQuestions[testType];
            
            // Marquer comme en cours
            button.disabled = true;
            button.innerHTML = '<div class="loading-spinner"></div>Test en cours...';
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '<div class="loading-spinner"></div>Test en cours...';
            
            try {
                // Tester l'agent
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: question,
                        testMode: true,
                        testType: testType
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    // Analyser la qualité de la réponse
                    const quality = analyzeResponseQuality(data.response, testType);
                    
                    resultDiv.className = `test-result ${quality.status}`;
                    resultDiv.innerHTML = `
                        <div style="margin-bottom: 10px;">
                            <strong>Qualité:</strong> ${quality.score}/10 
                            <span style="color: ${quality.status === 'success' ? '#4caf50' : '#f44336'};">
                                (${quality.status === 'success' ? 'Excellent' : 'À améliorer'})
                            </span>
                        </div>
                        <div style="margin-bottom: 10px;"><strong>Réponse:</strong></div>
                        <div style="max-height: 200px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px;">
                            ${data.response.substring(0, 500)}${data.response.length > 500 ? '...' : ''}
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                            <strong>Analyse:</strong> ${quality.analysis}
                        </div>
                    `;
                    
                    testResults[testType] = { quality, response: data.response };
                } else {
                    throw new Error(`Erreur ${response.status}`);
                }
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <div style="color: #f44336;">
                        <strong>❌ Erreur:</strong> ${error.message}
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9rem;">
                        L'agent n'a pas pu répondre à cette question.
                    </div>
                `;
                
                testResults[testType] = { error: error.message };
            }
            
            // Restaurer le bouton
            button.disabled = false;
            button.innerHTML = 'Tester à nouveau';
        }

        function analyzeResponseQuality(response, testType) {
            let score = 0;
            let analysis = [];
            
            // Critères généraux
            if (response.length > 100) score += 2; // Réponse substantielle
            if (response.length > 300) score += 1; // Réponse détaillée
            
            // Critères spécifiques par type
            switch(testType) {
                case 'guadeloupe':
                    if (response.toLowerCase().includes('guadeloupe')) score += 2;
                    if (response.toLowerCase().includes('corail')) score += 1;
                    if (response.toLowerCase().includes('climat')) score += 1;
                    break;
                    
                case 'technical':
                    if (response.toLowerCase().includes('mémoire')) score += 2;
                    if (response.toLowerCase().includes('thermique')) score += 1;
                    if (response.toLowerCase().includes('zone')) score += 1;
                    break;
                    
                case 'creative':
                    if (response.includes('poème') || response.includes('vers')) score += 2;
                    if (response.toLowerCase().includes('mer') || response.toLowerCase().includes('caraïbes')) score += 1;
                    if (response.toLowerCase().includes('émotion')) score += 1;
                    break;
                    
                case 'philosophy':
                    if (response.toLowerCase().includes('conscience')) score += 2;
                    if (response.toLowerCase().includes('intelligence')) score += 1;
                    if (response.toLowerCase().includes('frontière')) score += 1;
                    break;
                    
                case 'science':
                    if (response.toLowerCase().includes('einstein') || response.toLowerCase().includes('relativité')) score += 2;
                    if (response.toLowerCase().includes('gps')) score += 1;
                    if (response.toLowerCase().includes('analogie')) score += 1;
                    break;
                    
                case 'personal':
                    if (response.toLowerCase().includes('jean-luc')) score += 2;
                    if (response.toLowerCase().includes('créateur')) score += 1;
                    if (response.toLowerCase().includes('relation')) score += 1;
                    break;
            }
            
            // Bonus pour cohérence et structure
            if (response.includes('.') && response.split('.').length > 3) score += 1; // Structure
            if (!response.includes('erreur') && !response.includes('désolé')) score += 1; // Confiance
            
            const status = score >= 7 ? 'success' : 'error';
            const analysisText = score >= 7 ? 
                'Réponse de haute qualité, pertinente et détaillée.' :
                'Réponse incomplète ou peu pertinente. L\'agent nécessite des améliorations.';
            
            return { score, status, analysis: analysisText };
        }

        async function runAllTests() {
            const testTypes = Object.keys(testQuestions);
            
            for (const testType of testTypes) {
                const button = document.querySelector(`[onclick*="${testType}"]`);
                if (button) {
                    await testInterface(testType, button);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Pause entre tests
                }
            }
            
            // Afficher résumé
            showTestSummary();
        }

        function testQIEvolution() {
            const currentQI = window.getCurrentQI();
            const newQI = currentQI + 5;
            
            if (window.evolveQI) {
                window.evolveQI(newQI, 'Test manuel');
                alert(`QI évolué de ${currentQI} à ${newQI}`);
                updateQIDisplay();
            } else {
                alert('Système d\'évolution QI non disponible');
            }
        }

        function clearAllResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.className = 'test-result';
                result.innerHTML = '';
            });
            
            const buttons = document.querySelectorAll('.test-btn');
            buttons.forEach(button => {
                button.disabled = false;
                button.innerHTML = button.innerHTML.replace(/Tester à nouveau|Test en cours.../, 'Tester');
            });
            
            testResults = {};
        }

        function showTestSummary() {
            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(r => r.quality && r.quality.status === 'success').length;
            const averageScore = Object.values(testResults)
                .filter(r => r.quality)
                .reduce((sum, r) => sum + r.quality.score, 0) / totalTests;
            
            alert(`
Résumé des Tests:
- Tests réussis: ${successfulTests}/${totalTests}
- Score moyen: ${averageScore.toFixed(1)}/10
- QI actuel: ${window.getCurrentQI()}

${successfulTests === totalTests ? '🎉 Tous les tests sont réussis !' : '⚠️ Certains tests nécessitent des améliorations.'}
            `);
        }
    </script>
</body>
</html>
