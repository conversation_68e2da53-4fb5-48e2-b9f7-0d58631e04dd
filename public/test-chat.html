<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Chat - LOUNA AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .chat-container {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
        }

        .user-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e3f2fd;
            color: #333;
            border-left: 4px solid #2196f3;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        #messageInput {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        #sendBtn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        #sendBtn:hover {
            transform: translateY(-2px);
        }

        #sendBtn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .metrics {
            background: #f0f8ff;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #2196f3;
        }

        .metrics h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .metric-item {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 5px 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Chat - LOUNA AI</h1>
        
        <div id="status" class="status disconnected">
            🔄 Connexion en cours...
        </div>

        <div id="chatContainer" class="chat-container">
            <div class="message ai-message">
                👋 Bonjour ! Je suis LOUNA AI. Envoyez-moi un message pour tester la connexion.
            </div>
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Tapez votre message ici..." 
                   onkeypress="handleKeyPress(event)">
            <button id="sendBtn" onclick="sendMessage()">Envoyer</button>
        </div>

        <div id="metrics" class="metrics" style="display: none;">
            <h3>📊 Métriques en temps réel</h3>
            <div id="metricsContent"></div>
        </div>
    </div>

    <script>
        let isConnected = false;

        // Vérifier la connexion
        async function checkConnection() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    isConnected = true;
                    updateStatus('✅ Connecté au serveur LOUNA AI', 'connected');
                    updateMetrics(data.metrics);
                } else {
                    throw new Error('Serveur non disponible');
                }
            } catch (error) {
                isConnected = false;
                updateStatus('❌ Connexion échouée', 'disconnected');
                console.error('Erreur connexion:', error);
            }
        }

        // Mettre à jour le statut
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // Mettre à jour les métriques
        function updateMetrics(metrics) {
            if (!metrics) return;
            
            const metricsDiv = document.getElementById('metrics');
            const content = document.getElementById('metricsContent');
            
            const items = [
                `🧠 Neurones: ${metrics.brainStats?.activeNeurons || 'N/A'}`,
                `🔗 Synapses: ${metrics.brainStats?.synapticConnections || 'N/A'}`,
                `🌡️ Température: ${metrics.memoryStats?.globalTemperature?.toFixed(1) || 'N/A'}°C`,
                `⚡ Efficacité: ${Math.round(metrics.memoryStats?.memoryEfficiency || 0)}%`,
                `🤖 Agent: ${metrics.agent || 'DeepSeek R1'}`
            ];
            
            content.innerHTML = items.map(item => 
                `<span class="metric-item">${item}</span>`
            ).join('');
            
            metricsDiv.style.display = 'block';
        }

        // Envoyer un message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();
            
            if (!message) {
                alert('Veuillez saisir un message');
                return;
            }
            
            if (!isConnected) {
                alert('Connexion au serveur perdue');
                await checkConnection();
                return;
            }

            // Désactiver l'interface
            input.disabled = true;
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';

            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';

            // Afficher l'indicateur de chargement
            const loadingId = addMessage('🤔 Réflexion en cours...', 'ai', true);

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                
                // Supprimer l'indicateur de chargement
                document.getElementById(loadingId).remove();

                if (data.success) {
                    addMessage(data.response, 'ai');
                    
                    // Mettre à jour les métriques
                    if (data.memoryStats || data.brainStats) {
                        updateMetrics({
                            memoryStats: data.memoryStats,
                            brainStats: data.brainStats,
                            agent: data.agent
                        });
                    }
                } else {
                    addMessage('❌ Erreur: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('❌ Erreur de communication: ' + error.message, 'ai');
            } finally {
                // Réactiver l'interface
                input.disabled = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Envoyer';
                input.focus();
            }
        }

        // Ajouter un message au chat
        function addMessage(text, sender, isLoading = false) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            messageDiv.id = messageId;
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;

            return messageId;
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', async () => {
            await checkConnection();
            document.getElementById('messageInput').focus();
            
            // Vérifier la connexion toutes les 30 secondes
            setInterval(checkConnection, 30000);
        });
    </script>
</body>
</html>
