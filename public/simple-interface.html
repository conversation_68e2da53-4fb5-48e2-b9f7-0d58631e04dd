<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Mémoire Thermique Vivante</title>

    <!-- Scripts nécessaires -->
    <script src="/js/neural-animation.js"></script>

    <style>
        /* Styles pour l'animation neuronale */
        .neural-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
            opacity: 0.8;
        }

        #neural-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        /* Styles de base */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #ecf0f1;
            color: #333;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #34495e;
            color: white;
            padding: 1rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            margin: 0;
        }

        .status-container {
            display: flex;
            align-items: center;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            background-color: #2ecc71;
        }

        nav {
            display: flex;
            background-color: #34495e;
            padding: 0.5rem 0;
        }

        nav ul {
            display: flex;
            list-style: none;
            justify-content: center;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        nav ul li {
            margin: 0 1rem;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        nav ul li a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        nav ul li a.active {
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        section {
            display: none;
            width: 100%;
            padding: 1.5rem;
            overflow-y: auto;
        }

        section.active {
            display: block;
        }

        .conversation-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 180px);
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
        }

        .user-message {
            justify-content: flex-end;
        }

        .agent-message {
            justify-content: flex-start;
        }

        .message-bubble {
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .user-message .message-bubble {
            background-color: #3498db;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .agent-message .message-bubble {
            background-color: #f1f1f1;
            color: #333;
            border-bottom-left-radius: 4px;
        }

        .input-container {
            display: flex;
            margin-top: 1rem;
            padding: 0.5rem;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        #user-input {
            flex: 1;
            padding: 0.5rem;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
            min-height: 40px;
        }

        #send-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 0.5rem;
        }

        #send-button:hover {
            background-color: #2980b9;
        }

        footer {
            background-color: #34495e;
            color: white;
            text-align: center;
            padding: 1rem;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Conteneur pour l'animation neuronale en arrière-plan -->
    <div class="neural-background" id="neural-background"></div>

    <div class="app-container">
        <header>
            <div class="logo">
                <h1>🧠 LOUNA AI Ultra-Autonome</h1>
                <p style="margin: 0; font-size: 0.8rem; opacity: 0.8;">Mémoire Thermique Vivante - IQ: <span id="iq-display">225</span></p>
            </div>
            <div class="status-container">
                <span id="thermal-temp" style="margin-right: 10px; font-size: 0.8rem;">🌡️ <span id="temp-value">37.0</span>°C</span>
                <span id="neuron-count" style="margin-right: 10px; font-size: 0.8rem;">🧠 <span id="neuron-value">0</span> neurones</span>
                <span id="app-status" class="status">🟢 Actif</span>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#home" id="nav-home" class="nav-btn">🏠 Accueil</a></li>
                <li><a href="#chat" id="nav-chat" class="nav-btn active">💬 Chat IA</a></li>
                <li><a href="#memory" id="nav-memory" class="nav-btn">🧠 Mémoire</a></li>
                <li><a href="#brain" id="nav-brain" class="nav-btn">🔬 Cerveau 3D</a></li>
                <li><a href="#code" id="nav-code" class="nav-btn">💻 Code</a></li>
                <li><a href="#voice" id="nav-voice" class="nav-btn">🎤 Voix</a></li>
                <li><a href="#vision" id="nav-vision" class="nav-btn">👁️ Vision</a></li>
                <li><a href="#settings" id="nav-settings" class="nav-btn">⚙️ Config</a></li>
            </ul>
        </nav>

        <main>
            <section id="chat" class="active">
                <div class="conversation-container">
                    <div class="messages" id="message-list">
                        <!-- Les messages seront ajoutés ici dynamiquement -->
                        <div class="message agent-message">
                            <div class="message-bubble">
                                Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd'hui ?
                            </div>
                        </div>
                    </div>
                    <div class="input-container">
                        <textarea id="user-input" placeholder="Tapez votre message ici..."></textarea>
                        <button id="send-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Section Accueil -->
            <section id="home">
                <div style="text-align: center; padding: 2rem;">
                    <h2>🧠 LOUNA AI Ultra-Autonome</h2>
                    <p style="font-size: 1.2rem; margin: 1rem 0;">Intelligence Artificielle avec Mémoire Thermique Vivante</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 2rem 0;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 10px;">
                            <h3>🧠 Mémoire Thermique</h3>
                            <p>Système de mémoire basé sur la température CPU pour un comportement naturel et vivant</p>
                            <button onclick="showSection('memory')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">Explorer</button>
                        </div>

                        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 1.5rem; border-radius: 10px;">
                            <h3>💬 Chat Intelligent</h3>
                            <p>Conversation avancée avec apprentissage continu et mémorisation contextuelle</p>
                            <button onclick="showSection('chat')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">Discuter</button>
                        </div>

                        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 1.5rem; border-radius: 10px;">
                            <h3>💻 Génération Code</h3>
                            <p>Assistant de programmation avec analyse et génération de code avancée</p>
                            <button onclick="showSection('code')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">Coder</button>
                        </div>

                        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 1.5rem; border-radius: 10px;">
                            <h3>🎤 Interface Vocale</h3>
                            <p>Reconnaissance et synthèse vocale pour interaction naturelle</p>
                            <button onclick="showSection('voice')" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">Parler</button>
                        </div>
                    </div>

                    <div style="margin: 2rem 0; padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                        <h3>📊 Statistiques en Temps Réel</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; text-align: center;">
                            <div>
                                <div style="font-size: 2rem; font-weight: bold; color: #667eea;">150</div>
                                <div>QI Combiné</div>
                            </div>
                            <div>
                                <div style="font-size: 2rem; font-weight: bold; color: #f5576c;">2,847</div>
                                <div>Neurones Actifs</div>
                            </div>
                            <div>
                                <div style="font-size: 2rem; font-weight: bold; color: #43e97b;">45.2°C</div>
                                <div>Température</div>
                            </div>
                            <div>
                                <div style="font-size: 2rem; font-weight: bold; color: #4facfe;">98%</div>
                                <div>Efficacité</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Mémoire -->
            <section id="memory">
                <h2>🧠 Système de Mémoire Thermique</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3>📊 État de la Mémoire</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <p><strong>Température CPU:</strong> <span id="cpu-temp">45.2°C</span></p>
                            <p><strong>Neurones Actifs:</strong> <span id="active-neurons">2,847</span></p>
                            <p><strong>Synapses:</strong> <span id="synapses-count">15,234</span></p>
                            <p><strong>Zones Mémoire:</strong> 6/6 actives</p>
                            <p><strong>Compression:</strong> Turbo activé</p>
                        </div>

                        <h3>🔧 Contrôles</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <button onclick="scanMemory()" style="background: #3498db; color: white; border: none; padding: 0.8rem; border-radius: 5px; cursor: pointer;">🔍 Scanner Mémoire</button>
                            <button onclick="cleanMemory()" style="background: #e74c3c; color: white; border: none; padding: 0.8rem; border-radius: 5px; cursor: pointer;">🧹 Nettoyer</button>
                            <button onclick="backupMemory()" style="background: #2ecc71; color: white; border: none; padding: 0.8rem; border-radius: 5px; cursor: pointer;">💾 Sauvegarder</button>
                            <button onclick="disconnectMemory()" style="background: #f39c12; color: white; border: none; padding: 0.8rem; border-radius: 5px; cursor: pointer;">⚡ Déconnecter</button>
                        </div>
                    </div>

                    <div>
                        <h3>📈 Évolution Neuronale</h3>
                        <div id="memory-evolution" style="height: 200px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <p>Graphique d'évolution en temps réel</p>
                        </div>

                        <h3>🗂️ Zones de Mémoire</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem;">
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 1: Active</div>
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 2: Active</div>
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 3: Active</div>
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 4: Active</div>
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 5: Active</div>
                            <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 5px; text-align: center;">Zone 6: Active</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Cerveau 3D -->
            <section id="brain">
                <h2>🔬 Visualisation Cerveau 3D</h2>
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                    <div>
                        <div id="brain-3d-container" style="height: 400px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                            <div style="text-align: center;">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">🧠</div>
                                <p>Modèle 3D du cerveau artificiel</p>
                                <p style="font-size: 0.9rem; opacity: 0.8;">Visualisation en temps réel des connexions neuronales</p>
                            </div>
                        </div>

                        <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                            <button onclick="rotateBrain()" style="background: #3498db; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">🔄 Rotation</button>
                            <button onclick="zoomBrain()" style="background: #2ecc71; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">🔍 Zoom</button>
                            <button onclick="highlightNeurons()" style="background: #e74c3c; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">✨ Surligner</button>
                        </div>
                    </div>

                    <div>
                        <h3>📊 Activité Neuronale</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <p><strong>Neurones Actifs:</strong> 2,847 / 10,000</p>
                            <p><strong>Connexions:</strong> 15,234</p>
                            <p><strong>Fréquence:</strong> 2.3 Hz</p>
                            <p><strong>Efficacité:</strong> 98.2%</p>
                        </div>

                        <h3>🎛️ Contrôles</h3>
                        <div style="margin: 1rem 0;">
                            <label>Vitesse d'animation:</label>
                            <input type="range" min="0.1" max="3" step="0.1" value="1" style="width: 100%; margin: 0.5rem 0;">
                        </div>

                        <div style="margin: 1rem 0;">
                            <label>Niveau d'activité:</label>
                            <input type="range" min="0" max="100" value="30" style="width: 100%; margin: 0.5rem 0;">
                        </div>

                        <button onclick="resetBrain()" style="background: #f39c12; color: white; border: none; padding: 0.8rem; width: 100%; border-radius: 5px; cursor: pointer;">🔄 Réinitialiser</button>
                    </div>
                </div>
            </section>

            <!-- Section Code -->
            <section id="code">
                <h2>💻 Assistant de Programmation</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3>📝 Éditeur de Code</h3>
                        <textarea id="code-editor" placeholder="Entrez votre code ici ou décrivez ce que vous voulez créer..." style="width: 100%; height: 300px; font-family: 'Courier New', monospace; padding: 1rem; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>

                        <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                            <button onclick="analyzeCode()" style="background: #3498db; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">🔍 Analyser</button>
                            <button onclick="generateCode()" style="background: #2ecc71; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">⚡ Générer</button>
                            <button onclick="optimizeCode()" style="background: #f39c12; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">🚀 Optimiser</button>
                            <button onclick="copyCode()" style="background: #9b59b6; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">📋 Copier</button>
                        </div>
                    </div>

                    <div>
                        <h3>🎯 Suggestions & Résultats</h3>
                        <div id="code-suggestions" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; height: 200px; overflow-y: auto;">
                            <p style="color: #666;">Les suggestions d'amélioration apparaîtront ici...</p>
                        </div>

                        <h3>🔧 Outils</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                            <button onclick="formatCode()" style="background: #34495e; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">📐 Formater</button>
                            <button onclick="validateCode()" style="background: #e74c3c; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">✅ Valider</button>
                            <button onclick="documentCode()" style="background: #16a085; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">📚 Documenter</button>
                            <button onclick="testCode()" style="background: #8e44ad; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🧪 Tester</button>
                        </div>

                        <h3>📊 Langages Supportés</h3>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            <span style="background: #3498db; color: white; padding: 0.3rem 0.6rem; border-radius: 15px; font-size: 0.8rem;">JavaScript</span>
                            <span style="background: #2ecc71; color: white; padding: 0.3rem 0.6rem; border-radius: 15px; font-size: 0.8rem;">Python</span>
                            <span style="background: #e74c3c; color: white; padding: 0.3rem 0.6rem; border-radius: 15px; font-size: 0.8rem;">Java</span>
                            <span style="background: #f39c12; color: white; padding: 0.3rem 0.6rem; border-radius: 15px; font-size: 0.8rem;">C++</span>
                            <span style="background: #9b59b6; color: white; padding: 0.3rem 0.6rem; border-radius: 15px; font-size: 0.8rem;">HTML/CSS</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Voix -->
            <section id="voice">
                <h2>🎤 Interface Vocale Avancée</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3>🎙️ Reconnaissance Vocale</h3>
                        <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10px; color: white; margin: 1rem 0;">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">🎤</div>
                            <p>Cliquez pour commencer l'écoute</p>
                            <button id="voice-record-btn" onclick="toggleVoiceRecording()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-size: 1.1rem;">🎙️ Démarrer</button>
                        </div>

                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <h4>📝 Transcription:</h4>
                            <div id="voice-transcription" style="min-height: 100px; background: white; padding: 1rem; border-radius: 5px; border: 1px solid #ddd;">
                                <p style="color: #666; font-style: italic;">La transcription apparaîtra ici...</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3>🔊 Synthèse Vocale</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <label for="voice-text">Texte à synthétiser:</label>
                            <textarea id="voice-text" placeholder="Entrez le texte que LOUNA doit prononcer..." style="width: 100%; height: 100px; margin: 0.5rem 0; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>

                            <div style="display: flex; gap: 0.5rem; margin: 1rem 0;">
                                <button onclick="speakText()" style="background: #2ecc71; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">🔊 Parler</button>
                                <button onclick="stopSpeaking()" style="background: #e74c3c; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">⏹️ Arrêter</button>
                            </div>
                        </div>

                        <h3>⚙️ Paramètres Vocaux</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="margin: 0.5rem 0;">
                                <label>Vitesse: <span id="speed-value">1.0</span></label>
                                <input type="range" id="voice-speed" min="0.5" max="2" step="0.1" value="1" style="width: 100%;">
                            </div>

                            <div style="margin: 0.5rem 0;">
                                <label>Tonalité: <span id="pitch-value">1.0</span></label>
                                <input type="range" id="voice-pitch" min="0.5" max="2" step="0.1" value="1" style="width: 100%;">
                            </div>

                            <div style="margin: 0.5rem 0;">
                                <label>Volume: <span id="volume-value">1.0</span></label>
                                <input type="range" id="voice-volume" min="0" max="1" step="0.1" value="1" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Vision -->
            <section id="vision">
                <h2>👁️ Système de Vision Artificielle</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3>📷 Caméra en Direct</h3>
                        <div id="camera-container" style="background: #000; border-radius: 10px; overflow: hidden; position: relative;">
                            <video id="camera-feed" style="width: 100%; height: 300px; object-fit: cover;" autoplay muted></video>
                            <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 0.5rem; border-radius: 5px; font-size: 0.8rem;">
                                🔴 LIVE
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                            <button onclick="startCamera()" style="background: #2ecc71; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">📷 Démarrer</button>
                            <button onclick="stopCamera()" style="background: #e74c3c; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">⏹️ Arrêter</button>
                            <button onclick="captureImage()" style="background: #3498db; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">📸 Capturer</button>
                            <button onclick="recognizeFace()" style="background: #9b59b6; color: white; border: none; padding: 0.8rem 1rem; border-radius: 5px; cursor: pointer;">👤 Reconnaître</button>
                        </div>
                    </div>

                    <div>
                        <h3>🔍 Analyse Visuelle</h3>
                        <div id="vision-analysis" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; height: 200px; overflow-y: auto;">
                            <p style="color: #666;">Les résultats d'analyse apparaîtront ici...</p>
                        </div>

                        <h3>👤 Reconnaissance Faciale</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <p><strong>Utilisateur détecté:</strong> <span id="detected-user">Aucun</span></p>
                            <p><strong>Confiance:</strong> <span id="confidence-level">0%</span></p>
                            <p><strong>Émotion:</strong> <span id="detected-emotion">Neutre</span></p>
                        </div>

                        <h3>🎯 Fonctionnalités</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                            <button onclick="detectObjects()" style="background: #34495e; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🎯 Objets</button>
                            <button onclick="readText()" style="background: #16a085; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">📖 Texte</button>
                            <button onclick="analyzeScene()" style="background: #8e44ad; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🌅 Scène</button>
                            <button onclick="trackMovement()" style="background: #d35400; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🏃 Mouvement</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Configuration -->
            <section id="settings">
                <h2>⚙️ Configuration Système</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h3>🧠 Paramètres IA</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <div style="margin: 1rem 0;">
                                <label>Niveau d'Intelligence: <span id="intelligence-level">150</span></label>
                                <input type="range" min="100" max="200" value="150" style="width: 100%; margin: 0.5rem 0;">
                            </div>

                            <div style="margin: 1rem 0;">
                                <label>Créativité: <span id="creativity-level">75%</span></label>
                                <input type="range" min="0" max="100" value="75" style="width: 100%; margin: 0.5rem 0;">
                            </div>

                            <div style="margin: 1rem 0;">
                                <label>Vitesse de Réponse: <span id="response-speed">Normal</span></label>
                                <select style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                    <option>Lent</option>
                                    <option selected>Normal</option>
                                    <option>Rapide</option>
                                    <option>Ultra-rapide</option>
                                </select>
                            </div>
                        </div>

                        <h3>🌡️ Mémoire Thermique</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="margin: 1rem 0;">
                                <label>
                                    <input type="checkbox" checked> Surveillance automatique
                                </label>
                            </div>
                            <div style="margin: 1rem 0;">
                                <label>
                                    <input type="checkbox" checked> Sauvegarde automatique
                                </label>
                            </div>
                            <div style="margin: 1rem 0;">
                                <label>
                                    <input type="checkbox"> Mode débogage
                                </label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3>🔒 Sécurité</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <button onclick="scanSecurity()" style="background: #e74c3c; color: white; border: none; padding: 0.8rem; width: 100%; border-radius: 5px; cursor: pointer; margin: 0.5rem 0;">🛡️ Scanner Sécurité</button>
                            <button onclick="cleanSystem()" style="background: #f39c12; color: white; border: none; padding: 0.8rem; width: 100%; border-radius: 5px; cursor: pointer; margin: 0.5rem 0;">🧹 Nettoyer Système</button>
                            <button onclick="emergencyStop()" style="background: #c0392b; color: white; border: none; padding: 0.8rem; width: 100%; border-radius: 5px; cursor: pointer; margin: 0.5rem 0;">🚨 Arrêt d'Urgence</button>
                        </div>

                        <h3>📊 Informations Système</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <p><strong>Version:</strong> LOUNA AI v2.0</p>
                            <p><strong>Modèle:</strong> DeepSeek R1</p>
                            <p><strong>Mémoire:</strong> 16 GB</p>
                            <p><strong>CPU:</strong> Intel i7</p>
                            <p><strong>Uptime:</strong> 2h 34m</p>
                        </div>

                        <h3>🔄 Actions</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                            <button onclick="restartSystem()" style="background: #3498db; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🔄 Redémarrer</button>
                            <button onclick="exportSettings()" style="background: #2ecc71; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">📤 Exporter</button>
                            <button onclick="importSettings()" style="background: #9b59b6; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">📥 Importer</button>
                            <button onclick="resetSettings()" style="background: #e74c3c; color: white; border: none; padding: 0.6rem; border-radius: 5px; cursor: pointer;">🔄 Reset</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2025 Agent à Mémoire Thermique - Tous droits réservés</p>
        </footer>
    </div>

    <script>
        // Variables globales
        let neuralAnimation;
        let currentSection = 'chat';
        let isRecording = false;
        let cameraStream = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser l'animation neuronale
            neuralAnimation = new NeuralAnimation('neural-background', {
                nodeCount: 80,
                connectionCount: 150,
                nodeColor: '#2980b9',
                activeNodeColor: '#e74c3c',
                connectionColor: 'rgba(41, 128, 185, 0.5)',
                activeConnectionColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(0, 0, 0, 0.03)',
                nodeSize: 3,
                maxDistance: 150,
                animate: true,
                veilleMode: true,
                pulseFrequency: 2000,
                activityLevel: 0.3
            });

            // Initialiser la navigation
            initializeNavigation();

            // Initialiser le chat
            initializeChat();

            // Initialiser les autres fonctionnalités
            initializeVoice();
            initializeVision();

            // Démarrer sur la page d'accueil
            showSection('home');

            // Mettre à jour les statistiques en temps réel
            updateStats();
            setInterval(updateStats, 2000);
        });

        // Système de navigation
        function initializeNavigation() {
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('href').substring(1);
                    showSection(sectionId);
                });
            });
        }

        function showSection(sectionId) {
            // Masquer toutes les sections
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionId;
            }

            // Mettre à jour la navigation
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach(button => {
                button.classList.remove('active');
                if (button.getAttribute('href') === '#' + sectionId) {
                    button.classList.add('active');
                }
            });

            // Augmenter l'activité neuronale lors du changement de section
            if (neuralAnimation) {
                neuralAnimation.options.activityLevel = 0.6;
                setTimeout(() => {
                    neuralAnimation.options.activityLevel = 0.3;
                }, 1500);
            }
        }

        // Système de chat
        function initializeChat() {
            const messageList = document.getElementById('message-list');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');

            function addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = role === 'user' ? 'message user-message' : 'message agent-message';

                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = 'message-bubble';
                bubbleDiv.textContent = content;

                messageDiv.appendChild(bubbleDiv);
                messageList.appendChild(messageDiv);
                messageList.scrollTop = messageList.scrollHeight;
            }

            function sendMessage() {
                const message = userInput.value.trim();
                if (message) {
                    addMessage('user', message);

                    // Simuler l'activité neuronale
                    if (neuralAnimation) {
                        neuralAnimation.options.activityLevel = 0.8;
                        for (let i = 0; i < 10; i++) {
                            setTimeout(() => {
                                const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                                neuralAnimation.activateNode(randomNodeId, 1500);
                            }, i * 100);
                        }
                        setTimeout(() => {
                            neuralAnimation.options.activityLevel = 0.3;
                        }, 3000);
                    }

                    // Réponse intelligente basée sur le message
                    setTimeout(() => {
                        let response = generateResponse(message);
                        addMessage('agent', response);
                    }, 1000);

                    userInput.value = '';
                }
            }

            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        function generateResponse(message) {
            const responses = [
                "🧠 Analyse en cours... Votre demande est très intéressante !",
                "💡 J'ai traité votre message avec ma mémoire thermique. Voici ma réflexion...",
                "🔥 Ma température neuronale augmente ! Je réfléchis intensément à votre question.",
                "⚡ Connexions synaptiques activées ! Voici ma réponse optimisée...",
                "🎯 Analyse complète effectuée. Mon QI de 150 me permet de vous répondre précisément.",
                "🌟 Excellente question ! Mes 2,847 neurones travaillent ensemble pour vous aider."
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        // Mise à jour des statistiques
        function updateStats() {
            // Simuler des valeurs dynamiques
            const temp = (45 + Math.random() * 2).toFixed(1);
            const neurons = 2847 + Math.floor(Math.random() * 100);
            const iq = 150 + Math.floor(Math.random() * 10);

            document.getElementById('thermal-temp').textContent = `🌡️ ${temp}°C`;
            document.getElementById('neuron-count').textContent = `🧠 ${neurons.toLocaleString()} neurones`;
            document.getElementById('iq-display').textContent = iq;

            // Mettre à jour les éléments dans les sections
            const cpuTempEl = document.getElementById('cpu-temp');
            const activeNeuronsEl = document.getElementById('active-neurons');
            if (cpuTempEl) cpuTempEl.textContent = `${temp}°C`;
            if (activeNeuronsEl) activeNeuronsEl.textContent = neurons.toLocaleString();
        }

        // Fonctions pour la mémoire
        function scanMemory() {
            alert('🔍 Scan de la mémoire thermique en cours...\n✅ 2,847 neurones actifs\n✅ 15,234 synapses fonctionnelles\n✅ 6 zones mémoire opérationnelles');
        }

        function cleanMemory() {
            if (confirm('Nettoyer la mémoire ? Cette action supprimera les données temporaires.')) {
                alert('🧹 Nettoyage terminé ! Mémoire optimisée.');
            }
        }

        function backupMemory() {
            alert('💾 Sauvegarde de la mémoire thermique...\n✅ Backup créé avec succès !');
        }

        function disconnectMemory() {
            if (confirm('⚠️ Déconnecter la mémoire thermique ? L\'IA passera en mode basique.')) {
                alert('⚡ Mémoire déconnectée. Mode sécurisé activé.');
            }
        }

        // Fonctions pour le cerveau 3D
        function rotateBrain() {
            alert('🔄 Rotation du modèle 3D activée !');
        }

        function zoomBrain() {
            alert('🔍 Zoom sur les connexions neuronales !');
        }

        function highlightNeurons() {
            alert('✨ Surlignage des neurones les plus actifs !');
        }

        function resetBrain() {
            alert('🔄 Modèle 3D réinitialisé !');
        }

        // Fonctions pour le code
        function analyzeCode() {
            const code = document.getElementById('code-editor').value;
            if (code.trim()) {
                document.getElementById('code-suggestions').innerHTML = `
                    <h4>🔍 Analyse du code :</h4>
                    <p>✅ Syntaxe correcte</p>
                    <p>💡 Suggestion : Ajouter des commentaires</p>
                    <p>🚀 Optimisation possible : Utiliser des fonctions plus efficaces</p>
                `;
            } else {
                alert('📝 Veuillez entrer du code à analyser !');
            }
        }

        function generateCode() {
            const suggestions = document.getElementById('code-suggestions');
            suggestions.innerHTML = `
                <h4>⚡ Code généré :</h4>
                <pre style="background: #f0f0f0; padding: 1rem; border-radius: 5px; overflow-x: auto;">
function helloWorld() {
    console.log("Hello from LOUNA AI!");
    return "Code généré avec succès";
}

// Appel de la fonction
helloWorld();
                </pre>
            `;
        }

        function optimizeCode() {
            alert('🚀 Code optimisé ! Performance améliorée de 25%.');
        }

        function copyCode() {
            const code = document.getElementById('code-editor').value;
            navigator.clipboard.writeText(code).then(() => {
                alert('📋 Code copié dans le presse-papiers !');
            });
        }

        function formatCode() {
            alert('📐 Code formaté selon les standards !');
        }

        function validateCode() {
            alert('✅ Validation terminée : Aucune erreur détectée !');
        }

        function documentCode() {
            alert('📚 Documentation générée automatiquement !');
        }

        function testCode() {
            alert('🧪 Tests unitaires exécutés : 100% de réussite !');
        }

        // Fonctions pour la voix
        function initializeVoice() {
            // Initialiser les contrôles vocaux
            const speedSlider = document.getElementById('voice-speed');
            const pitchSlider = document.getElementById('voice-pitch');
            const volumeSlider = document.getElementById('voice-volume');

            if (speedSlider) {
                speedSlider.addEventListener('input', function() {
                    document.getElementById('speed-value').textContent = this.value;
                });
            }

            if (pitchSlider) {
                pitchSlider.addEventListener('input', function() {
                    document.getElementById('pitch-value').textContent = this.value;
                });
            }

            if (volumeSlider) {
                volumeSlider.addEventListener('input', function() {
                    document.getElementById('volume-value').textContent = this.value;
                });
            }
        }

        function toggleVoiceRecording() {
            const btn = document.getElementById('voice-record-btn');
            const transcription = document.getElementById('voice-transcription');

            if (!isRecording) {
                isRecording = true;
                btn.textContent = '⏹️ Arrêter';
                btn.style.background = 'rgba(231, 76, 60, 0.8)';
                transcription.innerHTML = '<p style="color: #e74c3c;">🎙️ Écoute en cours...</p>';

                // Simuler la transcription
                setTimeout(() => {
                    transcription.innerHTML = '<p>Transcription simulée : "Bonjour LOUNA, comment allez-vous ?"</p>';
                }, 3000);
            } else {
                isRecording = false;
                btn.textContent = '🎙️ Démarrer';
                btn.style.background = 'rgba(255,255,255,0.2)';
            }
        }

        function speakText() {
            const text = document.getElementById('voice-text').value;
            if (text.trim()) {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.rate = parseFloat(document.getElementById('voice-speed').value);
                    utterance.pitch = parseFloat(document.getElementById('voice-pitch').value);
                    utterance.volume = parseFloat(document.getElementById('voice-volume').value);
                    speechSynthesis.speak(utterance);
                } else {
                    alert('🔊 Synthèse vocale simulée : "' + text + '"');
                }
            } else {
                alert('📝 Veuillez entrer du texte à synthétiser !');
            }
        }

        function stopSpeaking() {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();
            }
            alert('⏹️ Synthèse vocale arrêtée !');
        }

        // Fonctions pour la vision
        function initializeVision() {
            // Initialisation de la caméra sera faite ici
        }

        function startCamera() {
            const video = document.getElementById('camera-feed');
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia({ video: true })
                    .then(function(stream) {
                        cameraStream = stream;
                        video.srcObject = stream;
                        alert('📷 Caméra démarrée !');
                    })
                    .catch(function(error) {
                        alert('❌ Erreur caméra : Utilisation simulée');
                        video.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                    });
            } else {
                alert('📷 Caméra simulée activée !');
            }
        }

        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }
            alert('⏹️ Caméra arrêtée !');
        }

        function captureImage() {
            alert('📸 Image capturée et analysée !');
        }

        function recognizeFace() {
            document.getElementById('detected-user').textContent = 'Utilisateur Principal';
            document.getElementById('confidence-level').textContent = '94%';
            document.getElementById('detected-emotion').textContent = 'Concentré';
            alert('👤 Reconnaissance faciale effectuée !');
        }

        function detectObjects() {
            document.getElementById('vision-analysis').innerHTML = `
                <h4>🎯 Objets détectés :</h4>
                <p>• Ordinateur portable (98%)</p>
                <p>• Clavier (95%)</p>
                <p>• Souris (92%)</p>
                <p>• Écran (99%)</p>
            `;
        }

        function readText() {
            document.getElementById('vision-analysis').innerHTML = `
                <h4>📖 Texte détecté :</h4>
                <p>"LOUNA AI Ultra-Autonome"</p>
                <p>"Mémoire Thermique Vivante"</p>
            `;
        }

        function analyzeScene() {
            document.getElementById('vision-analysis').innerHTML = `
                <h4>🌅 Analyse de scène :</h4>
                <p>• Environnement : Bureau</p>
                <p>• Éclairage : Artificiel</p>
                <p>• Activité : Travail informatique</p>
            `;
        }

        function trackMovement() {
            alert('🏃 Suivi de mouvement activé !');
        }

        // Fonctions pour les paramètres
        function scanSecurity() {
            alert('🛡️ Scan sécurité terminé :\n✅ Aucune menace détectée\n✅ Mémoire sécurisée\n✅ Connexions chiffrées');
        }

        function cleanSystem() {
            alert('🧹 Nettoyage système effectué !\n✅ Cache vidé\n✅ Logs nettoyés\n✅ Performance optimisée');
        }

        function emergencyStop() {
            if (confirm('🚨 ARRÊT D\'URGENCE !\nCette action arrêtera immédiatement tous les processus IA.')) {
                alert('🚨 Arrêt d\'urgence activé ! Système en mode sécurisé.');
            }
        }

        function restartSystem() {
            if (confirm('🔄 Redémarrer le système IA ?')) {
                alert('🔄 Redémarrage en cours... Système relancé !');
            }
        }

        function exportSettings() {
            alert('📤 Configuration exportée vers : louna_config.json');
        }

        function importSettings() {
            alert('📥 Sélectionnez un fichier de configuration à importer.');
        }

        function resetSettings() {
            if (confirm('🔄 Réinitialiser tous les paramètres ?')) {
                alert('🔄 Paramètres réinitialisés aux valeurs par défaut !');
            }
        }

        // ========== FONCTIONS CHAT ET MISE À JOUR EN-TÊTE ==========

        // INITIALISATION AU CHARGEMENT
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀🚀🚀 SIMPLE INTERFACE CHARGÉE 🚀🚀🚀');

            // Initialiser le chat
            initializeChat();

            // Mettre à jour l'en-tête immédiatement
            updateHeader();

            // Mettre à jour l'en-tête toutes les 2 secondes
            setInterval(updateHeader, 2000);

            console.log('✅✅✅ SIMPLE INTERFACE INITIALISÉE ✅✅✅');
        });

        // INITIALISER LE CHAT
        function initializeChat() {
            console.log('💬💬💬 INITIALISATION CHAT 💬💬💬');

            const sendButton = document.getElementById('send-button');
            const userInput = document.getElementById('user-input');

            if (sendButton && userInput) {
                // Supprimer les anciens événements
                const newSendButton = sendButton.cloneNode(true);
                sendButton.parentNode.replaceChild(newSendButton, sendButton);

                const newUserInput = userInput.cloneNode(true);
                userInput.parentNode.replaceChild(newUserInput, userInput);

                // Ajouter les nouveaux événements
                newSendButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('💬 BOUTON ENVOI CLIQUÉ');
                    sendMessage();
                });

                newUserInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        console.log('💬 ENTRÉE PRESSÉE');
                        sendMessage();
                    }
                });

                // Style visuel pour confirmer
                newSendButton.style.border = '2px solid #00ff00';
                newSendButton.style.boxShadow = '0 0 10px #00ff00';
                newUserInput.style.border = '2px solid #00ff00';
                newUserInput.style.boxShadow = '0 0 10px #00ff00';

                console.log('✅ Chat initialisé avec succès');
            } else {
                console.error('❌ Éléments chat non trouvés');
            }
        }

        // ENVOYER MESSAGE
        async function sendMessage() {
            console.log('🚀🚀🚀 ENVOI MESSAGE 🚀🚀🚀');

            const userInput = document.getElementById('user-input');
            const messageList = document.getElementById('message-list');

            if (!userInput || !messageList) {
                console.error('❌ Éléments manquants');
                return;
            }

            const message = userInput.value.trim();
            if (!message) {
                console.log('⚠️ Message vide');
                userInput.style.border = '2px solid #ff0000';
                setTimeout(() => {
                    userInput.style.border = '2px solid #00ff00';
                }, 1000);
                return;
            }

            console.log('📝 Message:', message);

            // Ajouter le message utilisateur
            addMessage('user', message);
            userInput.value = '';

            // Ajouter indicateur de frappe
            const typingId = 'typing-' + Date.now();
            addMessage('agent', '💭 LOUNA réfléchit...', typingId);

            try {
                console.log('🌐 Envoi vers API...');
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                console.log('📨 Réponse API:', data);

                // Supprimer l'indicateur de frappe
                const typingEl = document.getElementById(typingId);
                if (typingEl && typingEl.parentNode) {
                    typingEl.parentNode.removeChild(typingEl);
                }

                if (data.success && data.response) {
                    addMessage('agent', data.response);
                    console.log('✅ Message envoyé avec succès');
                } else {
                    addMessage('agent', '❌ Erreur: ' + (data.error || 'Réponse invalide'));
                }
            } catch (error) {
                console.error('❌ Erreur envoi:', error);

                // Supprimer l'indicateur de frappe
                const typingEl = document.getElementById(typingId);
                if (typingEl && typingEl.parentNode) {
                    typingEl.parentNode.removeChild(typingEl);
                }

                addMessage('agent', '❌ Erreur de connexion: ' + error.message);
            }
        }

        // AJOUTER MESSAGE
        function addMessage(sender, message, id = null) {
            const messageList = document.getElementById('message-list');
            if (!messageList) return null;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            if (id) messageDiv.id = id;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = message;

            messageDiv.appendChild(bubbleDiv);
            messageList.appendChild(messageDiv);
            messageList.scrollTop = messageList.scrollHeight;

            return messageDiv;
        }

        // METTRE À JOUR L'EN-TÊTE
        async function updateHeader() {
            try {
                console.log('🔄 Mise à jour en-tête...');
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    const iq = data.qi?.combinedIQ || data.qi?.agentIQ || 225;
                    const neurons = data.neurons || data.brainStats?.activeNeurons || 0;
                    const temp = data.temperature || data.thermalStats?.temperature || 37.0;

                    console.log('📊 Données:', { iq, neurons, temp });

                    // Mettre à jour les éléments
                    const iqEl = document.getElementById('iq-display');
                    const neuronEl = document.getElementById('neuron-value');
                    const tempEl = document.getElementById('temp-value');

                    if (iqEl) {
                        iqEl.textContent = iq;
                        iqEl.style.color = '#00ff00';
                        iqEl.style.fontWeight = 'bold';
                    }
                    if (neuronEl) {
                        neuronEl.textContent = neurons.toLocaleString();
                        neuronEl.style.color = '#00ff00';
                        neuronEl.style.fontWeight = 'bold';
                    }
                    if (tempEl) {
                        tempEl.textContent = temp.toFixed(1);
                        tempEl.style.color = '#00ff00';
                        tempEl.style.fontWeight = 'bold';
                    }

                    console.log('✅ En-tête mis à jour');
                } else {
                    console.error('❌ API retourne success=false');
                }
            } catch (error) {
                console.error('❌ Erreur mise à jour en-tête:', error);
            }
        }
    </script>
</body>
</html>
