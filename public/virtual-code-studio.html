<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthétiseur de Code Virtuel</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        :root {
            --primary-color: #4a6fe3;
            --secondary-color: #25b0e8;
            --accent-color: #00de9c;
            --dark-color: #1a2942;
            --light-color: #f4f7fc;
            --success-color: #00c969;
            --warning-color: #ffb74d;
            --error-color: #ff5252;
        }

        .virtual-studio {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 100vh;
            overflow: hidden;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .studio-header {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .studio-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background-color: white;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .module-list {
            list-style: none;
            padding: 0;
            margin: 0;
            flex: 1;
            overflow-y: auto;
        }

        .module-item {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 4px solid var(--primary-color);
        }

        .module-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .module-item h3 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .module-item p {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }

        .module-meta {
            display: flex;
            font-size: 0.8rem;
            color: #999;
            margin-top: 0.5rem;
        }

        .module-meta span {
            margin-right: 1rem;
        }

        .code-container {
            flex: 1;
            background-color: #1e1e1e;
            color: #d4d4d4;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow: auto;
            margin-bottom: 1rem;
            position: relative;
        }

        .code-container pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .task-form {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .task-form h2 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #3a5fd0;
        }

        button.secondary {
            background-color: #e0e0e0;
            color: #333;
        }

        button.secondary:hover {
            background-color: #d0d0d0;
        }

        .output-section {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .output-section h2 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .output-content {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', monospace;
            min-height: 150px;
        }

        .tabs {
            display: flex;
            margin-bottom: 1rem;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 500;
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.success {
            background-color: rgba(0, 201, 105, 0.1);
            color: var(--success-color);
        }

        .status-badge.warning {
            background-color: rgba(255, 183, 77, 0.1);
            color: var(--warning-color);
        }

        .status-badge.error {
            background-color: rgba(255, 82, 82, 0.1);
            color: var(--error-color);
        }
    </style>
</head>
<body>
    <div class="virtual-studio">
        <header class="studio-header">
            <div>
                <h1>Synthétiseur de Code Virtuel</h1>
                <p>Créez des modules de code adaptés pour vos besoins spécifiques</p>
            </div>
            <div>
                <span id="status-indicator" class="status-badge success">Système actif</span>
            </div>
        </header>

        <div class="studio-content">
            <aside class="sidebar">
                <h2>Modules disponibles</h2>
                <div class="loading" id="modules-loading">
                    <div class="loading-spinner"></div>
                </div>
                <ul class="module-list" id="module-list">
                    <!-- Les modules seront ajoutés ici dynamiquement -->
                </ul>
            </aside>

            <main class="main-content">
                <div class="tabs">
                    <div class="tab active" data-tab="create">Créer un module</div>
                    <div class="tab" data-tab="execute">Exécuter un module</div>
                    <div class="tab" data-tab="code">Voir le code</div>
                </div>

                <div class="tab-content active" id="create-tab">
                    <div class="task-form">
                        <h2>Créer un nouveau module</h2>
                        <div class="form-group">
                            <label for="task-description">Description de la tâche</label>
                            <textarea id="task-description" placeholder="Décrivez en détail ce que vous voulez que le module fasse..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="task-type">Type de tâche</label>
                            <select id="task-type">
                                <option value="3d_drawing">Dessin 3D</option>
                                <option value="data_processing">Traitement de données</option>
                                <option value="image_manipulation">Manipulation d'images</option>
                                <option value="simulation">Simulation</option>
                                <option value="algorithm">Algorithme</option>
                                <option value="generic">Générique</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="secondary" id="reset-form">Réinitialiser</button>
                            <button type="button" id="create-module">Créer le module</button>
                        </div>
                    </div>

                    <div class="output-section" id="create-output-section" style="display: none;">
                        <h2>Résultat</h2>
                        <div class="output-content" id="create-output"></div>
                    </div>
                </div>

                <div class="tab-content" id="execute-tab">
                    <div class="task-form">
                        <h2>Exécuter un module</h2>
                        <div class="form-group">
                            <label for="selected-module">Module sélectionné</label>
                            <input type="text" id="selected-module" placeholder="Sélectionnez un module dans la liste..." readonly>
                        </div>
                        <div class="form-group">
                            <label for="execution-params">Paramètres d'exécution (JSON)</label>
                            <textarea id="execution-params" placeholder='{"param1": "valeur1", "param2": 123}'></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" id="execute-module" disabled>Exécuter</button>
                        </div>
                    </div>

                    <div class="output-section" id="execute-output-section" style="display: none;">
                        <h2>Résultat de l'exécution</h2>
                        <div class="output-content" id="execute-output"></div>
                    </div>
                </div>

                <div class="tab-content" id="code-tab">
                    <div class="code-container">
                        <pre id="code-display">Sélectionnez un module pour voir son code...</pre>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Variables globales
            let currentModuleId = null;
            let modulesList = [];
            
            // Éléments DOM
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            const moduleList = document.getElementById('module-list');
            const modulesLoading = document.getElementById('modules-loading');
            const taskDescription = document.getElementById('task-description');
            const taskType = document.getElementById('task-type');
            const createButton = document.getElementById('create-module');
            const resetButton = document.getElementById('reset-form');
            const selectedModuleInput = document.getElementById('selected-module');
            const executionParams = document.getElementById('execution-params');
            const executeButton = document.getElementById('execute-module');
            const createOutput = document.getElementById('create-output');
            const createOutputSection = document.getElementById('create-output-section');
            const executeOutput = document.getElementById('execute-output');
            const executeOutputSection = document.getElementById('execute-output-section');
            const codeDisplay = document.getElementById('code-display');
            const statusIndicator = document.getElementById('status-indicator');
            
            // Gestionnaire d'onglets
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Désactiver tous les onglets
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Activer l'onglet cliqué
                    tab.classList.add('active');
                    const tabId = tab.dataset.tab;
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });
            
            // Vérifier l'état du système
            function checkSystemStatus() {
                fetch('/api/virtual-code/status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (data.enabled) {
                                statusIndicator.className = 'status-badge success';
                                statusIndicator.textContent = 'Système actif';
                            } else {
                                statusIndicator.className = 'status-badge warning';
                                statusIndicator.textContent = 'Système désactivé';
                            }
                        } else {
                            statusIndicator.className = 'status-badge error';
                            statusIndicator.textContent = 'Erreur système';
                        }
                    })
                    .catch(error => {
                        console.error('Erreur lors de la vérification du statut:', error);
                        statusIndicator.className = 'status-badge error';
                        statusIndicator.textContent = 'Connexion perdue';
                    });
            }
            
            // Charger la liste des modules
            function loadModules() {
                modulesLoading.style.display = 'flex';
                moduleList.innerHTML = '';
                
                fetch('/api/virtual-code/modules')
                    .then(response => response.json())
                    .then(data => {
                        modulesLoading.style.display = 'none';
                        
                        if (data.success && data.modules) {
                            modulesList = data.modules;
                            
                            if (modulesList.length === 0) {
                                moduleList.innerHTML = '<p>Aucun module disponible. Créez-en un!</p>';
                                return;
                            }
                            
                            // Trier les modules par date de création (plus récent en premier)
                            modulesList.sort((a, b) => b.creation_time - a.creation_time);
                            
                            // Afficher les modules
                            modulesList.forEach(module => {
                                const moduleItem = document.createElement('li');
                                moduleItem.className = 'module-item';
                                moduleItem.dataset.id = module.id;
                                
                                const creationDate = new Date(module.creation_time * 1000).toLocaleDateString();
                                const lastUsedDate = new Date(module.last_used_time * 1000).toLocaleDateString();
                                
                                moduleItem.innerHTML = `
                                    <h3>${module.name}</h3>
                                    <p>${module.description}</p>
                                    <div class="module-meta">
                                        <span>Type: ${module.task_type}</span>
                                        <span>Utilisations: ${module.usage_count}</span>
                                        <span>Créé le: ${creationDate}</span>
                                    </div>
                                `;
                                
                                moduleItem.addEventListener('click', () => selectModule(module.id));
                                moduleList.appendChild(moduleItem);
                            });
                        } else {
                            moduleList.innerHTML = `<p>Erreur lors du chargement des modules: ${data.message || 'Erreur inconnue'}</p>`;
                        }
                    })
                    .catch(error => {
                        modulesLoading.style.display = 'none';
                        moduleList.innerHTML = `<p>Erreur de connexion: ${error.message}</p>`;
                        console.error('Erreur lors du chargement des modules:', error);
                    });
            }
            
            // Sélectionner un module
            function selectModule(moduleId) {
                currentModuleId = moduleId;
                
                // Mettre à jour l'interface
                document.querySelectorAll('.module-item').forEach(item => {
                    item.classList.remove('selected');
                    if (item.dataset.id === moduleId) {
                        item.classList.add('selected');
                    }
                });
                
                // Trouver le module dans la liste
                const module = modulesList.find(m => m.id === moduleId);
                if (module) {
                    selectedModuleInput.value = module.name;
                    executeButton.disabled = false;
                    
                    // Préparer les paramètres d'exécution par défaut
                    const defaultParams = {};
                    executionParams.value = JSON.stringify(defaultParams, null, 2);
                    
                    // Afficher le code du module
                    loadModuleCode(moduleId);
                }
            }
            
            // Charger le code d'un module
            function loadModuleCode(moduleId) {
                codeDisplay.textContent = 'Chargement du code...';
                
                // Ici vous devriez avoir une API pour récupérer le code source du module
                // Pour l'instant, on va simuler une attente
                setTimeout(() => {
                    codeDisplay.textContent = `// Code du module ${moduleId}\n// Ce code est généré automatiquement\n\n/* Contenu du module à venir */`;
                }, 500);
            }
            
            // Créer un nouveau module
            createButton.addEventListener('click', function() {
                const description = taskDescription.value.trim();
                const type = taskType.value;
                
                if (!description) {
                    alert('Veuillez fournir une description de la tâche');
                    return;
                }
                
                createButton.disabled = true;
                createButton.textContent = 'Création en cours...';
                createOutput.innerHTML = 'Création du module en cours...';
                createOutputSection.style.display = 'block';
                
                fetch('/api/virtual-code/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_description: description,
                        params: {
                            task_type: type
                        }
                    })
                })
                .then(response => response.json())
                .then(data => {
                    createButton.disabled = false;
                    createButton.textContent = 'Créer le module';
                    
                    if (data.success && data.module) {
                        createOutput.innerHTML = `
                            <p>Module créé avec succès!</p>
                            <p><strong>Nom:</strong> ${data.module.name}</p>
                            <p><strong>ID:</strong> ${data.module.id}</p>
                            <p><strong>Type:</strong> ${data.module.task_type}</p>
                        `;
                        
                        // Recharger la liste des modules
                        loadModules();
                        
                        // Sélectionner automatiquement le nouveau module
                        setTimeout(() => {
                            selectModule(data.module.id);
                            tabs[1].click(); // Passer à l'onglet Exécuter
                        }, 1000);
                    } else {
                        createOutput.innerHTML = `<p>Erreur lors de la création du module: ${data.message || 'Erreur inconnue'}</p>`;
                    }
                })
                .catch(error => {
                    createButton.disabled = false;
                    createButton.textContent = 'Créer le module';
                    createOutput.innerHTML = `<p>Erreur de connexion: ${error.message}</p>`;
                    console.error('Erreur lors de la création du module:', error);
                });
            });
            
            // Réinitialiser le formulaire
            resetButton.addEventListener('click', function() {
                taskDescription.value = '';
                taskType.value = '3d_drawing';
                createOutputSection.style.display = 'none';
            });
            
            // Exécuter un module
            executeButton.addEventListener('click', function() {
                if (!currentModuleId) {
                    alert('Veuillez sélectionner un module à exécuter');
                    return;
                }
                
                let params = {};
                try {
                    params = JSON.parse(executionParams.value || '{}');
                } catch (e) {
                    alert('Erreur de format JSON dans les paramètres');
                    return;
                }
                
                executeButton.disabled = true;
                executeButton.textContent = 'Exécution en cours...';
                executeOutput.innerHTML = 'Exécution du module en cours...';
                executeOutputSection.style.display = 'block';
                
                fetch(`/api/virtual-code/execute/${currentModuleId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        params: params
                    })
                })
                .then(response => response.json())
                .then(data => {
                    executeButton.disabled = false;
                    executeButton.textContent = 'Exécuter';
                    
                    if (data.success) {
                        executeOutput.innerHTML = `<pre>${JSON.stringify(data.result, null, 2)}</pre>`;
                    } else {
                        executeOutput.innerHTML = `<p>Erreur lors de l'exécution du module: ${data.message || 'Erreur inconnue'}</p>`;
                    }
                })
                .catch(error => {
                    executeButton.disabled = false;
                    executeButton.textContent = 'Exécuter';
                    executeOutput.innerHTML = `<p>Erreur de connexion: ${error.message}</p>`;
                    console.error('Erreur lors de l\'exécution du module:', error);
                });
            });
            
            // Initialisation
            checkSystemStatus();
            loadModules();
            
            // Rafraîchir la liste des modules toutes les 60 secondes
            setInterval(loadModules, 60000);
            
            // Vérifier le statut du système toutes les 30 secondes
            setInterval(checkSystemStatus, 30000);
        });
    </script>
</body>
</html>
