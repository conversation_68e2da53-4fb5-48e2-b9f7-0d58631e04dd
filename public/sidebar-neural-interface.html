<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent à Mémoire Thermique</title>
    
    <style>
        /* Styles de base */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #ecf0f1;
            color: #333;
            overflow: hidden;
        }
        
        /* Layout principal avec sidebar */
        .main-container {
            display: flex;
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        
        /* Sidebar pour l'historique des conversations */
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            display: flex;
            flex-direction: column;
            z-index: 10;
            box-shadow: 2px 0 5px rgba(0,0,0,0.2);
        }
        
        .sidebar-header {
            padding: 1rem;
            background-color: #1a2530;
            border-bottom: 1px solid #34495e;
        }
        
        .sidebar-header h2 {
            margin: 0;
            font-size: 1.2rem;
        }
        
        .conversation-list {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }
        
        .conversation-item {
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .conversation-item:hover {
            background-color: #34495e;
        }
        
        .conversation-item.active {
            background-color: #3498db;
        }
        
        .new-conversation-btn {
            margin: 1rem;
            padding: 0.5rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .new-conversation-btn:hover {
            background-color: #2980b9;
        }
        
        /* Zone principale de contenu */
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 5;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #34495e;
            color: white;
            padding: 1rem;
            z-index: 6;
        }
        
        .logo h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .status-container {
            display: flex;
            align-items: center;
        }
        
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            background-color: #2ecc71;
        }
        
        main {
            flex: 1;
            display: flex;
            overflow: hidden;
            position: relative;
        }
        
        .conversation-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 2;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: rgba(249, 249, 249, 0.7);
        }
        
        .message {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .user-message {
            justify-content: flex-end;
        }
        
        .agent-message {
            justify-content: flex-start;
        }
        
        .message-bubble {
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .user-message .message-bubble {
            background-color: #3498db;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .agent-message .message-bubble {
            background-color: #f1f1f1;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        
        .input-container {
            display: flex;
            padding: 1rem;
            background-color: rgba(255, 255, 255, 0.9);
            border-top: 1px solid #ddd;
        }
        
        #user-input {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
            min-height: 20px;
            max-height: 150px;
            background-color: white;
        }
        
        #send-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            width: 80px;
            margin-left: 0.5rem;
            cursor: pointer;
            font-weight: bold;
        }
        
        #send-button:hover {
            background-color: #2980b9;
        }
        
        /* Animation neuronale en arrière-plan */
        .neural-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar pour l'historique des conversations -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Historique des conversations</h2>
            </div>
            <div class="conversation-list" id="conversation-list">
                <div class="conversation-item active">
                    Conversation actuelle
                </div>
                <div class="conversation-item">
                    Aide avec Python - 05/05
                </div>
                <div class="conversation-item">
                    Analyse de données - 03/05
                </div>
                <div class="conversation-item">
                    Recherche IA - 01/05
                </div>
            </div>
            <button class="new-conversation-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Nouvelle conversation
            </button>
        </div>
        
        <!-- Zone principale de contenu -->
        <div class="content-area">
            <!-- Conteneur pour l'animation neuronale en arrière-plan -->
            <div class="neural-background" id="neural-background"></div>
            
            <header>
                <div class="logo">
                    <h1>Agent à Mémoire Thermique</h1>
                </div>
                <div class="status-container">
                    <span id="app-status" class="status">Connecté</span>
                </div>
            </header>
            
            <main>
                <div class="conversation-container">
                    <div class="messages" id="message-list">
                        <!-- Les messages seront ajoutés ici dynamiquement -->
                        <div class="message agent-message">
                            <div class="message-bubble">
                                Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd'hui ?
                            </div>
                        </div>
                    </div>
                    <div class="input-container">
                        <textarea id="user-input" placeholder="Tapez votre message ici..."></textarea>
                        <button id="send-button">Envoyer</button>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Script pour l'animation neuronale -->
    <script src="/js/neural-animation.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser l'animation neuronale
            const neuralAnimation = new NeuralAnimation('neural-background', {
                nodeCount: 120,
                connectionCount: 250,
                nodeColor: '#2980b9',
                activeNodeColor: '#e74c3c',
                connectionColor: 'rgba(41, 128, 185, 0.5)',
                activeConnectionColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(255, 255, 255, 0.0)',
                nodeSize: 3,
                maxDistance: 150,
                animate: true,
                veilleMode: false,
                pulseFrequency: 2000,
                activityLevel: 0.5
            });
            
            // Éléments du DOM
            const messageList = document.getElementById('message-list');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const conversationItems = document.querySelectorAll('.conversation-item');
            const newConversationBtn = document.querySelector('.new-conversation-btn');
            
            // Fonction pour ajouter un message à la conversation
            function addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = role === 'user' ? 'message user-message' : 'message agent-message';
                
                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = 'message-bubble';
                bubbleDiv.textContent = content;
                
                messageDiv.appendChild(bubbleDiv);
                messageList.appendChild(messageDiv);
                
                // Faire défiler vers le bas
                messageList.scrollTop = messageList.scrollHeight;
            }
            
            // Gérer l'envoi de message
            function sendMessage() {
                const message = userInput.value.trim();
                if (message) {
                    // Ajouter le message de l'utilisateur à la conversation
                    addMessage('user', message);
                    
                    // Augmenter l'activité de l'animation neuronale
                    if (neuralAnimation) {
                        neuralAnimation.options.activityLevel = 0.8;
                        
                        // Activer plusieurs nœuds pour simuler l'activité cérébrale
                        for (let i = 0; i < 20; i++) {
                            setTimeout(() => {
                                const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                                neuralAnimation.activateNode(randomNodeId, 1500);
                            }, i * 100);
                        }
                        
                        // Revenir à un niveau d'activité normal après un délai
                        setTimeout(() => {
                            neuralAnimation.options.activityLevel = 0.5;
                        }, 3000);
                    }
                    
                    // Simuler une réponse de l'agent après un court délai
                    setTimeout(function() {
                        addMessage('agent', 'Je suis désolé, je ne peux pas traiter votre demande pour le moment. Veuillez réessayer plus tard.');
                    }, 1500);
                    
                    // Vider le champ de saisie
                    userInput.value = '';
                }
            }
            
            // Événement de clic sur le bouton d'envoi
            sendButton.addEventListener('click', sendMessage);
            
            // Événement de pression de la touche Entrée dans le champ de saisie
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Gestion des conversations dans la sidebar
            conversationItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Retirer la classe active de tous les éléments
                    conversationItems.forEach(i => i.classList.remove('active'));
                    // Ajouter la classe active à l'élément cliqué
                    this.classList.add('active');
                });
            });
            
            // Nouvelle conversation
            newConversationBtn.addEventListener('click', function() {
                // Créer un nouvel élément de conversation
                const newItem = document.createElement('div');
                newItem.className = 'conversation-item active';
                newItem.textContent = 'Nouvelle conversation';
                
                // Retirer la classe active de tous les éléments
                conversationItems.forEach(i => i.classList.remove('active'));
                
                // Ajouter le nouvel élément à la liste
                const conversationList = document.getElementById('conversation-list');
                conversationList.insertBefore(newItem, conversationList.firstChild);
                
                // Vider la conversation actuelle
                messageList.innerHTML = '';
                addMessage('agent', 'Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd\'hui ?');
                
                // Ajouter l'événement de clic au nouvel élément
                newItem.addEventListener('click', function() {
                    conversationItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
