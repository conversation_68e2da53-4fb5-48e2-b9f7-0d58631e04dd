<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord d'Apprentissage | Mémoire Thermique</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="/css/learning-dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.4.0/dist/chartjs-plugin-annotation.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gauge-js-web-component@0.1.0/dist/gauge-web-component.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Tableau de Bord d'Apprentissage Accéléré</h1>
            <button id="refreshBtn" class="refresh-button">Rafraîchir</button>
        </div>

        <!-- Section Progression -->
        <div class="dashboard-row">
            <div class="dashboard-card">
                <div class="dashboard-card-header">Progression d'Apprentissage</div>
                <div class="progress-section">
                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-label">Progression Globale</div>
                            <div id="currentProgress" class="stat-value highlight-value">0.0%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Phase Actuelle</div>
                            <div id="currentPhase" class="phase-badge badge-initialisation">Initialisation</div>
                        </div>
                    </div>
                    <div class="progress-bar-container">
                        <div id="progressBar" class="progress-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Section Vitesse -->
        <div class="dashboard-row">
            <div class="dashboard-card">
                <div class="dashboard-card-header">Vitesse d'Apprentissage</div>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-label">Vitesse Actuelle</div>
                        <div id="currentSpeed" class="stat-value highlight-value">0 items/min</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Vitesse de Pointe</div>
                        <div id="peakSpeed" class="stat-value">0 items/min</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Mode d'Apprentissage</div>
                        <div id="learningMode" class="stat-value">NORMAL</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Taux d'Apprentissage</div>
                        <div id="learningRate" class="stat-value">1.00</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="speedChart"></canvas>
                </div>
                
                <div class="mode-controls">
                    <button class="learning-mode-btn active" data-mode="normal">NORMAL</button>
                    <button class="learning-mode-btn" data-mode="turbo">TURBO</button>
                    <button class="learning-mode-btn" data-mode="hyper">HYPER</button>
                    <button class="learning-mode-btn" data-mode="quantum">QUANTUM</button>
                </div>
                
                <div style="text-align: center;">
                    <button id="testLearningBtn" class="test-button">Tester l'Apprentissage</button>
                </div>
            </div>
        </div>

        <!-- Section Statistiques -->
        <div class="dashboard-row">
            <div class="dashboard-card">
                <div class="dashboard-card-header">Statistiques d'Apprentissage</div>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-label">Total d'Items Appris</div>
                        <div id="totalItems" class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Apprentissages Réussis</div>
                        <div id="successfulItems" class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Apprentissages Échoués</div>
                        <div id="failedItems" class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Taux de Réussite</div>
                        <div id="successRate" class="stat-value">0%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Sujets Maîtrisés</div>
                        <div id="masteredTopics" class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Achèvement Estimé</div>
                        <div id="estimatedCompletion" class="stat-value">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Jauges -->
        <div class="dashboard-row">
            <div class="dashboard-card">
                <div class="dashboard-card-header">Jauges de Performance</div>
                <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                    <div style="flex: 1; min-width: 250px; text-align: center; padding: 10px;">
                        <h3>Vitesse d'Apprentissage</h3>
                        <div class="gauge-container">
                            <gauge-component
                                id="speedGauge"
                                min="0"
                                max="10000"
                                value="0"
                                title="items/min"
                                color-ranges='[
                                    {"start": 0, "end": 100, "color": "#CCCCCC"},
                                    {"start": 100, "end": 1000, "color": "#4CAF50"},
                                    {"start": 1000, "end": 5000, "color": "#FF9800"},
                                    {"start": 5000, "end": 10000, "color": "#F44336"}
                                ]'
                            ></gauge-component>
                        </div>
                    </div>
                    <div style="flex: 1; min-width: 250px; text-align: center; padding: 10px;">
                        <h3>Taux d'Accélération</h3>
                        <div class="gauge-container">
                            <gauge-component
                                id="rateGauge"
                                min="0"
                                max="12"
                                value="1"
                                title="facteur"
                                color-ranges='[
                                    {"start": 0, "end": 1, "color": "#CCCCCC"},
                                    {"start": 1, "end": 3, "color": "#4CAF50"},
                                    {"start": 3, "end": 6, "color": "#FF9800"},
                                    {"start": 6, "end": 12, "color": "#F44336"}
                                ]'
                            ></gauge-component>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialiser les jauges
        window.gauges = {
            speedGauge: document.getElementById('speedGauge'),
            rateGauge: document.getElementById('rateGauge')
        };
    </script>
    <script src="/js/learning-dashboard.js"></script>
    <script>
        // Ajouter les événements pour les boutons
        document.getElementById('refreshBtn').addEventListener('click', updateDashboard);
        document.getElementById('testLearningBtn').addEventListener('click', function() {
            testLearning(50, 0.8);
        });
    </script>
</body>
</html>