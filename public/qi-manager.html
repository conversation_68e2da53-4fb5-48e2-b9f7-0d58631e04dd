<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire QI - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Principal */
        .qi-header {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .qi-header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .qi-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .qi-title i {
            font-size: 2.5rem;
            color: #ff69b4;
            animation: pulse 2s infinite;
        }

        .qi-title h1 {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #ff69b4, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .qi-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-left: 10px;
        }

        .qi-nav {
            display: flex;
            gap: 15px;
        }

        .qi-nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .qi-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .qi-nav-btn.home {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
        }

        .qi-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 25px;
            color: #4caf50;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        /* Container Principal */
        .qi-main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .qi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .qi-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .qi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .qi-card h2 {
            color: #ff69b4;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qi-card h2 i {
            color: #ffa726;
        }

        .qi-display {
            text-align: center;
            margin: 30px 0;
        }

        .qi-value {
            font-size: 4rem;
            font-weight: bold;
            background: linear-gradient(135deg, #ff69b4, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .qi-label {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 12px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 5px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffa726;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff69b4, #ffa726);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .test-section {
            margin-top: 20px;
        }

        .test-btn {
            background: linear-gradient(135deg, #4caf50, #45a049);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 15px 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
            margin: 10px 0;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .qi-grid {
                grid-template-columns: 1fr;
            }
            
            .qi-nav {
                flex-direction: column;
                gap: 10px;
            }
            
            .qi-header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Principal -->
    <div class="qi-header">
        <div class="qi-header-content">
            <div class="qi-title">
                <i class="fas fa-brain"></i>
                <h1>Gestionnaire QI</h1>
                <span class="qi-subtitle">Système d'évaluation cognitive avancé</span>
            </div>
            <div class="qi-nav">
                <a href="/" class="qi-nav-btn home">
                    <i class="fas fa-home"></i>
                    Accueil
                </a>
                <a href="/dashboard-master.html" class="qi-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    Tableau de Bord
                </a>
                <a href="/chat" class="qi-nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
            </div>
            <div class="qi-status">
                <div class="status-indicator"></div>
                <span>QI Actif</span>
            </div>
        </div>
    </div>

    <!-- Container Principal -->
    <div class="qi-main-container">
        <!-- Grille principale -->
        <div class="qi-grid">
            <!-- Affichage QI Principal -->
            <div class="qi-card">
                <h2><i class="fas fa-brain"></i> QI de Jean-Luc Passave</h2>
                <div class="qi-display">
                    <div class="qi-value" id="qi-value">225</div>
                    <div class="qi-label">Quotient Intellectuel</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 90%"></div>
                </div>
                <div class="control-btn" onclick="refreshQI()">
                    <i class="fas fa-sync"></i> Actualiser QI
                </div>
            </div>

            <!-- Statistiques Cognitives -->
            <div class="qi-card">
                <h2><i class="fas fa-chart-line"></i> Statistiques Cognitives</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="neurons-count">145</div>
                        <div class="stat-label">Neurones Actifs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="connections-count">717</div>
                        <div class="stat-label">Connexions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="efficiency">95%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="learning-rate">8.5</div>
                        <div class="stat-label">Taux d'Apprentissage</div>
                    </div>
                </div>
            </div>

            <!-- Tests Cognitifs -->
            <div class="qi-card">
                <h2><i class="fas fa-microscope"></i> Tests Cognitifs</h2>
                <div class="test-section">
                    <button class="test-btn" onclick="runLogicTest()">
                        <i class="fas fa-puzzle-piece"></i>
                        Test de Logique
                    </button>
                    <button class="test-btn" onclick="runMemoryTest()">
                        <i class="fas fa-memory"></i>
                        Test de Mémoire
                    </button>
                    <button class="test-btn" onclick="runCreativityTest()">
                        <i class="fas fa-lightbulb"></i>
                        Test de Créativité
                    </button>
                    <button class="test-btn" onclick="runComprehensionTest()">
                        <i class="fas fa-book-open"></i>
                        Test de Compréhension
                    </button>
                </div>
            </div>

            <!-- Amélioration Cognitive -->
            <div class="qi-card">
                <h2><i class="fas fa-rocket"></i> Amélioration Cognitive</h2>
                <div class="control-btn" onclick="enhanceQI()">
                    <i class="fas fa-arrow-up"></i> Améliorer QI
                </div>
                <div class="control-btn" onclick="optimizeNeurons()">
                    <i class="fas fa-cogs"></i> Optimiser Neurones
                </div>
                <div class="control-btn" onclick="boostLearning()">
                    <i class="fas fa-bolt"></i> Boost Apprentissage
                </div>
                <div class="control-btn" onclick="analyzePerformance()">
                    <i class="fas fa-chart-bar"></i> Analyser Performance
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentQI = 225;
        let isTestRunning = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Gestionnaire QI initialisé');
            loadQIData();
            setInterval(updateStats, 5000); // Mise à jour toutes les 5 secondes
        });

        // Charger les données QI
        async function loadQIData() {
            try {
                const response = await fetch('/api/qi/current');
                if (response.ok) {
                    const data = await response.json();
                    currentQI = data.qi || 225;
                    document.getElementById('qi-value').textContent = currentQI;
                    updateProgressBar();
                }
            } catch (error) {
                console.warn('API QI non disponible, utilisation de données par défaut');
                document.getElementById('qi-value').textContent = currentQI;
            }
        }

        // Actualiser le QI
        async function refreshQI() {
            showNotification('🔄 Actualisation du QI...', 'info');
            await loadQIData();
            showNotification('✅ QI actualisé !', 'success');
        }

        // Mettre à jour la barre de progression
        function updateProgressBar() {
            const percentage = Math.min((currentQI / 250) * 100, 100);
            document.querySelector('.progress-fill').style.width = percentage + '%';
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const neurons = 145 + Math.floor(Math.random() * 10);
            const connections = 717 + Math.floor(Math.random() * 50);
            const efficiency = 95 + Math.floor(Math.random() * 5);
            const learningRate = (8.5 + Math.random() * 1.5).toFixed(1);

            document.getElementById('neurons-count').textContent = neurons;
            document.getElementById('connections-count').textContent = connections;
            document.getElementById('efficiency').textContent = efficiency + '%';
            document.getElementById('learning-rate').textContent = learningRate;
        }

        // Tests cognitifs
        async function runLogicTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('🧩 Test de logique en cours...', 'info');
            
            setTimeout(() => {
                const score = 85 + Math.floor(Math.random() * 15);
                showNotification(`✅ Test terminé ! Score: ${score}/100`, 'success');
                isTestRunning = false;
            }, 3000);
        }

        async function runMemoryTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('🧠 Test de mémoire en cours...', 'info');
            
            setTimeout(() => {
                const score = 90 + Math.floor(Math.random() * 10);
                showNotification(`✅ Test terminé ! Score: ${score}/100`, 'success');
                isTestRunning = false;
            }, 2500);
        }

        async function runCreativityTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('💡 Test de créativité en cours...', 'info');
            
            setTimeout(() => {
                const score = 88 + Math.floor(Math.random() * 12);
                showNotification(`✅ Test terminé ! Score: ${score}/100`, 'success');
                isTestRunning = false;
            }, 4000);
        }

        async function runComprehensionTest() {
            if (isTestRunning) return;
            isTestRunning = true;
            showNotification('📖 Test de compréhension en cours...', 'info');
            
            setTimeout(() => {
                const score = 92 + Math.floor(Math.random() * 8);
                showNotification(`✅ Test terminé ! Score: ${score}/100`, 'success');
                isTestRunning = false;
            }, 3500);
        }

        // Améliorations cognitives
        async function enhanceQI() {
            showNotification('🚀 Amélioration du QI en cours...', 'info');
            
            try {
                const response = await fetch('/api/qi/enhance', { method: 'POST' });
                if (response.ok) {
                    currentQI += Math.floor(Math.random() * 3) + 1;
                    document.getElementById('qi-value').textContent = currentQI;
                    updateProgressBar();
                    showNotification('✅ QI amélioré !', 'success');
                } else {
                    showNotification('⚠️ Amélioration simulée', 'warning');
                }
            } catch (error) {
                showNotification('⚠️ Amélioration simulée', 'warning');
            }
        }

        async function optimizeNeurons() {
            showNotification('⚙️ Optimisation des neurones...', 'info');
            setTimeout(() => {
                showNotification('✅ Neurones optimisés !', 'success');
                updateStats();
            }, 2000);
        }

        async function boostLearning() {
            showNotification('⚡ Boost d\'apprentissage...', 'info');
            setTimeout(() => {
                showNotification('✅ Apprentissage boosté !', 'success');
                updateStats();
            }, 1500);
        }

        async function analyzePerformance() {
            showNotification('📊 Analyse des performances...', 'info');
            setTimeout(() => {
                showNotification('✅ Analyse terminée ! Performance excellente.', 'success');
            }, 2500);
        }

        // Système de notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
                color: ${type === 'warning' ? '#000' : '#fff'};
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 10000;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
