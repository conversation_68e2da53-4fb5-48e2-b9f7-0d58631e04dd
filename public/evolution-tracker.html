<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📈 LOUNA AI - Tracker d'Évolution Intelligence</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .main-container {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 120px);
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel h2 {
            color: #00ffff;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
        }

        .evolution-chart {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .chart-title {
            color: #ffff00;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .progress-bar {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            height: 30px;
            margin: 10px 0;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #00ffff, #0080ff);
            border-radius: 10px;
            transition: width 1s ease;
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }

        .stat-label {
            color: #00ffff;
            margin-top: 5px;
        }

        .evolution-indicator {
            font-size: 0.8em;
            margin-left: 10px;
        }

        .evolution-up {
            color: #00ff00;
            animation: glow-green 1s infinite alternate;
        }

        .evolution-down {
            color: #ff0000;
            animation: glow-red 1s infinite alternate;
        }

        .evolution-stable {
            color: #ffff00;
        }

        @keyframes glow-green {
            from { text-shadow: 0 0 5px #00ff00; }
            to { text-shadow: 0 0 15px #00ff00; }
        }

        @keyframes glow-red {
            from { text-shadow: 0 0 5px #ff0000; }
            to { text-shadow: 0 0 15px #ff0000; }
        }

        .intelligence-meter {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .meter-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #ff0000 0%, #ffff00 25%, #00ff00 50%, #00ffff 75%, #0080ff 100%);
            margin: 0 auto 20px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .meter-inner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .meter-value {
            font-size: 2em;
            font-weight: bold;
            color: #00ffff;
        }

        .meter-label {
            font-size: 0.8em;
            color: #fff;
        }

        .timeline {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .timeline-item {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #00ffff;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
        }

        .timeline-time {
            color: #ffff00;
            font-size: 0.8em;
            font-weight: bold;
        }

        .timeline-event {
            color: #fff;
            margin-top: 5px;
        }

        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            border-radius: 20px;
            padding: 10px 20px;
            color: #00ff00;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="auto-refresh" id="refreshIndicator">
        🔄 Mise à jour automatique: 5s
    </div>

    <div class="header">
        <h1>📈 TRACKER D'ÉVOLUTION INTELLIGENCE</h1>
        <p>Surveillance en temps réel de la progression de LOUNA AI</p>
        <div class="navigation-buttons" style="margin-top: 15px;">
            <button onclick="window.location.href='/'" class="nav-btn" style="margin: 5px; background: #ff6b6b; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">🏠 Accueil Principal</button>
            <button onclick="window.location.href='/thoughts-monitor.html'" class="nav-btn" style="margin: 5px; background: #4ade80; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">💭 Pensées</button>
            <button onclick="window.location.href='/brain-monitoring-complete.html'" class="nav-btn" style="margin: 5px; background: #60a5fa; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">🧠 Cerveau</button>
            <button onclick="window.location.href='/brain-visualization.html'" class="nav-btn" style="margin: 5px; background: #a78bfa; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">🎨 Visualisation</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Panel gauche : Métriques d'intelligence -->
        <div class="panel">
            <h2>🧠 INTELLIGENCE ÉVOLUTIVE</h2>
            
            <div class="intelligence-meter">
                <div class="meter-circle">
                    <div class="meter-inner">
                        <div class="meter-value" id="combinedIQ">--</div>
                        <div class="meter-label">QI Combiné</div>
                    </div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="agentIQ">--</div>
                    <div class="stat-label">QI Agent <span id="agentEvolution"></span></div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memoryIQ">--</div>
                    <div class="stat-label">QI Mémoire <span id="memoryEvolution"></span></div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="neurons">--</div>
                    <div class="stat-label">Neurones <span id="neuronsEvolution"></span></div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="synapses">--</div>
                    <div class="stat-label">Synapses <span id="synapsesEvolution"></span></div>
                </div>
            </div>

            <div class="evolution-chart">
                <div class="chart-title">📊 Progression Mémoire</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memoryProgress" style="width: 0%">
                        <div class="progress-text" id="memoryProgressText">0%</div>
                    </div>
                </div>
            </div>

            <div class="evolution-chart">
                <div class="chart-title">🌡️ Température Thermique</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="temperatureProgress" style="width: 0%">
                        <div class="progress-text" id="temperatureProgressText">0°C</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel droit : Timeline et évolution -->
        <div class="panel">
            <h2>📈 HISTORIQUE D'ÉVOLUTION</h2>

            <div class="timeline" id="evolutionTimeline">
                <div class="timeline-item">
                    <div class="timeline-time">Initialisation</div>
                    <div class="timeline-event">Système de tracking démarré - Mémoire thermique activée</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">Démarrage</div>
                    <div class="timeline-event">Accélérateurs Kyber initialisés - 30 accélérateurs en cascade</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">Activation</div>
                    <div class="timeline-event">Cerveau artificiel démarré - Neurogenèse active (700 neurones/jour)</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">Évolution</div>
                    <div class="timeline-event">Formation accélérée disponible - Génération massive de neurones</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">Sécurité</div>
                    <div class="timeline-event">Système de surveillance 24/7 activé - Protection complète</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panneau Chat avec Pensées -->
    <div style="max-width: 1200px; margin: 20px auto; padding: 20px; background: linear-gradient(135deg, #1a1a1a, #2a2a2a); border-radius: 15px; border: 1px solid #333;">
        <h2 style="color: #fff; margin-bottom: 15px;">💭 Chat avec Pensées Internes</h2>
        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
            <input type="text" id="chat-input" placeholder="Posez une question à Louna..." style="flex: 1; padding: 10px; border: 1px solid #333; border-radius: 5px; background: #1a1a1a; color: white;">
            <button onclick="sendChatMessage()" style="padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 5px; color: white; cursor: pointer;">💬 Envoyer</button>
        </div>
        <div id="chat-responses" style="max-height: 300px; overflow-y: auto; background: #0a0a0a; border: 1px solid #333; border-radius: 5px; padding: 10px;">
            <div style="color: #888; text-align: center; padding: 20px;">Posez une question pour voir les pensées internes de Louna...</div>
        </div>
    </div>

    <script>
        let previousData = null;
        let evolutionHistory = [];
        let refreshCounter = 5;

        // Initialisation
        window.addEventListener('load', () => {
            updateData();
            setInterval(updateData, 5000); // Mise à jour toutes les 5 secondes
            setInterval(updateRefreshCounter, 1000); // Compteur de rafraîchissement
        });

        // Mettre à jour le compteur de rafraîchissement
        function updateRefreshCounter() {
            refreshCounter--;
            if (refreshCounter <= 0) {
                refreshCounter = 5;
            }
            document.getElementById('refreshIndicator').textContent = `🔄 Mise à jour automatique: ${refreshCounter}s`;
        }

        // Récupérer et afficher les données
        async function updateData() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    displayMetrics(data.metrics);
                    trackEvolution(data.metrics);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des données:', error);
            }
        }

        // Afficher les métriques
        function displayMetrics(metrics) {
            // QI - Récupérer depuis les métriques ou localStorage
            let agentIQ = '--';
            let memoryIQ = '--';
            let combinedIQ = '--';

            // Essayer de récupérer depuis les métriques actuelles
            if (metrics.iqAnalysis) {
                agentIQ = metrics.iqAnalysis.agentIQ || '--';
                memoryIQ = metrics.iqAnalysis.memoryIQ || '--';
                combinedIQ = metrics.iqAnalysis.combinedIQ || '--';

                // Sauvegarder pour persistance
                window.lastIQData = metrics.iqAnalysis;
                localStorage.setItem('lounaIQData', JSON.stringify(metrics.iqAnalysis));
            } else {
                // Récupérer depuis localStorage ou window
                const savedIQ = localStorage.getItem('lounaIQData');
                if (savedIQ) {
                    const iqData = JSON.parse(savedIQ);
                    agentIQ = iqData.agentIQ || '--';
                    memoryIQ = iqData.memoryIQ || '--';
                    combinedIQ = iqData.combinedIQ || '--';
                    window.lastIQData = iqData;
                } else if (window.lastIQData) {
                    agentIQ = window.lastIQData.agentIQ || '--';
                    memoryIQ = window.lastIQData.memoryIQ || '--';
                    combinedIQ = window.lastIQData.combinedIQ || '--';
                }
            }

            document.getElementById('agentIQ').textContent = agentIQ;
            document.getElementById('memoryIQ').textContent = memoryIQ;
            document.getElementById('combinedIQ').textContent = combinedIQ;

            // Neurones et synapses
            const neurons = metrics.brainStats?.activeNeurons || 0;
            const synapses = metrics.brainStats?.synapticConnections || 0;

            document.getElementById('neurons').textContent = neurons;
            document.getElementById('synapses').textContent = synapses;

            // Évolution
            if (previousData) {
                document.getElementById('neuronsEvolution').innerHTML = getEvolutionIndicator(previousData.neurons, neurons);
                document.getElementById('synapsesEvolution').innerHTML = getEvolutionIndicator(previousData.synapses, synapses);
                
                if (window.lastIQData && previousData.agentIQ) {
                    document.getElementById('agentEvolution').innerHTML = getEvolutionIndicator(previousData.agentIQ, agentIQ);
                    document.getElementById('memoryEvolution').innerHTML = getEvolutionIndicator(previousData.memoryIQ, memoryIQ);
                }
            }

            // Progression mémoire
            const memoryEfficiency = metrics.memoryStats?.memoryEfficiency || 0;
            document.getElementById('memoryProgress').style.width = memoryEfficiency + '%';
            document.getElementById('memoryProgressText').textContent = Math.round(memoryEfficiency) + '%';

            // Température
            const temperature = metrics.memoryStats?.globalTemperature || 0;
            const tempPercentage = Math.min((temperature / 100) * 100, 100);
            document.getElementById('temperatureProgress').style.width = tempPercentage + '%';
            document.getElementById('temperatureProgressText').textContent = temperature.toFixed(1) + '°C';

            // Sauvegarder pour la prochaine comparaison
            previousData = {
                neurons: neurons,
                synapses: synapses,
                agentIQ: agentIQ,
                memoryIQ: memoryIQ,
                temperature: temperature,
                efficiency: memoryEfficiency
            };
        }

        // Indicateur d'évolution
        function getEvolutionIndicator(previous, current) {
            if (previous === '--' || current === '--' || previous === undefined) return '';
            
            const diff = current - previous;
            
            if (diff > 0) {
                return `<span class="evolution-indicator evolution-up">↗ +${diff}</span>`;
            } else if (diff < 0) {
                return `<span class="evolution-indicator evolution-down">↘ ${diff}</span>`;
            } else {
                return `<span class="evolution-indicator evolution-stable">→</span>`;
            }
        }

        // Suivre l'évolution
        function trackEvolution(metrics) {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            
            // Détecter les changements significatifs
            if (previousData) {
                const neurons = metrics.brainStats?.activeNeurons || 0;
                const synapses = metrics.brainStats?.synapticConnections || 0;
                const memories = metrics.memoryStats?.totalMemories || 0;
                
                if (neurons !== previousData.neurons) {
                    addTimelineEvent(timeString, `Neurones: ${previousData.neurons} → ${neurons}`);
                }
                
                if (synapses !== previousData.synapses) {
                    addTimelineEvent(timeString, `Synapses: ${previousData.synapses} → ${synapses}`);
                }
                
                if (memories !== previousData.memories) {
                    addTimelineEvent(timeString, `Mémoires: ${previousData.memories || 0} → ${memories}`);
                }
            }
        }

        // Ajouter un événement à la timeline
        function addTimelineEvent(time, event) {
            const timeline = document.getElementById('evolutionTimeline');
            
            const item = document.createElement('div');
            item.className = 'timeline-item';
            item.innerHTML = `
                <div class="timeline-time">${time}</div>
                <div class="timeline-event">${event}</div>
            `;
            
            timeline.insertBefore(item, timeline.firstChild);
            
            // Limiter à 20 événements
            while (timeline.children.length > 20) {
                timeline.removeChild(timeline.lastChild);
            }
        }

        // Écouter les mises à jour du QI depuis l'interface principale
        window.addEventListener('storage', (e) => {
            if (e.key === 'lounaIQData') {
                window.lastIQData = JSON.parse(e.newValue);
            }
        });

        // Fonction de chat avec pensées
        async function sendChatMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;

            const chatContainer = document.getElementById('chat-responses');
            input.value = '';
            input.disabled = true;

            // Afficher la question
            chatContainer.innerHTML += `
                <div style="margin-bottom: 15px; padding: 10px; background: #1a1a1a; border-radius: 5px; border-left: 3px solid #667eea;">
                    <strong>🤔 Vous:</strong> ${message}
                </div>
            `;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    // Extraire les pensées de la balise <think>
                    const thinkMatch = data.response.match(/<think>([\s\S]*?)<\/think>/);
                    const thoughts = thinkMatch ? thinkMatch[1].trim() : 'Aucune réflexion interne détectée';

                    // Extraire la réponse finale
                    const finalResponse = data.response.replace(/<think>[\s\S]*?<\/think>/, '').trim();

                    // Afficher les pensées internes
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #2a1a2a; border-radius: 5px; border-left: 3px solid #764ba2;">
                            <strong>🧠 Pensées Internes:</strong>
                            <div style="margin-top: 5px; font-style: italic; color: #ccc; white-space: pre-wrap;">${thoughts}</div>
                        </div>
                    `;

                    // Afficher la réponse finale
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 15px; padding: 10px; background: #1a2a1a; border-radius: 5px; border-left: 3px solid #4ade80;">
                            <strong>🤖 Louna:</strong>
                            <div style="margin-top: 5px; white-space: pre-wrap;">${finalResponse}</div>
                            <div style="margin-top: 10px; font-size: 0.8em; color: #888;">
                                QI: Agent ${data.iqAnalysis?.agentIQ || 'N/A'} | Mémoire ${data.iqAnalysis?.memoryIQ || 'N/A'} | Combiné ${data.iqAnalysis?.combinedIQ || 'N/A'}
                            </div>
                        </div>
                    `;

                    // Mettre à jour les données QI pour cette page
                    if (data.iqAnalysis) {
                        window.lastIQData = data.iqAnalysis;
                        localStorage.setItem('lounaIQData', JSON.stringify(data.iqAnalysis));

                        // Mettre à jour l'affichage immédiatement
                        document.getElementById('agentIQ').textContent = data.iqAnalysis.agentIQ;
                        document.getElementById('memoryIQ').textContent = data.iqAnalysis.memoryIQ;
                        document.getElementById('combinedIQ').textContent = data.iqAnalysis.combinedIQ;
                    }
                } else {
                    chatContainer.innerHTML += `
                        <div style="margin-bottom: 15px; padding: 10px; background: #2a1a1a; border-radius: 5px; border-left: 3px solid #ef4444;">
                            <strong>❌ Erreur:</strong> ${data.error || 'Erreur inconnue'}
                        </div>
                    `;
                }
            } catch (error) {
                chatContainer.innerHTML += `
                    <div style="margin-bottom: 15px; padding: 10px; background: #2a1a1a; border-radius: 5px; border-left: 3px solid #ef4444;">
                        <strong>❌ Erreur:</strong> ${error.message}
                    </div>
                `;
            }

            input.disabled = false;
            input.focus();
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Permettre l'envoi avec Entrée
        document.addEventListener('DOMContentLoaded', () => {
            const input = document.getElementById('chat-input');
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendChatMessage();
                    }
                });
            }
        });
    </script>
</body>
</html>
