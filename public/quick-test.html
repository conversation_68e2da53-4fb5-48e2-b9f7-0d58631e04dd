<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Test Rapide - LOUNA AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .test-btn {
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .test-btn:hover {
            transform: translateY(-2px);
        }

        .test-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            background: #f0f8ff;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-left-color: #4caf50;
            background: #f1f8e9;
        }

        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .manual-test {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }

        .manual-test input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 16px;
        }

        .manual-test button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ Test Rapide - LOUNA AI</h1>
        
        <div id="status" class="status disconnected">
            🔄 Vérification de la connexion...
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testConnection()">🔗 Test Connexion</button>
            <button class="test-btn" onclick="testMetrics()">📊 Test Métriques</button>
            <button class="test-btn" onclick="testSimpleChat()">💬 Test Chat Simple</button>
            <button class="test-btn" onclick="testApps()">🖥️ Test Applications</button>
            <button class="test-btn" onclick="testThoughts()">💭 Test Pensées</button>
            <button class="test-btn" onclick="testTraining()">⚡ Test Formation</button>
        </div>

        <div id="results"></div>

        <div class="manual-test">
            <h3>💬 Test Manuel</h3>
            <input type="text" id="manualInput" placeholder="Tapez votre message ici..." 
                   onkeypress="handleKeyPress(event)">
            <button onclick="sendManualMessage()">Envoyer</button>
        </div>
    </div>

    <script>
        let isConnected = false;

        // Ajouter un résultat
        function addResult(title, content, type = 'result') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        // Test de connexion
        async function testConnection() {
            addResult('🔗 Test de Connexion', 'Vérification en cours...');
            
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    isConnected = true;
                    document.getElementById('status').className = 'status connected';
                    document.getElementById('status').textContent = '✅ Connecté au serveur';
                    addResult('🔗 Connexion', JSON.stringify(data, null, 2), 'success');
                } else {
                    throw new Error('Réponse invalide');
                }
            } catch (error) {
                isConnected = false;
                document.getElementById('status').className = 'status disconnected';
                document.getElementById('status').textContent = '❌ Connexion échouée';
                addResult('🔗 Connexion', `Erreur: ${error.message}`, 'error');
            }
        }

        // Test des métriques
        async function testMetrics() {
            addResult('📊 Test Métriques', 'Récupération des métriques...');
            
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                addResult('📊 Métriques', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('📊 Métriques', `Erreur: ${error.message}`, 'error');
            }
        }

        // Test chat simple
        async function testSimpleChat() {
            addResult('💬 Test Chat', 'Envoi d\'un message simple...');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Bonjour ! Réponds juste "OK" pour confirmer que tu fonctionnes.' })
                });
                
                const data = await response.json();
                addResult('💬 Chat Simple', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
            } catch (error) {
                addResult('💬 Chat Simple', `Erreur: ${error.message}`, 'error');
            }
        }

        // Test applications
        async function testApps() {
            addResult('🖥️ Test Applications', 'Récupération de la liste...');
            
            try {
                const response = await fetch('/api/desktop/apps');
                const data = await response.json();
                addResult('🖥️ Applications', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('🖥️ Applications', `Erreur: ${error.message}`, 'error');
            }
        }

        // Test pensées
        async function testThoughts() {
            addResult('💭 Test Pensées', 'Récupération des pensées...');
            
            try {
                const response = await fetch('/api/thoughts/recent');
                const data = await response.json();
                addResult('💭 Pensées', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('💭 Pensées', `Erreur: ${error.message}`, 'error');
            }
        }

        // Test formation
        async function testTraining() {
            addResult('⚡ Test Formation', 'Test du statut de formation...');
            
            try {
                const response = await fetch('/api/training/status');
                const data = await response.json();
                addResult('⚡ Formation', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('⚡ Formation', `Erreur: ${error.message}`, 'error');
            }
        }

        // Envoyer un message manuel
        async function sendManualMessage() {
            const input = document.getElementById('manualInput');
            const message = input.value.trim();
            
            if (!message) {
                alert('Veuillez saisir un message');
                return;
            }
            
            addResult('💬 Message Manuel', `Envoi: "${message}"`);
            input.value = '';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                addResult('💬 Réponse', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
            } catch (error) {
                addResult('💬 Réponse', `Erreur: ${error.message}`, 'error');
            }
        }

        // Gérer la touche Entrée
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendManualMessage();
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            testConnection();
        });
    </script>
</body>
</html>
