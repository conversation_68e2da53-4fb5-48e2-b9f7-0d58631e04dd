<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent à Mémoire Thermique</title>
    
    <style>
        /* Styles de base */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #ecf0f1;
            color: #333;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #34495e;
            color: white;
            padding: 1rem;
        }
        
        .logo h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .status-container {
            display: flex;
            align-items: center;
        }
        
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            background-color: #2ecc71;
        }
        
        nav {
            display: flex;
            background-color: #34495e;
            padding: 0.5rem 0;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            justify-content: center;
            width: 100%;
            margin: 0;
            padding: 0;
        }
        
        nav ul li {
            margin: 0 1rem;
        }
        
        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }
        
        nav ul li a.active {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        main {
            flex: 1;
            display: flex;
            overflow: hidden;
            position: relative;
        }
        
        section {
            display: none;
            width: 100%;
            padding: 1.5rem;
            overflow-y: auto;
        }
        
        section.active {
            display: block;
        }
        
        .conversation-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 180px);
            position: relative;
            z-index: 2;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: rgba(249, 249, 249, 0.8);
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .message {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .user-message {
            justify-content: flex-end;
        }
        
        .agent-message {
            justify-content: flex-start;
        }
        
        .message-bubble {
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .user-message .message-bubble {
            background-color: #3498db;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .agent-message .message-bubble {
            background-color: #f1f1f1;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        
        .input-container {
            display: flex;
            margin-top: 1rem;
            padding: 0.5rem;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        #user-input {
            flex: 1;
            padding: 0.5rem;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
            min-height: 40px;
            background-color: transparent;
        }
        
        #send-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 0.5rem;
        }
        
        #send-button:hover {
            background-color: #2980b9;
        }
        
        footer {
            background-color: #34495e;
            color: white;
            text-align: center;
            padding: 1rem;
            font-size: 0.8rem;
        }
        
        /* Styles pour l'animation neuronale */
        .neural-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Conteneur pour l'animation neuronale en arrière-plan -->
    <div class="neural-background" id="neural-background"></div>
    
    <div class="app-container">
        <header>
            <div class="logo">
                <h1>Agent à Mémoire Thermique</h1>
            </div>
            <div class="status-container">
                <span id="app-status" class="status">Connecté</span>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="#chat" id="nav-chat" class="active">Chat</a></li>
            </ul>
        </nav>
        
        <main>
            <section id="chat" class="active">
                <div class="conversation-container">
                    <div class="messages" id="message-list">
                        <!-- Les messages seront ajoutés ici dynamiquement -->
                        <div class="message agent-message">
                            <div class="message-bubble">
                                Bonjour ! Je suis votre agent à mémoire thermique. Comment puis-je vous aider aujourd'hui ?
                            </div>
                        </div>
                    </div>
                    <div class="input-container">
                        <textarea id="user-input" placeholder="Tapez votre message ici..."></textarea>
                        <button id="send-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; 2025 Agent à Mémoire Thermique - Tous droits réservés</p>
        </footer>
    </div>
    
    <!-- Script pour l'animation neuronale -->
    <script src="/js/neural-animation.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser l'animation neuronale
            const neuralAnimation = new NeuralAnimation('neural-background', {
                nodeCount: 100,
                connectionCount: 200,
                nodeColor: '#2980b9',
                activeNodeColor: '#e74c3c',
                connectionColor: 'rgba(41, 128, 185, 0.5)',
                activeConnectionColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(255, 255, 255, 0.0)',
                nodeSize: 3,
                maxDistance: 150,
                animate: true,
                veilleMode: false,
                pulseFrequency: 2000,
                activityLevel: 0.5
            });
            
            // Éléments du DOM
            const messageList = document.getElementById('message-list');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            
            // Fonction pour ajouter un message à la conversation
            function addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = role === 'user' ? 'message user-message' : 'message agent-message';
                
                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = 'message-bubble';
                bubbleDiv.textContent = content;
                
                messageDiv.appendChild(bubbleDiv);
                messageList.appendChild(messageDiv);
                
                // Faire défiler vers le bas
                messageList.scrollTop = messageList.scrollHeight;
            }
            
            // Gérer l'envoi de message
            function sendMessage() {
                const message = userInput.value.trim();
                if (message) {
                    // Ajouter le message de l'utilisateur à la conversation
                    addMessage('user', message);
                    
                    // Augmenter l'activité de l'animation neuronale
                    if (neuralAnimation) {
                        neuralAnimation.options.activityLevel = 0.8;
                        
                        // Activer plusieurs nœuds pour simuler l'activité cérébrale
                        for (let i = 0; i < 15; i++) {
                            setTimeout(() => {
                                const randomNodeId = Math.floor(Math.random() * neuralAnimation.nodes.length);
                                neuralAnimation.activateNode(randomNodeId, 1500);
                            }, i * 100);
                        }
                        
                        // Revenir à un niveau d'activité normal après un délai
                        setTimeout(() => {
                            neuralAnimation.options.activityLevel = 0.5;
                        }, 3000);
                    }
                    
                    // Simuler une réponse de l'agent après un court délai
                    setTimeout(function() {
                        addMessage('agent', 'Je suis désolé, je ne peux pas traiter votre demande pour le moment. Veuillez réessayer plus tard.');
                    }, 1500);
                    
                    // Vider le champ de saisie
                    userInput.value = '';
                }
            }
            
            // Événement de clic sur le bouton d'envoi
            sendButton.addEventListener('click', sendMessage);
            
            // Événement de pression de la touche Entrée dans le champ de saisie
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
