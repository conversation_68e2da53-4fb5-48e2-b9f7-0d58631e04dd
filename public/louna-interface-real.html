<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Interface Réelle</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 350px;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .chat-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .reflection-section {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .metrics-bar {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 20px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #FFD700;
        }

        .metric-label {
            font-size: 0.8em;
            opacity: 0.8;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .user-message {
            background: #3498db;
            margin-left: auto;
            text-align: right;
        }

        .agent-message {
            background: #2ecc71;
            margin-right: auto;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        #messageInput {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }

        #sendButton {
            padding: 12px 24px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s;
        }

        #sendButton:hover {
            background: #c0392b;
        }

        .reflection-header {
            text-align: center;
            margin-bottom: 15px;
        }

        .reflection-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .control-btn {
            flex: 1;
            padding: 8px;
            background: #9b59b6;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .control-btn:hover {
            background: #8e44ad;
        }

        .control-btn.active {
            background: #27ae60;
        }

        .thoughts-container {
            flex: 1;
            overflow-y: auto;
            max-height: 500px;
        }

        .thought {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #f39c12;
            animation: slideIn 0.5s ease-out;
        }

        .thought-content {
            font-size: 0.9em;
            line-height: 1.4;
        }

        .thought-meta {
            font-size: 0.7em;
            opacity: 0.7;
            margin-top: 5px;
            display: flex;
            justify-content: space-between;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-active {
            background: #2ecc71;
            animation: pulse 2s infinite;
        }

        .status-inactive {
            background: #95a5a6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            color: #f39c12;
            font-style: italic;
        }

        .error {
            color: #e74c3c;
            font-weight: bold;
        }

        .success {
            color: #2ecc71;
            font-weight: bold;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }
            
            .reflection-section {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Section Chat -->
        <div class="chat-section">
            <div class="header">
                <h1>🧠 LOUNA AI - Interface Réelle</h1>
                <p>Aucune simulation - Données système réelles uniquement</p>
            </div>

            <div class="metrics-bar" id="metricsBar">
                <div class="metric">
                    <div class="metric-value" id="neuronsCount">---</div>
                    <div class="metric-label">Neurones</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="temperature">---</div>
                    <div class="metric-label">Température</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="memoryEntries">---</div>
                    <div class="metric-label">Mémoires</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="qiValue">---</div>
                    <div class="metric-label">QI</div>
                </div>
            </div>

            <div class="messages" id="messages">
                <div class="message agent-message">
                    🤖 Interface réelle initialisée ! Aucune simulation, que des données système authentiques.
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Parlez avec LOUNA AI..." />
                <button id="sendButton">Envoyer</button>
                <button id="showReflectionButton" style="background: #f39c12; margin-left: 5px; padding: 12px; border: none; border-radius: 25px; color: white; cursor: pointer;">💭 Réflexion</button>
            </div>
        </div>

        <!-- Section Réflexion -->
        <div class="reflection-section">
            <div class="reflection-header">
                <h2>🧠 Réflexions en Temps Réel</h2>
                <p>Pensées de LOUNA basées sur données système</p>
            </div>

            <div class="reflection-controls">
                <button class="control-btn" id="startReflection">▶️ Démarrer</button>
                <button class="control-btn" id="stopReflection">⏹️ Arrêter</button>
                <button class="control-btn" id="clearThoughts">🗑️ Vider</button>
            </div>

            <div class="thoughts-container" id="thoughtsContainer">
                <div class="thought">
                    <div class="thought-content">
                        💭 Système de réflexion prêt. Cliquez sur "Démarrer" pour voir mes pensées en temps réel.
                    </div>
                    <div class="thought-meta">
                        <span>Initialisation</span>
                        <span>Prêt</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let reflectionActive = false;
        let reflectionInterval = null;
        let messageCount = 0;
        let lastThoughtId = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Interface réelle LOUNA AI chargée');

            // Événements
            document.getElementById('sendButton').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            document.getElementById('startReflection').addEventListener('click', startReflection);
            document.getElementById('stopReflection').addEventListener('click', stopReflection);
            document.getElementById('clearThoughts').addEventListener('click', clearThoughts);
            document.getElementById('showReflectionButton').addEventListener('click', showCurrentReflection);

            // Démarrer les mises à jour
            updateMetrics();
            setInterval(updateMetrics, 5000); // Toutes les 5 secondes

            // Démarrer automatiquement la réflexion
            setTimeout(() => {
                startReflection();
                addMessage('system', '🧠 Réflexions en temps réel activées automatiquement');
            }, 2000);
        });

        // Envoyer message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            addMessage('user', message);
            input.value = '';
            messageCount++;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                
                if (data.success && data.response) {
                    addMessage('agent', data.response);
                } else {
                    addMessage('agent', '❌ Erreur: ' + (data.error || 'Réponse invalide'));
                }
            } catch (error) {
                addMessage('agent', '❌ Erreur de connexion: ' + error.message);
                console.error('Erreur:', error);
            }
        }

        // Ajouter message
        function addMessage(sender, text) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');

            // Gérer différents types de messages
            if (sender === 'system') {
                messageDiv.className = 'message agent-message';
                messageDiv.style.background = '#9b59b6';
                messageDiv.style.fontStyle = 'italic';
            } else if (sender === 'reflection') {
                messageDiv.className = 'message agent-message';
                messageDiv.style.background = '#f39c12';
                messageDiv.style.fontSize = '0.9em';
            } else {
                messageDiv.className = `message ${sender}-message`;
            }

            messageDiv.textContent = text;

            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;

            // Limiter le nombre de messages
            const allMessages = messages.querySelectorAll('.message');
            if (allMessages.length > 50) {
                messages.removeChild(allMessages[0]);
            }
        }

        // Mettre à jour métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success || data.brainStats) {
                    document.getElementById('neuronsCount').textContent = data.brainStats?.activeNeurons || data.neurons || '---';
                    document.getElementById('temperature').textContent = (data.thermalStats?.temperature || data.temperature || '---') + '°C';
                    document.getElementById('memoryEntries').textContent = data.thermalStats?.totalEntries || data.entries || '---';
                    document.getElementById('qiValue').textContent = data.iqStats?.combined || data.qi?.combined || '---';
                }
            } catch (error) {
                console.error('Erreur métriques:', error);
            }
        }

        // Démarrer réflexion
        async function startReflection() {
            if (reflectionActive) return;

            try {
                const response = await fetch('/api/reflection/start');
                const data = await response.json();

                if (data.success) {
                    reflectionActive = true;
                    document.getElementById('startReflection').classList.add('active');
                    document.getElementById('startReflection').textContent = '✅ Actif';

                    // Démarrer la récupération des pensées
                    reflectionInterval = setInterval(updateThoughts, 3000);
                    updateThoughts(); // Première mise à jour immédiate

                    addMessage('system', '🧠 Réflexions en temps réel démarrées');
                } else {
                    addMessage('system', '❌ Erreur démarrage réflexions: ' + (data.error || 'Erreur inconnue'));
                }
            } catch (error) {
                console.error('Erreur démarrage réflexion:', error);
                addMessage('system', '❌ Erreur connexion réflexions: ' + error.message);
            }
        }

        // Arrêter réflexion
        async function stopReflection() {
            if (!reflectionActive) return;

            try {
                const response = await fetch('/api/reflection/stop');
                const data = await response.json();

                if (data.success) {
                    reflectionActive = false;
                    document.getElementById('startReflection').classList.remove('active');
                    document.getElementById('startReflection').textContent = '▶️ Démarrer';

                    if (reflectionInterval) {
                        clearInterval(reflectionInterval);
                        reflectionInterval = null;
                    }

                    addMessage('system', '⏹️ Réflexions en temps réel arrêtées');
                } else {
                    addMessage('system', '❌ Erreur arrêt réflexions: ' + (data.error || 'Erreur inconnue'));
                }
            } catch (error) {
                console.error('Erreur arrêt réflexion:', error);
                addMessage('system', '❌ Erreur connexion arrêt: ' + error.message);
            }
        }

        // Mettre à jour pensées
        async function updateThoughts() {
            try {
                const response = await fetch('/api/reflection/thoughts?count=10');
                const data = await response.json();

                if (data.success && data.thoughts && data.thoughts.length > 0) {
                    displayThoughts(data.thoughts);

                    // Afficher la nouvelle pensée dans le chat
                    const latestThought = data.thoughts[0];
                    if (latestThought && latestThought.id !== lastThoughtId) {
                        addMessage('reflection', '💭 ' + latestThought.content);
                        lastThoughtId = latestThought.id;
                    }
                }
            } catch (error) {
                console.error('Erreur récupération pensées:', error);
                addMessage('system', '❌ Erreur récupération réflexions: ' + error.message);
            }
        }

        // Afficher pensées
        function displayThoughts(thoughts) {
            const container = document.getElementById('thoughtsContainer');
            container.innerHTML = '';
            
            thoughts.forEach(thought => {
                const thoughtDiv = document.createElement('div');
                thoughtDiv.className = 'thought';
                
                const time = new Date(thought.timestamp).toLocaleTimeString();
                
                thoughtDiv.innerHTML = `
                    <div class="thought-content">${thought.content}</div>
                    <div class="thought-meta">
                        <span>${thought.type}</span>
                        <span>${time}</span>
                    </div>
                `;
                
                container.appendChild(thoughtDiv);
            });
        }

        // Vider pensées
        function clearThoughts() {
            const container = document.getElementById('thoughtsContainer');
            container.innerHTML = `
                <div class="thought">
                    <div class="thought-content">
                        🗑️ Pensées vidées. Les nouvelles réflexions apparaîtront ici.
                    </div>
                    <div class="thought-meta">
                        <span>Système</span>
                        <span>${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
            `;

            // Vider aussi les réflexions du chat
            const messages = document.getElementById('messages');
            const reflectionMessages = messages.querySelectorAll('.message[style*="background: #f39c12"]');
            reflectionMessages.forEach(msg => msg.remove());

            addMessage('system', '🗑️ Réflexions vidées du chat et du panneau');
            lastThoughtId = null;
        }

        // Afficher réflexion actuelle
        async function showCurrentReflection() {
            try {
                const response = await fetch('/api/reflection/thoughts?count=1');
                const data = await response.json();

                if (data.success && data.thoughts && data.thoughts.length > 0) {
                    const thought = data.thoughts[0];
                    addMessage('reflection', '🧠 Réflexion actuelle: ' + thought.content);
                } else {
                    addMessage('system', '💭 Aucune réflexion disponible. Démarrez les réflexions d\'abord.');
                }
            } catch (error) {
                addMessage('system', '❌ Erreur récupération réflexion: ' + error.message);
            }
        }

        // Fonction pour tester toutes les APIs
        async function testAllAPIs() {
            addMessage('system', '🧪 Test de toutes les APIs...');

            // Test métriques
            try {
                const metricsResponse = await fetch('/api/metrics');
                const metricsData = await metricsResponse.json();
                if (metricsData.success || metricsData.brainStats) {
                    addMessage('system', '✅ API Métriques: OK');
                } else {
                    addMessage('system', '❌ API Métriques: Erreur');
                }
            } catch (error) {
                addMessage('system', '❌ API Métriques: ' + error.message);
            }

            // Test réflexions
            try {
                const reflectionResponse = await fetch('/api/reflection/state');
                const reflectionData = await reflectionResponse.json();
                if (reflectionData.success) {
                    addMessage('system', '✅ API Réflexions: OK');
                } else {
                    addMessage('system', '❌ API Réflexions: Erreur');
                }
            } catch (error) {
                addMessage('system', '❌ API Réflexions: ' + error.message);
            }

            // Test chat
            try {
                const chatResponse = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Test API' })
                });
                const chatData = await chatResponse.json();
                if (chatData.success) {
                    addMessage('system', '✅ API Chat: OK');
                } else {
                    addMessage('system', '❌ API Chat: Erreur');
                }
            } catch (error) {
                addMessage('system', '❌ API Chat: ' + error.message);
            }
        }

        // Ajouter commandes spéciales
        function handleSpecialCommands(message) {
            if (message.toLowerCase() === '/test') {
                testAllAPIs();
                return true;
            }
            if (message.toLowerCase() === '/reflection') {
                showCurrentReflection();
                return true;
            }
            if (message.toLowerCase() === '/clear') {
                document.getElementById('messages').innerHTML = '';
                addMessage('system', '🗑️ Chat vidé');
                return true;
            }
            if (message.toLowerCase() === '/help') {
                addMessage('system', '📋 Commandes: /test (tester APIs), /reflection (voir réflexion), /clear (vider chat), /help (aide)');
                return true;
            }
            return false;
        }

        // Modifier la fonction sendMessage pour gérer les commandes
        const originalSendMessage = sendMessage;
        sendMessage = async function() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // Vérifier les commandes spéciales
            if (handleSpecialCommands(message)) {
                input.value = '';
                return;
            }

            // Sinon, utiliser la fonction originale
            await originalSendMessage();
        };
    </script>
</body>
</html>
