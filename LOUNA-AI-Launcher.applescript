-- 🚀 LANCEUR LOUNA AI NATIF MACOS
-- Ce script AppleScript lance votre application LOUNA AI complète

on run
    try
        -- Afficher une notification de démarrage
        display notification "Démarrage de LOUNA AI..." with title "🧠 LOUNA AI" subtitle "Intelligence Artificielle Autonome"
        
        -- Obtenir le chemin du répertoire de l'application
        set appPath to (path to me as text)
        set appFolder to (do shell script "dirname " & quoted form of POSIX path of appPath)
        
        -- Changer vers le répertoire de l'application
        do shell script "cd " & quoted form of appFolder
        
        -- Vérifier si le serveur est déjà en cours
        try
            do shell script "lsof -i :3005"
            set serverRunning to true
        on error
            set serverRunning to false
        end try
        
        if serverRunning then
            display notification "Serveur déjà actif" with title "✅ LOUNA AI" subtitle "Connexion au serveur existant"
        else
            display notification "Démarrage du serveur..." with title "🔄 LOUNA AI" subtitle "Initialisation en cours"
            
            -- D<PERSON><PERSON>rer le serveur en arrière-plan
            do shell script "cd " & quoted form of appFolder & " && nohup node minimal-server.js > /dev/null 2>&1 &"
            
            -- Attendre que le serveur soit prêt
            delay 8
        end if
        
        -- Ouvrir l'interface dans le navigateur
        do shell script "open 'http://localhost:3005'"
        
        -- Notification de succès
        display notification "Interface ouverte avec succès!" with title "🎉 LOUNA AI" subtitle "Votre IA autonome est prête"
        
        -- Afficher un dialogue de confirmation
        display dialog "🧠 LOUNA AI lancé avec succès !

📊 Fonctionnalités actives :
• Cerveau autonome avec pensées spontanées
• Mémoire thermique vivante (37°C)  
• 30 accélérateurs Kyber persistants
• DeepSeek R1-0528-8B (dernier modèle)
• 1536 applications détectées
• Surveillance anti-crash
• Agent garde-fou actif

🎯 Interface disponible sur : http://localhost:3005" with title "✅ LOUNA AI - Prêt" buttons {"Parfait!"} default button "Parfait!"
        
    on error errorMessage
        display dialog "❌ Erreur lors du lancement de LOUNA AI :

" & errorMessage with title "Erreur LOUNA AI" buttons {"OK"} default button "OK"
    end try
end run
