#!/bin/bash

# Louna AI Launcher - Application Desktop CORRIGÉE
# <PERSON><PERSON><PERSON> par <PERSON>, Guadeloupe

# Configuration pour application desktop
LOUNA_DIR="/Applications/Louna AI.app/Contents/Resources"

echo "🚀 LANCEMENT APPLICATION DESKTOP LOUNA AI"
echo "=========================================="

# Fonction de nettoyage
cleanup() {
    echo "🧹 Nettoyage des processus existants..."
    pkill -f "node.*server.*louna\|node.*main.js" 2>/dev/null || true
    lsof -ti:52796 | xargs kill -9 2>/dev/null || true
}

# Fonction de démarrage
start_louna_desktop() {
    cd "$LOUNA_DIR" || {
        echo "❌ Impossible d'accéder au répertoire: $LOUNA_DIR"
        exit 1
    }

    echo "📍 Répertoire: $(pwd)"

    # Vérifier les fichiers essentiels
    if [[ ! -f "main.js" ]]; then
        echo "❌ main.js manquant"
        osascript -e 'display alert "Erreur" message "main.js manquant dans l application Louna AI" as critical'
        exit 1
    fi

    if [[ ! -f "server.js" ]]; then
        echo "❌ server.js manquant"
        osascript -e 'display alert "Erreur" message "server.js manquant dans l application Louna AI" as critical'
        exit 1
    fi

    echo "✅ Fichiers essentiels trouvés"

    # Nettoyer les processus existants
    cleanup

    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js non installé"
        osascript -e 'display alert "Erreur" message "Node.js non installé" as critical'
        exit 1
    fi

    echo "✅ Node.js version: $(node --version)"

    # Afficher une notification de démarrage
    osascript -e 'display notification "Lancement de l application desktop..." with title "🧠 Louna AI"'
    
    echo "🚀 Lancement de l'application desktop..."
    
    # Lancer l'application desktop avec main.js
    node main.js &
    MAIN_PID=$!

    echo "📝 PID de l'application: $MAIN_PID"

    # Attendre un peu pour que l'application se lance
    sleep 5
    
    # Vérifier que l'application tourne
    if ps -p $MAIN_PID > /dev/null 2>&1; then
        echo "✅ Application desktop lancée avec succès !"
        osascript -e 'display notification "Louna AI Desktop est lancé !" with title "🧠 Louna AI"'
        
        # Attendre que l'application se termine
        wait $MAIN_PID
        echo "🛑 Application desktop arrêtée"
    else
        echo "❌ Échec du lancement de l'application"
        osascript -e 'display alert "Erreur" message "Impossible de lancer l application desktop Louna AI" as critical'
        exit 1
    fi
}

# Gestion des signaux
trap cleanup EXIT

# Point d'entrée
echo "🧠 Louna AI Desktop Launcher v2.0"
start_louna_desktop
