<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/app.asar</key>
		<data>
		qrQtRgVXdK/7pfAqOGcm3Juhb34=
		</data>
		<key>Resources/electron.icns</key>
		<data>
		Sv8+IaExyHw5CkFMG9tM1AfdxeI=
		</data>
		<key>Resources/iconDarkTemplate.png</key>
		<data>
		kO0n0mOUJ+GuEXovVZmUGZhsR80=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Epn/0suir7jGQcMLpnMvv1SsGIg=
		</data>
		<key>Resources/iconDarkUpdateTemplate.png</key>
		<data>
		3kIltg+HoOK/5H29pVjGdNz4dbI=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		GTlj5K2dzOaY13twI8lG1V8Wksw=
		</data>
		<key>Resources/iconTemplate.png</key>
		<data>
		EOUeEkeooa+3x6bbtHIwCAYNV+8=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		9u9A1RVp28ZEBmDfWD+I3qr4tAY=
		</data>
		<key>Resources/iconUpdateTemplate.png</key>
		<data>
		IWemNpNUB6nIRpTqvVPrQCIAuDU=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Av0B0p5zWJVvB4e0Sez0iAUNuQQ=
		</data>
		<key>Resources/libggml-base.dylib</key>
		<data>
		rTG1KukdgJZyOvqDN2ge5lxz6g0=
		</data>
		<key>Resources/libggml-cpu-alderlake.so</key>
		<data>
		v9/9MhqJsdyslYGe/ZThWxN6HpE=
		</data>
		<key>Resources/libggml-cpu-haswell.so</key>
		<data>
		1Y4r/gvxI+Xn194j1xRWDz8ciE4=
		</data>
		<key>Resources/libggml-cpu-icelake.so</key>
		<data>
		C/SBZkteZv4vS0SNjjU/1vFP0Jc=
		</data>
		<key>Resources/libggml-cpu-sandybridge.so</key>
		<data>
		Qwfu9Y5KGC8PDi+s3DWEL5XFXlg=
		</data>
		<key>Resources/libggml-cpu-skylakex.so</key>
		<data>
		LwP7Uf8epYDsfbfE6IzqZxXkK+o=
		</data>
		<key>Resources/libggml-cpu-sse42.so</key>
		<data>
		gJMY/Mcr66po+cUmQYXG5tZgww8=
		</data>
		<key>Resources/libggml-cpu-x64.so</key>
		<data>
		+WRbhmPLiY321vgbdU5SlUX5UQc=
		</data>
		<key>Resources/ollama</key>
		<data>
		zgccd13et6i0iv7Ph+nSkfo64jE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Electron Framework.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			MGtH2MrHf8SiAsclKJpnWb98BU0=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Electron.framework" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Mantle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			JqIoqzofi3sKfZde90YiD8W+PpM=
			</data>
			<key>requirement</key>
			<string>identifier "org.mantle.Mantle" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Ollama Helper (GPU).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			dCW0gex6QyKPCM4taq/a/VSdMfs=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.ollama.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Ollama Helper (Plugin).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			feXjlK+U2NE898lVfLx4LDX5GQo=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.ollama.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Ollama Helper (Renderer).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			qbPObte0ZGaJwoYXEKQAY/6BRz4=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.ollama.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Ollama Helper.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			7M2hlsdm5t8gWsRP9r+jmMotyx4=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.ollama.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/ReactiveObjC.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			8g0Dd0x+/tCqR1y7oVMCnCwBgPs=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.reactive" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Frameworks/Squirrel.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			wtx+1HTCbCkm055IX8Zetw7xBdQ=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Squirrel" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Resources/app.asar</key>
		<dict>
			<key>hash2</key>
			<data>
			MPm+E3DvKfC0Vpll5q7xr67nGqNejYmwWyqY4xPSVHI=
			</data>
		</dict>
		<key>Resources/electron.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			ABgti9kzZUZFaGFwyy0yV4AKf/sVs2SCmhWvpLSM0nI=
			</data>
		</dict>
		<key>Resources/iconDarkTemplate.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6/1Ppv+SEBbzP8e8kPlT2RFWqdzPTPINj4s4dWjY7Hk=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			I30Wx1Q/2iYeXzaIUkztEsM2isl7ndz/JDQTDdls0gU=
			</data>
		</dict>
		<key>Resources/iconDarkUpdateTemplate.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yQ7L/bm1+TZooZn3hCduOHFqcpc99qm/LWCqpQqSGKg=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			suu43fdYcG8FSCk/JbrqH8UxNxgB8C68yPiOrkguXP8=
			</data>
		</dict>
		<key>Resources/iconTemplate.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IlwrjJxtxt4J0RKNJatAYzmEK7EzVnbX0e+q54Qr87o=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			lQ3kyZ+ro6z8a+Puiz3tc0i1FCP0/Pkp1EqNIr2wp7E=
			</data>
		</dict>
		<key>Resources/iconUpdateTemplate.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PHfMJDv1WyKEUN0A5anskPaNP5fRQh/w+QUURoinrYg=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			LI7lQiCG5avEN1O+Ywio8vXinQHK8ANb8JWjV0bqXqU=
			</data>
		</dict>
		<key>Resources/libggml-base.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			R0vW1hosRriV/Wi9Cn+uZiu5xhgf/dfhoa52MIql1DE=
			</data>
		</dict>
		<key>Resources/libggml-cpu-alderlake.so</key>
		<dict>
			<key>hash2</key>
			<data>
			eMDaT7H5pxreUAzEr16hCcoznc5Qeo67IMffdzz3XTw=
			</data>
		</dict>
		<key>Resources/libggml-cpu-haswell.so</key>
		<dict>
			<key>hash2</key>
			<data>
			l/YtRTSPiac2WF69kk5q9+ZXGQli+ZpdbIVGoJ8Wkdw=
			</data>
		</dict>
		<key>Resources/libggml-cpu-icelake.so</key>
		<dict>
			<key>hash2</key>
			<data>
			+XPHM0n9CqkpIw/bona9ek1rGOZU8SZGd4tkg6m9UlQ=
			</data>
		</dict>
		<key>Resources/libggml-cpu-sandybridge.so</key>
		<dict>
			<key>hash2</key>
			<data>
			SEWQ47ZbFXni3aWaaI5buAP/pDkFQ0NRG1uU9Sgrj8g=
			</data>
		</dict>
		<key>Resources/libggml-cpu-skylakex.so</key>
		<dict>
			<key>hash2</key>
			<data>
			cpYvSia5cv69V6dNbkBDu+swMmWJ+MZatKndSpZInOM=
			</data>
		</dict>
		<key>Resources/libggml-cpu-sse42.so</key>
		<dict>
			<key>hash2</key>
			<data>
			GwBV+DPH1KfkdlerpBfrIsJOAPxjjb/fzpC6t8bW0lI=
			</data>
		</dict>
		<key>Resources/libggml-cpu-x64.so</key>
		<dict>
			<key>hash2</key>
			<data>
			BH5MJlz9trZMUqd0DlzflgnCZQeG3TkppZVFsa/MeAI=
			</data>
		</dict>
		<key>Resources/ollama</key>
		<dict>
			<key>hash2</key>
			<data>
			ct24v49X/agddGP5A37jnpepuioLuhBLjzvkYp9L/II=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
