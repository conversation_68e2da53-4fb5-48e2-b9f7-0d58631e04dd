<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleIdentifier</key>
    <string>com.electron.ollama.helper</string>
    <key>CFBundleName</key>
    <string>Ollama Helper (Plugin)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>22E245</string>
    <key>DTSDKName</key>
    <string>macosx13.3</string>
    <key>DTXcode</key>
    <string>1431</string>
    <key>DTXcodeBuild</key>
    <string>14E300c</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
    </dict>
    <key>LSUIElement</key>
    <true/>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>CFBundleDisplayName</key>
    <string>Ollama Helper (Plugin)</string>
    <key>CFBundleExecutable</key>
    <string>Ollama Helper (Plugin)</string>
    <key>CFBundleVersion</key>
    <string>0.9.0</string>
    <key>CFBundleShortVersionString</key>
    <string>0.9.0</string>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Resources/app.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>be1c36dc72d980dd2b55e78279be8e2b9ce0ce88338952c254fed635d0eea3ce</string>
      </dict>
    </dict>
  </dict>
</plist>