<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		a6+1dooWUvHjsd5xVXm6AYzGJ7k=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		kGuqJL15gkToanSQ/frSnS0EtAw=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uVs2DtJPZKax/WM9Wwr/tJDFJ70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			pbHsBOGs24LD55bySm8EET0KDpM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bJwhlB/i23hDFg4iVN1cdRefljQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			CnF9+680hnYTz+iitDBjkcB+XzA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			M6ycuzdaVi+KW7LWUa0RNKFocs0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			SdyBpnwfMMcmgHQshSllq6fBsqA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		gOD53fuEV8G4vAYB58w//hAWAZY=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		E8WsaUEUpjEZ1n9JfY5emN+WH9g=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			p9eOSZu6VeDrFxSlQEBYySQFNLY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			43e7IiMFM7UmpMbQivOA4rjaz9E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			0WxK4uAiBVKoddxsSBSn+3F/cLQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			JZEjk+rr3Uk5hMIz7eHzjNLW3kg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5DetCHpUm7YinDk+txXg60R40XI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bOGOkMtClYdekscYxanWZdEnTRA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			EvICm4JNIYUy1h6mTAXLor0q99k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			INgfA6GLMLCpWo9RCLJGEqs9g04=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zFDrnB3zljAoMRWy+QIkFG66vBU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Rf9wZsdRr283YAKo3VLU22FDIS0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			T5bZ1o+dnNAMqoT/LZkHJES3p0E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			JX3ST5xINn52wpR2J54LnQT1ONY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			QMpPGUNiHi1U+Q7LE4JTfUUIe4g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VXt2CwpFvWfIx3xZVWTBa3tENbA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			nifnm/O0JBotWqP4F1CKNDxHaXk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			iLj0LD8NRO5a6JEiB4l24woVcxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			aQp8G+0rhzSVBOy+GojghyDmnss=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			F6uG7jiaD3nzmrhrSUvezYT9mTo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		2+zy0ZOuV1q6QhcZTUE2vZKR1Ns=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8LcIJBJqRScHh9uhwlCGSPpQOHw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ubw+fgZ/koNmYpYrwWwzSjMYNzo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			7dGQYCKC8/Tm+6xODbe9RkidKy8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			g4JlbS7yPtdSsoTFoO1r14PEMWw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			yJCdR9AKmOSKTl9IERdamhi05nM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Ce0eWu6ZghMpD3p/X2WwKp1k/KY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			2T1Dv59bqElFEuDgP+x+af/FLiY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			usZ7j03qZKb8kZXDuZs0J502yNc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			suB9wupvSeJXF4c/5pGWwkoKBLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			PgwsfJa9Wb/xoBD2z+wdknEclhY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			peIMihJxxVczbx6eY2sgPQ75Omw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			qPDQtHgwb0rCJ0ZBeGNo7yByY3g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			UyZDbCecQ28Ii4ytu3QN+3V25+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AfAjD94vf86aFXUAn0VDBx07Q+I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			6qIcgg31Gt2CJko5b0bJdtY9QlM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		Vs4NcCuDIiVWyZ94Ww00DXZczXw=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ebV2ckG6J4125VupcJYy4UGhOG0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			6mvgH/FbLHDSP07rrEJE4lJlr/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1vTKRiAEhIaL4VZXvoQ9CSf1t9A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YTjjOvolqIQIZe92Kh7DlcWn0ow=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			CEGT/PiIevEJgjdomAFrKhdkRtk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ee7vsadPBMd/Y8R7HIvC3uwEXQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xhTAHxbn5nJbZxTkigFPbcnCEPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			vHSh62NKfLGrP2XPwK59sbCAbds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			D48c3xiXLWi3CNA321/FxoTYDyk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			fNW/WGxW+GmeaX50jrI68ThFNjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zhpuw+HIplB6OLvQtNwoGUFcYwA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/VIfanzuGXyi7/ATfYFqoZRO0+4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			f54Cg/XGi7DecqdcQaO8Uef+R1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<data>
		LKdBSQFCmy/CK1/A7EnwlMWiy0Q=
		</data>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<data>
		slgjg8cGXP3Tlm89i6O8yexs/jg=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			HauNwLX7aTzTsOJ7NeG9qyQtgi0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+8KOp7hTJW4oAR4thYEO9ti8y/k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			a/qTzoz1urh5COUn9Yh6zHppEBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			aNZ6FyqXI8r1nZxi+5oLtKQtWMM=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "3MU9H2V9Y9"</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			j5pnDO3+P8Y0g0NMxhRUFvMGyUBvJhXPJ7R5q7kGshA=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			2kp3mb88JhO1HmjHcEM7ORToEaiaQ52E85QWoPKuGQw=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			IQLR8zrgRwOGKT5TxnlcBRX0iaP7wWzMzkdGU0L+01U=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			QP/eramMe2hRd+0m/WIPgoaxm/lGKJGbvsyVNm5YAVg=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AUY4HxVZbzekkJc3KrenAyOEUq8LK/LqL6/LNHGGahw=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pdL4G86pddwplV8OvauLD6Gpkqye9P27g6JLvgxDeAM=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6qRkOXa9SDpQTlqEaPYuj++qg8UWb0bToCpz6LQcpuw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			7SxEOimqAqzJ2OZe5RYtadVnnsVtn5VJFgNBWa8kkRQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VPoDMon1Irs8Qa97L7RLtcPlLyJWMxwqh2iiBTOhI5o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ldoDfQ6/WRA92NThwa254XYN0B86bSe9H3mjavTGw14=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Isaj7mHpwMUiFR/1glnUZazqweP5IkDIkZQy6tSXIDI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0qQvIiwWcOudeu+FXvBMVr+zpgySJy8jqdt4XE+2vuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			4xx3DYk/f7RX9rvpeRYglqIUd1t1X2VXjapOi1n2pdw=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WWcnUMpSo8gQgGIxT8bZ11grVQcKPPXjiN8rFcHqz/k=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHKYSFVAVVKryWe0IqYK3tVrI+NhVjy/ym6p1WvVNfY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2eiGS+3evjKZbq2tWu2PR8whNkpuW2pHTj+Hos2Ih/A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			m/q00Fg44W5yVLDRnEw8DizHJq+n91cGF8u7R2KfMVs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HVOeWPIfM/4EbsZbD7yrGdpCXCgYxGFPWCKiTPChqZ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DaQJ4YtykIApbVL0MbnevohDzn75qiNFILtfJEFT6ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8kLx/yvtgYJ4j8ZOtlUqjIvwCvWEmEas5VowjC6L6Pw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			zQMhXhkhfq4XKl859QYdfrTJYkZ1SLW4+tQgvfYixC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			bx2Dz2tuKReWJE80ZpYhOikM3H+gL1iA5Bsgz9qefM8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DM6aA4XDjRdJ590coIDhLKnGmf/6ibZXzoIpLc6UrBg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			df0Strg82rlV6aF0XQk+CNAJFwYzIo6q1JgvTKmgCt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			eymDRVmJyvVtomMV7SFiY6Vfdd6Av37thf8V4pXNzME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1FcvSJMPUUqDKRvCCVGfHt4p9i1sXHB591tepg29Quo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			UZfluDHv0nktvGHbBGskRiSzak96faK5p15D05AZh3E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9eVC1s9YB8CbVnklWy9vc8Jw+iPuq5VzJtXDjmK8ZW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			F/qWtJbFxKG/07rBtorRx0W2Ejzn2KwavYLMg/ObWac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			F1uAopzpeKXZ6dKLOPh5v4LoR4z5CKANbP0HdP7reyc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mNE/pvc06zqrhDh+T+Q9RGUjRSFndJiDQMyAHYXTI/Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xnCobuPzR/xQKSuHUrJOgducJWtTX9EeyjLx+vVh3ko=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			7jyIg+/9kO37D/W3WMVgy8ol0VmPy1W4DvZ+mQ3RnUE=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			wiZpPfdLYFECAwFKfmhUQdRAtfmpvO1/9dLT39bRu80=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			+caLS55eFxkVUwV0D88Ub3SyLuSH7izBKY/7XKos624=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			40haZ0eYBXtxHSGi0bYOcr/JpJtG7U2RHbS6iHYoovc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ilbe84j0ND+WS5NUDXIwc24P1epvoAGgIlt/cwhi1q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2QNezzEGtgiMrMorl2n9FPB4qdQN22qBIgFias5zwDE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			goRBf6ZkpSZmlOvrzIIrShPmAkQlbDkFTklW6y9V/Nc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			v0HJ5V6J1TtHqb3tE3f57CIlMsvS/r4Y7mJxB0ylpSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1X4ACsiRymGjOZ1qr9kPDkQvWGQngBLUeM+0OIVLEi8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			JCyMnd2RLrg+bJFtSVcufrd51kynup24/Hq349NQAiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Pv47oinsmItXr5ILFdgYwg+3vwwZohzlBIPxVoHjnjM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HGvdVGzYyLb5Hw3xD8T6iAfnoYFFQnIFn3OSE4pZa6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			IYlEiuWcuazOp3w4rtElMaghDC/1oJqSP+uTA9sAYy4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PEQAXUvGyoLfy9ZOAdeoy/ifDPnYSOxt+KQO1VF5FrQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			qTZTDmPIx3lb2sikgkbwkOBQpb/dFBJNREU3IKj5NlQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XcVjJ1gNA2iTzwCHIYhxmMTlEjZr4BIwB4r642BJGtI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			L5ukoig1o/PtEIEuhogJx2kKOEsxoPidtZpApB8vdNI=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GiHVzrZ5Vnl4ZY5QXfpRwgUqwbsGDIfHR+lnpAAHaxA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9M76305UmfrEhjjATFjUGFINQ5QL7l/xdW3kKNHcbm8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ydCHMDIf3PrwvpjiGp2W3KGM32bNrJKNpr9n2SQGfgg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			QvjyO13XciGVVi+eOZpNDqWHE53RJYLKvNP9E2QP5ks=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0wLz8Nlfx4E0MxBt3LqEEujtYv6qdo52oYY106Ta1/Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			hSbaGLdWda4P2L/aetjJV9N+FxM4WVfEJnlYDryOZ24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XDFIN8LtgyBsV56Jt54ckgnxfIx5kFhE80NSXFCmM+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8LDIFd36cszSYCFaTXxxkbpjKBAGk92Ua4QukIAmnzU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			OWs7KYNERAim+BbomQq6PERHh79zDElxMppue/LPtuI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2lm/muBQGARAktuGZaZooWFFnCwaDodoX2oih3ZlC34=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ya6EVUfwHCBStuYE98bp90194f7Ln0qpwJ4gsXnvIa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mWiahWayOXPkQQzNINBrXI2kmdTnM5Br0g8HWKHq1Us=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			T9Z8TMu+yQF1NMAtzTSPOu0ai1bgRy8UjxKbB+sHTFI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			BPBcqvIHbVq2YpDLA46BiiRPmnCSqYiJqaUP0C3wXqY=
			</data>
		</dict>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			oKNNP9dcCypLdDl0H+DMynHBcVBYsJ3QtDhijjCDHFs=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			04LLQeQ3fdLLcgrKI4ZMdQ+gGN1DH/EayvsF3+z2M2s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			67YGtwymCImCld3UA3X9xP0hevv9J5mOuQ+u+OIuIe4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HyaFUlTK3A7M41/4rzw40UfEMY4HNRnrsHVQhWMy1Hk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
