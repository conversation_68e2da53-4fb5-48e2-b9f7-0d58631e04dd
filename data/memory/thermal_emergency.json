{"timestamp": "2025-06-08T20:41:05.552Z", "entries": [{"id": "thermal_1749415236392_dh3t0jwrx", "type": "training_session", "data": "Formation: Algorithmes et Structures de Données - // 🧠 ALGORITHMES AVANCÉS // 1. Tri Rapide (QuickSort) function quickSort(arr) { if (arr.lengt...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9999190027777778, "timestamp": "2025-06-08T20:40:36.392Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:36.392Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415172343_sc3q5bz9a", "type": "training_session", "data": "Formation: React Avancé - // ⚛️ REACT PATTERNS AVANCÉS // 1. Custom Hooks function useLocalStorage(key, initialValue) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9995655593472073, "timestamp": "2025-06-08T20:39:32.343Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415142342_2qidj215k", "type": "training_session", "data": "Formation: Algorithmes et Structures de Données - // 🧠 ALGORITHMES AVANCÉS // 1. Tri Rapide (QuickSort) function quickSort(arr) { if (arr.lengt...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9993169407004785, "timestamp": "2025-06-08T20:39:02.342Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415142328_u7ztlazg9", "type": "training_session", "data": "Formation: React Avancé - // ⚛️ REACT PATTERNS AVANCÉS // 1. Custom Hooks function useLocalStorage(key, initialValue) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9993168240869486, "timestamp": "2025-06-08T20:39:02.328Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415082326_jmmqjud98", "type": "training_session", "data": "Formation: React Avancé - // ⚛️ REACT PATTERNS AVANCÉS // 1. Custom Hooks function useLocalStorage(key, initialValue) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9775324685599522, "timestamp": "2025-06-08T20:38:02.326Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415172329_xv6r3fbwl", "type": "training_session", "data": "Formation: JavaScript Avancé - // 🚀 TECHNIQUES JAVASCRIPT AVANCÉES // 1. Closures et Scope function createCounter() { let co...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9718659375710825, "timestamp": "2025-06-08T20:39:32.329Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415082340_6q7ciaat6", "type": "training_session", "data": "Formation: React Avancé - // ⚛️ REACT PATTERNS AVANCÉS // 1. Custom Hooks function useLocalStorage(key, initialValue) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9615429223541089, "timestamp": "2025-06-08T20:38:02.340Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415112328_rms85z7oh", "type": "training_session", "data": "Formation: Réseaux de Neurones - // 🧠 RÉSEAU DE NEURONES SIMPLE class NeuralNetwork { constructor(inputSize, hiddenSize, outputSize) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9466966271586303, "timestamp": "2025-06-08T20:38:32.328Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415236405_dnv3lpmb7", "type": "training_session", "data": "Formation: JavaScript Avancé - // 🚀 TECHNIQUES JAVASCRIPT AVANCÉES // 1. Closures et Scope function createCounter() { let co...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9437629280389936, "timestamp": "2025-06-08T20:40:36.406Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:36.406Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415112341_3i1eewxmt", "type": "training_session", "data": "Formation: React Avancé - // ⚛️ REACT PATTERNS AVANCÉS // 1. Custom Hooks function useLocalStorage(key, initialValue) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9407721772147698, "timestamp": "2025-06-08T20:38:32.341Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415191880_xeo48fiwj", "type": "neuron_activity", "data": "Activité neuronale: 207 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8970981084519308, "timestamp": "2025-06-08T20:39:51.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415172327_65azvswhr", "type": "skill_evaluation", "data": "Évaluation: 7 compétences, niveau moyen 7.9%", "importance": 0.8, "category": "ai_assessment", "temperature": 0.8956311779822735, "timestamp": "2025-06-08T20:39:32.327Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "procedural", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415061897_ftx682uy0", "type": "neuron_activity", "data": "Activité neuronale: 101 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.865248537712547, "timestamp": "2025-06-08T20:37:41.897Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415255556_4w4ffdzcm", "type": "neuron_activity", "data": "Activité neuronale: 125 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8641384798107546, "timestamp": "2025-06-08T20:40:55.556Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:55.556Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415131880_znvgy6z7g", "type": "neuron_activity", "data": "Activité neuronale: 155 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.860732484878314, "timestamp": "2025-06-08T20:38:51.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415121880_9bl2jxkyn", "type": "neuron_activity", "data": "Activité neuronale: 145 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8596978970358174, "timestamp": "2025-06-08T20:38:41.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415245554_mcpqbxsto", "type": "neuron_activity", "data": "Activité neuronale: 115 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8338016725765761, "timestamp": "2025-06-08T20:40:45.554Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:45.554Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415172341_v1o2f1q0d", "type": "skill_evaluation", "data": "Évaluation: 8 compétences, niveau moyen 7.5%", "importance": 0.8, "category": "ai_assessment", "temperature": 0.8325082709572896, "timestamp": "2025-06-08T20:39:32.341Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "procedural", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415151880_azm6iig5v", "type": "conversation", "data": "Discussion avec <PERSON><PERSON><PERSON> à 4:39:11 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.8213660048135196, "timestamp": "2025-06-08T20:39:11.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415141880_wf9sie9cw", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:39:01 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.8194570929691991, "timestamp": "2025-06-08T20:39:01.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415161880_5k6el6rib", "type": "conversation", "data": "Discussion avec <PERSON><PERSON><PERSON> à 4:39:21 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.8122703905854095, "timestamp": "2025-06-08T20:39:21.880Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415225552_dd297d8je", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:40:25 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.7961408142832308, "timestamp": "2025-06-08T20:40:25.552Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:25.552Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415235552_76e5rm0br", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:40:35 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.78479518470509, "timestamp": "2025-06-08T20:40:35.552Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:35.552Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415091877_b5awtl0gw", "type": "conversation", "data": "Discussion avec <PERSON><PERSON><PERSON> à 4:38:11 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.7720025538300093, "timestamp": "2025-06-08T20:38:11.877Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415071875_bz4uxs25z", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:37:51 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.7353717464171189, "timestamp": "2025-06-08T20:37:51.875Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415101878_xvxw7qijv", "type": "learning", "data": "Apprentissage automatique: compression 97.11965393144916% active", "importance": 0.6, "category": "ai_learning", "temperature": 0.7068325321045101, "timestamp": "2025-06-08T20:38:21.878Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.6, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.48, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415081876_zckapezvw", "type": "system_event", "data": "Température système: 33.9°C - 1321 entrées stockées", "importance": 0.5, "category": "monitoring", "temperature": 0.664329972418732, "timestamp": "2025-06-08T20:38:01.876Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "episodic", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.5, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.4, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415171881_mxe3z0hts", "type": "system_event", "data": "Température système: 34.2°C - 1336 entrées stockées", "importance": 0.5, "category": "monitoring", "temperature": 0.6633125992953871, "timestamp": "2025-06-08T20:39:31.881Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "episodic", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.5, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.4, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415111879_ckty2kkon", "type": "system_event", "data": "Température système: 34.0°C - 1326 entrées stockées", "importance": 0.5, "category": "monitoring", "temperature": 0.6426714810785407, "timestamp": "2025-06-08T20:38:31.879Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "episodic", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.5, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.4, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415215562_n7dkgg4kp", "type": "system_event", "data": "Température système: 36.4°C - 1343 entrées stockées", "importance": 0.5, "category": "monitoring", "temperature": 0.5874437966239721, "timestamp": "2025-06-08T20:40:15.562Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "episodic", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:40:15.562Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.5, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.4, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749415181882_pd7ms2dxk", "type": "system_event", "data": "Température système: 37.0°C - 1341 entrées stockées", "importance": 0.5, "category": "monitoring", "temperature": 0.5868736611285323, "timestamp": "2025-06-08T20:39:41.882Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "episodic", "transferHistory": "[Circular Reference]", "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.5, "ltpLevel": 0, "engramConnections": "[Circular Reference]", "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.4, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}], "temperature": 34.16846819683144, "neurons": 135}