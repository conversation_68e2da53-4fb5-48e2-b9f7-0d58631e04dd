{"timestamp": "2025-06-08T20:35:24.964Z", "entries": [{"id": "thermal_1749414895439_lnhhipe95", "type": "training_session", "data": "Formation: JavaScript Avancé - // 🚀 TECHNIQUES JAVASCRIPT AVANCÉES // 1. Closures et Scope function createCounter() { let co...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9966844916973946, "timestamp": "2025-06-08T20:34:55.439Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:34:55.439Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414895425_21m0lzh52", "type": "training_session", "data": "Formation: Réseaux de Neurones - // 🧠 RÉSEAU DE NEURONES SIMPLE class NeuralNetwork { constructor(inputSize, hiddenSize, outputSize) { ...", "importance": 0.9, "category": "ai_learning", "temperature": 0.9483227769225668, "timestamp": "2025-06-08T20:34:55.425Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:34:55.425Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "maximum", "synapticStrength": 0.9, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.7200000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414884963_owzcfm25k", "type": "neuron_activity", "data": "Activité neuronale: 101 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8580230870061635, "timestamp": "2025-06-08T20:34:44.963Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:34:44.963Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414904965_fivi2kze5", "type": "neuron_activity", "data": "Activité neuronale: 109 neurones actifs", "importance": 0.8, "category": "brain_activity", "temperature": 0.8223853714979296, "timestamp": "2025-06-08T20:35:04.965Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:35:04.965Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.8, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.6400000000000001, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414894964_wjf31m0o2", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:34:54 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.8177231189071351, "timestamp": "2025-06-08T20:34:54.964Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:34:54.964Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414874972_173xp0e1r", "type": "conversation", "data": "Discussion avec <PERSON><PERSON>Luc à 4:34:34 PM sur l'amélioration de la mémoire thermique", "importance": 0.7, "category": "user_interaction", "temperature": 0.746731007148353, "timestamp": "2025-06-08T20:34:34.972Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:34:34.972Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "high", "synapticStrength": 0.7, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.5599999999999999, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}, {"id": "thermal_1749414914966_foob3c3aj", "type": "learning", "data": "Apprentissage automatique: compression 96.65864692266709% active", "importance": 0.6, "category": "ai_learning", "temperature": 0.7052226602501411, "timestamp": "2025-06-08T20:35:14.966Z", "accessCount": 0, "lastAccessed": null, "fluidTransfer": true, "autoCompression": true, "streamingMode": true, "acceleratedAccess": true, "compressionRatio": null, "memoryZone": "zone3_working", "memoryType": "working", "transferHistory": [{"zone": "zone3_working", "timestamp": "2025-06-08T20:35:14.966Z", "action": "stored"}], "compressionLevel": 9, "securityLevel": "medium", "synapticStrength": 0.6, "ltpLevel": 0, "engramConnections": [], "consolidationStatus": "hippocampal", "replayCount": 0, "interferenceResistance": 0.48, "accelerationBoost": 1, "transferSpeed": "ultra_fast", "cascadeProcessed": true}], "temperature": 34.183523728099104, "neurons": 128}