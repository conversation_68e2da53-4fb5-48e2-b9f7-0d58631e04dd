#!/bin/bash

echo "🖥️ LANCEMENT APPLICATION DESKTOP LOUNA AI"
echo "=========================================="

# Démarrer le serveur en arrière-plan
echo "🚀 Démarrage du serveur backend..."
node server.js &
SERVER_PID=$!

# Attendre que le serveur soit prêt
echo "⏳ Attente du serveur..."
sleep 5

# Vérifier si le serveur fonctionne
if curl -s http://localhost:52796 > /dev/null; then
    echo "✅ Serveur backend actif sur le port 52796"
else
    echo "❌ Serveur non accessible, tentative de redémarrage..."
    kill $SERVER_PID 2>/dev/null
    node server.js &
    SERVER_PID=$!
    sleep 3
fi

# Lancer l'application en mode desktop avec Chrome
echo "🖥️ Lancement de l'application desktop..."

# Essayer différentes commandes selon le système
if command -v open >/dev/null 2>&1; then
    # macOS - Lancer Chrome en mode app
    echo "🍎 Système macOS détecté"
    open -a "Google Chrome" --args --app="http://localhost:52796" --new-window --disable-web-security --allow-running-insecure-content --window-size=1400,900 --window-position=100,100
    
    if [ $? -ne 0 ]; then
        echo "⚠️ Chrome non trouvé, essai avec Safari..."
        open -a Safari "http://localhost:52796"
        
        if [ $? -ne 0 ]; then
            echo "⚠️ Safari non trouvé, ouverture avec navigateur par défaut..."
            open "http://localhost:52796"
        fi
    fi
else
    echo "🐧 Système Linux/Unix détecté"
    # Linux - Essayer différents navigateurs
    if command -v google-chrome >/dev/null 2>&1; then
        google-chrome --app="http://localhost:52796" --new-window &
    elif command -v chromium-browser >/dev/null 2>&1; then
        chromium-browser --app="http://localhost:52796" --new-window &
    elif command -v firefox >/dev/null 2>&1; then
        firefox "http://localhost:52796" &
    else
        xdg-open "http://localhost:52796" &
    fi
fi

echo "✅ Application desktop LOUNA AI lancée !"
echo ""
echo "🧠 LOUNA AI - APPLICATION DESKTOP ACTIVE"
echo "========================================"
echo "🌐 URL: http://localhost:52796"
echo "🧠 Cerveau autonome avec neurogenèse"
echo "🌡️ Mémoire thermique vivante"
echo "🎓 Formation avancée active"
echo "🖥️ Interface desktop opérationnelle"
echo "========================================"
echo ""
echo "💡 Pour arrêter l'application, fermez la fenêtre et appuyez sur Ctrl+C ici"

# Fonction de nettoyage
cleanup() {
    echo ""
    echo "🛑 Arrêt de LOUNA AI..."
    kill $SERVER_PID 2>/dev/null
    echo "✅ Serveur arrêté"
    exit 0
}

# Capturer les signaux d'arrêt
trap cleanup SIGINT SIGTERM

# Garder le script actif
echo "🔄 Application en cours d'exécution... (Ctrl+C pour arrêter)"
wait $SERVER_PID
