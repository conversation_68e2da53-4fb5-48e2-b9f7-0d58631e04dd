#!/bin/bash

echo "🚀 REDÉMARRAGE ULTRA-RAPIDE DE LOUNA AI"

# Tuer tous les processus existants
echo "⚡ Arrêt des processus existants..."
pkill -f "node.*server"
pkill -f ollama
lsof -ti:3005 | xargs kill -9 2>/dev/null
lsof -ti:11434 | xargs kill -9 2>/dev/null

# Attendre un peu
sleep 2

# Redémarrer Ollama en mode ultra-rapide
echo "⚡ Démarrage Ollama ultra-rapide..."
OLLAMA_FLASH_ATTENTION=1 OLLAMA_FAST_MODE=1 OLLAMA_LOW_LATENCY=1 OLLAMA_KEEP_ALIVE=-1 ollama serve &

# Attendre qu'Ollama soit prêt
sleep 3

# Redémarrer le serveur
echo "⚡ Démarrage serveur ultra-rapide..."
cd /Volumes/seagate/LOUNA-AI-VIVANTE
node server.js &

echo "🚀 Redémarrage ultra-rapide terminé !"
echo "⚡ Serveur disponible sur http://localhost:3005"
echo "⚡ Mode dialogue instantané activé"
