/**
 * 🧪 SERVEUR DE TEST ULTRA-SIMPLE
 */

console.log('🧪 Test 1: Démarrage...');

try {
    console.log('🧪 Test 2: Import express...');
    const express = require('express');
    
    console.log('🧪 Test 3: Création app...');
    const app = express();
    
    console.log('🧪 Test 4: Configuration port...');
    const PORT = 52796;
    
    console.log('🧪 Test 5: Route de test...');
    app.get('/', (req, res) => {
        res.send('LOUNA AI Test OK !');
    });
    
    console.log('🧪 Test 6: Démarrage serveur...');
    app.listen(PORT, () => {
        console.log(`✅ SERVEUR TEST OK SUR PORT ${PORT}`);
        console.log(`🌐 URL: http://localhost:${PORT}`);
    });
    
} catch (error) {
    console.error('❌ ERREUR TEST:', error);
    console.error('Stack:', error.stack);
}
