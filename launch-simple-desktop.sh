#!/bin/bash

echo "🖥️ LANCEMENT SIMPLE APPLICATION DESKTOP LOUNA AI"
echo "================================================"

# Démarrer le serveur en arrière-plan (sans attendre)
echo "🚀 Démarrage du serveur backend..."
nohup node server.js > server.log 2>&1 &
SERVER_PID=$!
echo "📝 Serveur PID: $SERVER_PID"

# Attendre un peu
echo "⏳ Attente de 3 secondes..."
sleep 3

# Lancer immédiatement l'application desktop
echo "🖥️ Lancement de l'application desktop..."

# Lancer Chrome en mode app (macOS)
echo "🍎 Lancement avec Chrome en mode application..."
open -a "Google Chrome" --args \
    --app="http://localhost:52796" \
    --new-window \
    --disable-web-security \
    --allow-running-insecure-content \
    --window-size=1400,900 \
    --window-position=100,100 \
    --user-data-dir="/tmp/louna-chrome-profile"

if [ $? -eq 0 ]; then
    echo "✅ Application desktop LOUNA AI lancée avec Chrome !"
else
    echo "⚠️ Chrome non disponible, essai avec Safari..."
    open -a Safari "http://localhost:52796"
    
    if [ $? -eq 0 ]; then
        echo "✅ Application desktop LOUNA AI lancée avec Safari !"
    else
        echo "⚠️ Safari non disponible, ouverture avec navigateur par défaut..."
        open "http://localhost:52796"
        echo "✅ Application desktop LOUNA AI lancée !"
    fi
fi

echo ""
echo "🧠 LOUNA AI - APPLICATION DESKTOP ACTIVE"
echo "========================================"
echo "🌐 URL: http://localhost:52796"
echo "📝 Logs serveur: server.log"
echo "🔧 PID serveur: $SERVER_PID"
echo "🖥️ Interface desktop opérationnelle"
echo "========================================"
echo ""
echo "💡 L'application desktop est maintenant lancée !"
echo "💡 Le serveur continue en arrière-plan"
echo "💡 Pour arrêter le serveur: kill $SERVER_PID"

# Sauvegarder le PID pour pouvoir l'arrêter plus tard
echo $SERVER_PID > louna-server.pid
echo "💾 PID sauvegardé dans louna-server.pid"
